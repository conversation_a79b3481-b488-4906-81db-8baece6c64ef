**Business Requirement Document (BRD)** **Module:** Fee Management **Product:** Modular School ERP (India-first SaaS) **Purpose:** Define a fully customizable, scalable, and extensible fee engine that supports diverse Indian school billing structures and enables real-world implementation across coaching centers, colleges, and K-12 schools.

---

### I. Objective

Build a world-class, flexible fee management system that supports dynamic fee heads, templates, concessions, receipts, and multiple collection modes. Design for configurability, extensibility, and robustness while maintaining simplicity for budget and premium institutions alike.

---

### II. Core Business Goals

- Create dynamic fee heads and fee templates per school/class/year.
- Apply fee structures to students with override options.
- Support partial payments, discounts, and refunds.
- Handle manual and online fee collection.
- Provide role-specific reports and receipt generation.

---

### III. Functional Features

#### 1. Fee Heads

- School-defined: Tuition, Admission, Lab, Transport, Exam, etc.
- Fields: `is_recurring`, `frequency`, `is_discountable`, `account_code`

#### 2. Base Default Template

- System provides a predefined **default fee structure template** (editable & copyable)
- Admin can duplicate and customize per class/year
- Useful for faster setup during onboarding

#### 3. Fee Templates

- Created per school → class → section → academic year
- Grouped fee heads with fixed amounts
- Option to create one-time or recurring term templates

#### 4. Student Fee Mapping

- Students mapped automatically based on class/section
- Manual override allowed per student
- Store individual structure in JSONB if customized

#### 5. Concessions / Discounts

- Sibling discount, Staff child waiver, Scholarships
- Manual or rule-based
- Approver logic (admin/accountant)

#### 6. Fee Collection

- Manual payment: Cash, Cheque, DD, UPI
- Online Payment (Razorpay/Stripe integration – future)
- Part payments supported
- Receipt generation with notes

#### 7. Refunds

- Link to original transaction
- Reason logging + approval
- Audit + reversal logic (do not delete original)

#### 8. Receipt Management

- PDF download
- Print-friendly version
- Custom school logo, receipt notes, terms

#### 9. Reporting

- Outstanding dues
- Collection by date/class/student
- Defaulters report
- Export to Excel/PDF

---

### IV. Data Models (Simplified)

```text
fee_heads
- id, school_id, name, is_recurring, frequency, is_discountable, account_code

fee_templates
- id, school_id, class_id, section_id, academic_year_id, total_amount

fee_template_items
- fee_template_id, fee_head_id, amount

student_fee_mapping
- student_id, template_id, custom_fee_structure (JSONB), concessions_applied (JSONB)

fee_transactions
- id, student_id, amount, mode, date, remarks, collected_by

fee_receipts
- id, receipt_no, transaction_id, print_count, file_url, issued_by

fee_discounts
- id, student_id, fee_head_id, discount_type, discount_value, approved_by, reason

fee_refunds
- id, original_transaction_id, refunded_by, amount, reason, date
```

---

### V. API Contracts (Examples)

- `POST /fee-heads` → Create fee head
- `POST /fee-templates` → Create fee structure for class/year
- `GET /fee-preview/:student_id` → Fetch payable amount + discount
- `POST /collect-fee` → Submit transaction & auto-generate receipt
- `GET /fee-report?class=X` → Outstanding/paid report

---

### VI. Frontend UX Flow

- Admin Panel:
  - Fee Heads Setup → Create/Edit/Delete
  - Template Builder → Assign heads + amounts
  - Student Mapping → View mapped structure, preview
  - Concession Manager → Add/approve/view
  - Collection Page → Quick entry by roll no / class
  - Receipt Manager → Search, reprint
- Reports Page → Filters for month, year, class, dues

---

### VII. RBAC Access Matrix

| Role       | Access                            |
| ---------- | --------------------------------- |
| Admin      | Full                              |
| Accountant | Templates, Collection, Refunds    |
| Teacher    | Read-only (optional)              |
| Parent     | View dues, receipts, pay (future) |
| Student    | Same as Parent                    |

---

### VIII. Caching Strategy

- Cache templates per school/class/year
- Redis keys:
  - `erp:<school_id>:fee_template:<class_id>:<year>`
  - `erp:<school_id>:student_fee:<student_id>`
- Invalidate cache on template or mapping update

---

### IX. Extensibility Notes

- Auto-scheduler for term fee reminders
- Online Payment Gateway support (Razorpay/Stripe)
- Multi-currency support (future)
- GST/TDS logic for CA/audit readiness
- Region-specific account codes
- Late fine auto calculation via rules
- AI predictions (cash flow, defaulters)

---

### X. AI Agent Development Rules

1. Always scope data to `school_id` and `academic_year_id`
2. Validate fee amounts and payment modes
3. Prevent backdated or duplicate collection
4. Immutability: never delete past transactions — use refund/reversal
5. Cache resolved fee templates and fee mappings
6. Use JSONB only for overrides, not core structure
7. Maintain audit logs for all actions
8. Modularize collection logic → service layer for future payment gateway reuse
9. Keep fee head logic separate from accounting if integrating with finance later
10. Prevent student-level editing unless explicitly overridden by admin

---

**End of BRD: Fee Management**

