**Business Requirement Document (BRD)** **Module:** Licensing & Feature Access Control **Product:** Modular School ERP (India-first SaaS) **Purpose:** Define the licensing system that governs feature access, subscription plans, usage enforcement, trial handling, and SaaS monetization structure.

---

### I. Objective

Design a flexible, scalable licensing system that maps feature access to pricing plans per school. The system must support onboarding trials, plan upgrades, usage limits, and enforcement logic, all scoped per school or organization.

---

### II. Core Business Goals

- Support tiered plans (Basic, Pro, Enterprise)
- Handle school trial period with auto-expiry
- Limit access to modules via feature codes
- Enable future billing integration (Stripe, Razorpay)
- Allow internal control panel for ERP operator to manage plans/licenses

---

### III. Functional Features

#### 1. Plan Management (SaaS Operator Scope)

- Define/edit plans: name, price, billing cycle
- Assign modules/features to each plan
- Manage allowed limits: students, teachers, storage, SMS credits

#### 2. School Licensing

- `school_licenses` table: maps school to plan, with dates
- Fields:
  - `plan_id`, `school_id`
  - `start_date`, `end_date`
  - `is_trial`, `status` (active, expired, grace)
  - `created_by`, `notes`

#### 3. Feature Flag System

- `features` table → `code`, `module`, `description`
- `plan_features` table → links features to plans
- Optional: JSONB override field in `school_licenses` to enable/disable at school level
- Evaluated at runtime by RBAC + UI layer

#### 4. Trial Management

- Every new school gets default plan (trial)
- Duration: e.g., 14 days
- Automatic expiration logic
- Optional grace period: allow limited read-only use

#### 5. Enforcement & Middleware

- Every protected API should:
  - Verify active license for school
  - Check if module's feature code is allowed
  - Return 403 if not permitted
- Auto-hide UI modules not licensed

#### 6. Notification Triggers

- Notify via email/app:
  - 7 days before expiry
  - On expiration
  - When feature limit is exceeded

#### 7. Internal Admin Panel (ERP Operator View)

- View all schools and their current license
- Activate/deactivate licenses manually
- Create/edit plans and features
- Assign/downgrade school plans
- View school usage metrics (future)

#### 8. Custom Plan Handling & Overrides

- `enabled_features (JSONB)` field in `school_licenses` supports per-school feature control
- Can manually enable or disable any feature regardless of plan
- Use case examples:
  - Enable LMS for a Basic plan school
  - Disable Transport for a Pro plan school
- Merged at runtime with base plan features to determine final access
- Useful for migrations, promotions, custom enterprise clients

#### 9. Future: Payment & Billing Integration

- Razorpay or Stripe webhook for plan upgrade
- Auto-renewal options
- Plan invoices & transaction logs

---

### IV. Data Models (Simplified)

```text
pricing_plans
- id, name, price, billing_cycle, is_trial_available, max_students, max_staff, description

features
- id, code, module, description

plan_features
- plan_id, feature_id

school_licenses
- id, school_id, plan_id, start_date, end_date, is_trial, status, enabled_features (JSONB)
```

---

### V. API Contracts (Examples)

- `GET /plans` → List all plans (internal use)
- `POST /plans` → Create/edit plan (admin only)
- `GET /school-license` → Get license for current school
- `POST /assign-plan` → Assign plan to school (internal only)
- `GET /feature-checks` → UI checks which features are active

---

### VI. Frontend UX Flow

- Admin Panel:
  - View License → Plan name, start-end date, active modules
  - Show trial countdown if applicable
  - Auto-hide non-licensed menu items/modules
- Internal Admin Panel:
  - Plan Manager
  - Assign/Modify School Plans
  - Feature-Plan Matrix (grid view)

---

### VII. RBAC Integration

- Each permission is bound to a `feature_code`
- RBAC engine first checks: `is_feature_enabled(school_id, feature_code)`
- Only then applies permission logic
- Redis cache for quick feature lookup

---

### VIII. Caching Strategy

- Cache `school_licenses` and resolved feature set
- Key: `erp:<school_id>:license:features`
- Invalidate on license/plan change

---

### IX. Extensibility Notes

- Support per-org license (applied to all branches)
- Future: Soft-limits (warn only) vs hard-limits (block)
- Multi-region pricing packs (for international rollout)
- Add-on based billing (SMS, storage, staff count overage)

---

### X. AI Agent Development Rules

1. Always validate plan and license before showing protected content
2. Don't hardcode features — use `feature_code` logic
3. Invalidate cache on license update
4. Respect read-only mode for expired plans (if grace is active)
5. Modularize feature-check utility: `is_feature_enabled(school_id, feature_code)`
6. Auto-assign default trial on school registration
7. Return proper 403/plan\_expired response for blocked APIs
8. Maintain all internal controls in protected `/internal-admin` routes only

---

**End of BRD: Licensing & Feature Access Control**

