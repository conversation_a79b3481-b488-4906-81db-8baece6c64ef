**Business Requirement Document (BRD)**
**Product:** AI-Driven Modular School ERP for Indian Schools
**Module:** Overall Product Overview (Refactored & Complete)
**Prepared For:** Internal Development & Strategy Alignment

---

### I. Executive Summary
We are building a modern, modular, multi-tenant SaaS School ERP platform designed for Indian schools (CBSE, ICSE, State Boards), with built-in support for colleges and coaching centers. The platform will be clean, configurable, white-label ready, and scalable to support multi-branch organizations, global rollout, and role-specific workflows.

---

### II. Core Objectives
- Serve all types of institutions: private schools, coaching centers, and colleges.
- Support multi-branch organizations via a centralized `organization` layer.
- Use a single-DB, multi-tenant architecture with role- and plan-based feature toggling.
- Ensure configurable roles, forms, and modules per institution.
- Lay groundwork for mobile support, internationalization, and enterprise analytics.

---

### III. Target Markets
- Tier 2 & Tier 3 Indian private schools (CBSE/ICSE/State Board)
- Coaching institutes for NEET/JEE preparation (future)
- Residential colleges, B.Tech, Nursing colleges (future)

---

### IV. System Design Principles
- **Modular-first:** Every feature can be toggled on/off per school or plan.
- **Multi-Tenant Aware:** Every entity scoped by `school_id` (and optionally `organization_id`).
- **Subdomain Routing:** Each school/branch will have its own subdomain: `schoolname.myschoolerp.com`
- **Clean Extensibility:** Future-ready hooks for modules, forms, roles, themes.
- **Metadata-Driven:** Forms and dashboards rendered from DB templates.
- **Configurable RBAC:** School-specific roles and permissions.
- **Mobile-first UI:** Fully responsive design.

---

### V. Architecture Overview
#### Backend
- **Framework:** Python + FastAPI
- **Database:** PostgreSQL (single DB, multi-tenant)
- **Caching:** Redis (sessions, config, feature flags)
- **Containerization:** Docker (Oracle Cloud for hosting)

#### Frontend
- **Framework:** React (Next.js)
- **Hosting:** Vercel, Cloudflare Pages (custom domain ready)
- **UI Layer:** TailwindCSS + shadcn/ui + Headless UI
- **State:** React Query + Redux
- **i18n:** i18next or Lingui (multi-language support)

#### Storage
- **Object Storage:** Oracle Cloud (initial), S3-compatible later
- **CDN & DNS:** Cloudflare (SSL, asset caching, DNS)

---

### VI. Core Modules (MVP Scope)
1. Student Admission & Information System ✅
2. Fee Management & Online Payments
3. Attendance Tracking with Parent Notifications
4. Examination & Report Card Generation
5. School Communication (Notices, Circulars)
6. RBAC with permission groups
7. Dynamic Forms & Field Templates (Metadata driven)
8. Audit Logging for all critical actions
9. Feature Flags + Plan Enforcement Engine
10. Tenant Routing & Subdomain Access

---

### VII. Organization & Multi-Branch Support
- `organizations` table to represent a school group/trust.
- Each school links to one organization.
- At signup:
  - Ask: _“Do you manage a single school or a group of branches?”_
  - If multi-branch: create org + head branch + ORG_ADMIN
  - If single school: create both org + school automatically
- ORG_ADMIN can add/manage schools later from a unified dashboard.
- Feature plans can be assigned at org or school level.

---

### VIII. Licensing & Feature Control
- Plans stored in `pricing_plans`, mapped via `school_licenses`
- `plan_features` controls which modules are enabled
- Trial period setup with auto-expiry logic
- JSONB `enabled_features` for school-specific overrides

---

### IX. Role & Permission Engine (RBAC)
- Predefined roles: Admin, Teacher, Accountant, Parent, Student
- Configurable per school
- Permissions grouped by features/modules
- Caching of permissions per session via Redis

---

### X. White-Labeling & Branding Support
- Per-school branding (logo, color, domain, email templates)
- UI loads theme from Redis or DB on subdomain resolution

---

### XI. Internationalization
- Timezone, currency, and date formatting support
- Multi-language ready (UI + DB labels)
- Support region-based feature packs in the future

---

### XII. Security, Data, and Compliance
- JWT + refresh token based auth
- All queries scoped to `school_id`
- Field-level access via permission mapping
- Audit logs of every sensitive action
- DPDPA/GDPR planning ready

---

### XIII. Caching Strategy
- Redis for:
  - Session tokens
  - School config + branding
  - Feature plans
  - Permission sets
- Optional local in-memory fallback cache for non-auth APIs

---

### XIV. Future Features (Post MVP)
- Notification Service (SMS, email, push)
- Analytics Dashboards
- Data Backups + Recovery
- LMS (Assignments, Class Content)
- Transport with GPS
- Hostel Management
- Library & Inventory
- Biometric + RFID support
- Internal Super Admin Panel
- Offline Mode (Mobile Sync)
- Mobile App (React Native)

---

### XV. Next Steps (Development Start)
- ✅ BRD: Student Admission (done)
- ⏳ BRD: Fee Management
- Set up Git + Docker + local dev
- Build: `auth_service`, `school_registry`, `rbac`, `feature_checker`, `form_renderer`
- Seed: roles, permissions, plans, orgs, form templates
- Test: onboarding flow with subdomain routing + tenant resolution

---

This version consolidates all previous discussions and final architecture decisions.

