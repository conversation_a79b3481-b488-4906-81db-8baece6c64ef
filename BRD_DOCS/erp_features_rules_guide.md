**Document Title:** School ERP BRD Overview
**Product:** Modular School ERP (India-first SaaS)
**Purpose:** Business Requirements Document covering end-to-end functionality, architecture, and roadmap.

---

### ✅ Core Functional Requirements (Modules & Capabilities)

#### 🏫 School & Organization Setup
- Support both single-school and multi-branch (organization/trust) structures.
- At registration: Ask user if they're a single school or multi-branch organization.
- If single school: create one school with auto-generated organization.
- If multi-branch: create organization + first school + assign ORG_ADMIN role.
- Admins can add more branches (schools) later.
- Each school linked via `organization_id`.

#### 🔐 Authentication & Access Control
- JWT-based login with refresh token.
- Subdomain-aware login sessions.
- RBAC with configurable roles and permission groups.
- Multi-level access: ORG_ADMIN, SCHOOL_ADMIN, TEACHER, PARENT, ACCOUNTANT, etc.
- Redis caching of roles, permissions, and school config.

#### 🧑‍🎓 Student Management
- Modular tables for student: core, profile, contacts, parents, documents.
- Form-driven, metadata-based dynamic rendering.
- JSONB-based support for flexible custom fields per school.

#### 💰 Fee Management
- Custom fee heads, concessions, fine rules.
- Receipt generation.
- Online payment gateway integration.

#### 📅 Attendance
- Manual or automatic attendance.
- Notification hooks for parents (SMS/app).

#### 📝 Examination & Report Card
- Exam creation, marks entry.
- Multiple report templates for CBSE, ICSE, State Boards.
- Printable and downloadable format.

#### 📣 Communication
- Notices, circulars to selected user groups.

#### 📋 Dynamic Form Builder
- Custom form templates (admission, student info, etc).
- School-level overrides of default templates.

#### 🧾 License & Feature Control
- Plan-based feature flagging.
- `pricing_plans`, `plan_features`, `school_licenses` tables.
- Support trial flow (time-bound access, auto-disable).
- Support for bundle- and region-based pricing.

#### 🔄 Feature Flag Engine
- Per-school or per-org toggling of modules.
- Plug and play system with Redis/cache support.
- AI agents use feature checks to determine accessible components.

#### 🕵️ Audit Logs
- Track all user and system actions (CRUD, login, config changes).

#### 🌐 Localization & White-labeling
- Multi-language, time zone, currency, and formatting support.
- School-specific themes, logo, color palette.
- Email/SMS sender branding.

#### 🧮 Analytics Dashboard (Future Phase)
- Role-based dashboard views.
- Academic, financial, and attendance insights.

#### 📱 Mobile Responsiveness
- Fully responsive web UI.
- Future-ready for PWA and native mobile app.

#### 📂 Document Storage
- Object storage support for documents, photos, certificates.
- File size quota per plan.

#### 🏠 Admin Portals
- School Admin Panel
- Org Super Admin Panel (multi-branch dashboard)
- Internal Super Admin Panel (for platform support – future)

#### 🎓 Coaching & College Adaptability
- Architecture supports custom workflows for colleges, coaching centers.
- Add-on fields and modules for academic year, credits, hostel, courses, etc.

#### 🔁 Offline Support (Future)
- Architecture prepared for offline-first mobile capability.

#### 🧪 Developer Utilities
- Seed scripts
- Swagger API
- Postman Collections

---

### 🧱 Technical Architecture Summary

#### Stack:
- **Backend**: Python + FastAPI (modular service-based)
- **Frontend**: React (to be deployed on Vercel/Cloudflare/Netlify)
- **DB**: PostgreSQL (single DB, multi-tenant with school_id)
- **Cache**: Redis (session, config, permissions)
- **Storage**: Oracle Cloud Object Storage (free tier), future S3 compatible
- **Auth**: JWT (access + refresh)
- **Deployment**: Local Docker for dev, Oracle Cloud backend

#### Multi-Tenant Approach:
- Single DB, single table per resource.
- Every query scoped by `school_id`, optionally `organization_id`.
- Redis key-based cache for fast config and session resolution.

---

### 🔄 Future Capabilities (Planned Post-MVP)

- LMS Module (assignments, online classes)
- Transport Management (GPS, routes, attendance)
- Hostel Management
- Inventory & Asset Tracking
- Library System
- Biometric Integration
- Advanced Data Analytics
- Internal Super Admin Panel
- Offline Mode for Rural Areas
- Mobile App (iOS/Android)

---

### ✅ Security, Compliance & Performance
- Compliance prep: DPDPA, IT Act
- Secure JWT sessions
- Scoped access at API layer
- File upload size/format limits per plan
- Indexed queries for performance
- Redis + local in-memory caching balance

---



### 📜 AI Agent Development Guidelines (Rules to Follow)

#### 🔧 Architecture & Coding
1. **Simplicity First** – Avoid unnecessary abstraction. Clear, readable code > clever code.
2. **Modular Design** – Use clean separation of concerns. Service per module where possible.
3. **Impact Awareness** – Always check if changes impact shared models/services before refactoring.
4. **Follow Conventions** – Use snake_case for DB, camelCase for JSON, PascalCase for Python classes.
5. **Consistency Wins** – Apply consistent naming, import structure, logging format.
6. **Multi-Tenant Awareness** – Every query and logic must scope by tenant/school ID.

#### 💾 Data & Performance
7. **Use Redis Wisely** – Cache config, roles, sessions, permissions. Avoid stale cache bugs.
8. **Optimize Queries** – Avoid N+1, index frequently accessed fields, use joins efficiently.
9. **Seed and Clean** – Seed data must match latest schema; old data/fields must be cleaned up.
10. **Protect Sensitive Data** – Always hash passwords, log only non-sensitive debug info.

#### 🧠 Developer Behavior (AI Agents)
11. **No Workarounds** – Avoid shortcut code; solve it the right way.
12. **Always Test** – Include integration/unit tests especially for service layer.
13. **Comment When Needed** – Add intent-based comments for non-obvious logic.
14. **Document Endpoints** – Use OpenAPI (Swagger) consistently with FastAPI.
15. **Auto-Cleanup** – When modifying a service, clean unused imports/vars immediately.


