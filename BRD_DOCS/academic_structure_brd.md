**Business Requirement Document (BRD)** **Module:** Academic Structure Management (Classes, Sections, Academic Years) **Product:** Modular School ERP (India-first SaaS) **Purpose:** Define how academic years, class and section hierarchy are created and managed across schools, and how they interlink with core modules such as student admission, attendance, fee, exam, and timetable.

---

### I. Objective

To enable schools to define and manage their class-section structure and academic year lifecycle, forming the foundational context for all major academic and administrative operations.

---

### II. Core Business Goals

- Allow schools to define the range of classes they offer.
- Enable flexible section mapping per class.
- Support multi-year academic calendars.
- Maintain strict school-level isolation in structure.
- Enable audit, rollback, and future migration of students.

---

### III. Functional Features

#### 1. Academic Year Management

- Create and manage multiple academic years per school.
- Fields: `year_label`, `start_date`, `end_date`, `status`, `is_active`
- Only one active academic year per school.
- Used in student admission, attendance, fee mapping, exams.

#### 2. Class Management

- Ad<PERSON> can add/edit/delete classes offered by the school.
- Fields: `class_name`, `display_order`, `is_active`, `academic_level` (e.g., Primary, Secondary)
- Examples: Nursery, LKG, UKG, 1 to 12, BTech, NEET
- Class labels should be customizable per school.

#### 3. Section Management

- Create section names (e.g., A, B, C, Red, Blue)
- Used for organizing students within a class.
- Field: `section_name`, `max_capacity`

#### 4. Class-Section Mapping

- Map multiple sections under each class per academic year.
- Table links `academic_year_id`, `class_id`, `section_id`
- Also used to tag students in admission.
- Optional metadata: `class_teacher_id`, `room_no`, etc.

#### 5. Status & Rollback

- Support deactivation of class/section/year.
- Prevent deletion if in use.
- Rollback academic year or archive (read-only mode).

#### 6. Integration Points

- Used in:
  - Student Admission (assign class + section + academic year)
  - Fee Setup (amount per class/section/year)
  - Timetable & Exams (mapped per class/section)
  - Attendance (recorded per class-section-day)

---

### IV. Data Models (Simplified)

```text
academic_years
- id, school_id, year_label, start_date, end_date, is_active

classes
- id, school_id, name, display_order, academic_level, is_active

sections
- id, school_id, name, max_capacity

class_section_mappings
- id, school_id, academic_year_id, class_id, section_id, class_teacher_id (FK), room_no
```

---

### V. API Contracts (Examples)

- `POST /academic-years` → Create new year
- `GET /academic-years?school_id=...` → List all years
- `POST /classes` → Add class
- `POST /sections` → Add section
- `POST /class-section-mappings` → Map sections to class for year
- `GET /class-structure?year=2024` → Fetch full class-section tree

---

### VI. Frontend UX Flow

- Admin Panel > Academic Setup
  - Step 1: Add/Edit Academic Years
  - Step 2: Add Classes (bulk import optional)
  - Step 3: Add Sections
  - Step 4: Assign Sections to Classes per year
- Form validation for overlapping year ranges
- Tagging current year as active and default view

---

### VII. RBAC Access Matrix

| Role       | Access Type              |
| ---------- | ------------------------ |
| Admin      | Full CRUD                |
| Teacher    | View only                |
| Accountant | Read year/class for fees |

---

### VIII. Caching & Performance

- Cache full academic structure per school per year in Redis
- Key: `erp:<school_id>:academic:<year>`
- Invalidate cache on add/update/delete

---

### IX. Extensibility Notes

- Supports college-type structures (departments as class-level metadata)
- Future: Promote students class-wise across years
- Future: Timezone and date-format localization per academic year (global rollout)

---

### X. AI Agent Development Rules

1. Keep academic entities fully scoped to `school_id`
2. Allow dynamic label overrides (e.g., 'Grade' instead of 'Class')
3. Use indexing on `academic_year_id`, `class_id`, `section_id`
4. Validate year overlap, class-section uniqueness during creation
5. Keep logic modular — each service with its own layer
6. Maintain change logs (who edited what, when)
7. Don’t allow deletion of entities linked to active student records

---

**End of BRD: Academic Structure Management**

