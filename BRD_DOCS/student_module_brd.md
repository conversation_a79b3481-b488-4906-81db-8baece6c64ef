**Business Requirement Document (BRD)** **Module:** Student Admission & Information Management **Product:** Modular School ERP (India-first SaaS) **Purpose:** Define the structure, flows, APIs, and design for the student lifecycle system. Includes best-practice guidance for AI-assisted development.

---

### I. Objective

Build a modular, extensible, high-performance Student Admission and Information Management system that forms the foundation for dependent modules like Fee, Exams, Attendance, LMS, and Analytics. Ensure tenant-awareness, performance, and future extensibility.

---

### II. Core Business Goals

- Centralized, normalized student database.
- Support school-specific admission workflows.
- Enable dynamic form configurations per school.
- Handle full student lifecycle: Admission → Active → Alumni → Archived.
- Easy extensibility for colleges/coaching needs (e.g., department, batch, guardian type).

---

### III. Functional Features

#### 1. Student Admission Flow

- Multi-step form: Personal → Contact → Parent → Academic → Documents
- Admission number: Auto-generated (pattern customizable per school)
- Academic Year tagging
- Status lifecycle: Pending → Verified → Approved → Rejected

#### 2. Student Core Information

- Basic: Name, DOB, Gender, Blood Group, Religion, Nationality
- Contact: Mobile, Email, Address, Emergency contact
- Academic: Class, Section, Roll No, Previous School
- Identity: Aadhar, Birth Certificate No., Caste Category

#### 3. Parent/Guardian Information

- Multiple guardian support (father/mother/other)
- Fields: Name, Occupation, Phone, Email, Relationship
- Dynamic tagging (e.g., Financially Responsible)

#### 4. Document Uploads

- Upload student-specific documents (e.g., TC, Birth Cert, Caste Cert, Aadhar)
- Required/Optional config per school/form
- File validation (type, size, required)
- Storage in per-school directory in object store

#### 5. Dynamic Form Support

- Form templates per school stored in `form_templates`
- Field schema and validation rules in `form_fields`
- Rendered on UI dynamically per template
- Support for extra JSONB `custom_fields` in student table

#### 6. Student Listing & Filtering

- Filter by Class, Section, Gender, Category, Date
- Search: Name, Mobile, Admission No, Aadhar
- Export to Excel/PDF
- Pagination, sorting, and server-side filters

#### 7. Profile View & Edit

- Tabbed UI: Basic Info, Contact, Parents, Docs, Audit
- Role-based editable fields
- Audit trail of all changes

#### 8. Audit Logging

- Log all creation/edits/deletes with user, timestamp, change summary
- Stored in `student_audit_logs`

---

### IV. Data Models (Simplified)

```text
students
- id (UUID), school_id, admission_no, class_id, section_id, academic_year_id
- status, dob, gender, photo_url, religion, nationality, custom_fields (JSONB)

student_contacts
- student_id, phone, email, address, emergency_contact

student_parents
- student_id, name, phone, relationship, occupation, financially_responsible

student_documents
- student_id, doc_type, file_url, verified, uploaded_by

form_templates, form_fields
- Per-school template config

student_audit_logs
- action, student_id, changed_by, field_changed, old_value, new_value, timestamp
```

---

### V. API Contracts (Examples)

- `POST /students` → Create student profile (with validation)
- `GET /students?filters...` → List students
- `GET /students/{id}` → Fetch profile with all nested data
- `PUT /students/{id}` → Update profile fields (RBAC controlled)
- `POST /students/{id}/documents` → Upload docs
- `GET /students/{id}/audit` → Fetch audit trail

---

### VI. Frontend Flow

- Multi-step admission form using schema from `form_fields`
- Dynamic tab UI for student profile view
- Document upload modal with file validation
- Role-based editable form fields
- Filters + Export in listing

---

### VII. RBAC Access Matrix

| Role       | Access Type           |
| ---------- | --------------------- |
| Admin      | Full CRUD + Approvals |
| Teacher    | Read + limited edit   |
| Accountant | View only             |
| Parent     | Read child info only  |

---

### VIII. Caching Strategy

- Cache student form schema on load via Redis
- Frequently accessed profile data (class-wise) cached by school\_id
- Cache invalidation on update only

---

### IX. Performance Notes

- Indexed fields: `admission_no`, `school_id`, `class_id`, `aadhar`
- Pagination + filter on server side
- Limit nested joins; batch load nested entities when needed

---

### X. Extensibility Considerations

- JSONB `custom_fields` column in `students` for extra attributes
- Hooks for module integration (e.g., link to Fees, Attendance, Exam)
- Future: Student transfer between schools in same org
- Future: Student-type: day scholar/hosteller/college/coaching

---

### XI. AI Agent Guidelines (Development Rules)

#### 🔧 Architecture & Code Quality

1. Always scope queries by `school_id` (multi-tenancy).
2. Keep code modular – follow clean folder structure.
3. Use async I/O patterns via FastAPI best practices.
4. Cache smartly: Only frequently read config/data.

#### ✅ Performance & Maintainability

5. Use DB indexes on search-heavy fields.
6. Follow DRY principles. Abstract shared logic.
7. Avoid hardcoded fields – respect form templates.
8. Ensure all file uploads are sanitized and validated.
9. Keep endpoints secure – use JWT auth and RBAC check middleware.

#### 🔍 Clean Code Rules

10. Write reusable schema validations with Pydantic.

11. Remove dead code and unused imports when refactoring.

12. Log significant changes with actor + context.

13. Comment complex logic clearly. Avoid unnecessary abstractions.

---

**End of BRD: Student Module (v2.0 Refactored)**

