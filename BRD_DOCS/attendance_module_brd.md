**Business Requirement Document (BRD)** **Module:** Attendance Management **Product:** Modular School ERP (India-first SaaS) **Purpose:** Define how student attendance is recorded, tracked, analyzed, and integrated with other modules like academic structure, student info, calendar, and notifications.

---

### I. Objective

Build a flexible, robust attendance system that allows schools to track daily student attendance at class-section level across academic years. The module must support manual marking, bulk upload, automated systems (future), and reporting.

---

### II. Core Business Goals

- Capture attendance per student daily at class-section level.
- Record full-day, half-day, and subject-wise attendance (future-ready).
- Enable integration with calendar (working days, holidays).
- Generate daily/monthly reports by class/section/date range.
- Notify parents (SMS/push/email) if configured.

---

### III. Functional Features

#### 1. Daily Attendance (Default Mode)

- Mark Present, Absent, Leave, or Half-day per student.
- Mark at class-section level.
- Linked to active `academic_year_id` and `class_section_mapping`.

#### 2. Bulk Attendance Entry

- Allow CSV/XLS upload with format validation.
- Admin/teacher selects class, section, date → upload sheet.
- Map student via admission number or roll number.

#### 3. View Attendance History

- Filter by:
  - Date range
  - Class + section
  - Student name/admission no
- Summary: Total Present, Absent, Leave
- Export to PDF/CSV

#### 4. Calendar Integration

- Track working days vs holidays
- Use academic calendar to exclude holidays
- Block future date entry

#### 5. Notification Trigger (Optional)

- If enabled in school settings:
  - Send notification to parent on absence
  - Modes: Email, SMS, App push
  - Configurable per school

#### 6. Audit & Lock

- Lock attendance after X days (configurable)
- Log who marked attendance and when

---

### IV. Data Models (Simplified)

```text
student_attendance
- id, school_id, student_id, academic_year_id, date
- class_id, section_id, status (P/A/L/H), marked_by, marked_at

attendance_logs
- id, student_id, action, old_value, new_value, changed_by, timestamp

attendance_settings
- school_id, notification_enabled, lock_days_limit
```

---

### V. API Contracts (Examples)

- `POST /attendance/mark` → Mark attendance (batch)
- `POST /attendance/upload` → Bulk upload
- `GET /attendance/report?filters...` → View/report attendance
- `GET /attendance/calendar-view` → Grid view by class/section/date

---

### VI. Frontend UX Flow

- Select Academic Year > Class > Section > Date
- Student list loads → mark status dropdown (P/A/L/H)
- Summary card: % present, absent
- Submit or bulk upload
- View report grid by month/class

---

### VII. RBAC Access Matrix

| Role       | Access Type                    |
| ---------- | ------------------------------ |
| Admin      | Full CRUD, bulk upload         |
| Teacher    | Mark own class attendance only |
| Accountant | View only                      |
| Parent     | View child attendance          |

---

### VIII. Caching & Performance

- Cache attendance summary per class per date
- Redis Key: `erp:<school_id>:attendance:<class_id>:<date>`
- Auto-expire cache on update

---

### IX. Extensibility Notes

- Future: Subject-period-wise attendance
- Future: Biometric/RFID integration
- Future: Auto-flag low attendance (<75%)
- Future: Leave requests approval workflow

---

### X. AI Agent Development Rules

1. All entries must validate against `academic_year_id`, `class_id`, `section_id`.
2. Use async DB calls for batch inserts.
3. Cache summary views, not raw records.
4. Support optional notification hooks as feature flag.
5. Prevent duplicate entries per student/date.
6. Deny future-dated records via server rule.
7. Store action logs for traceability.
8. Modularize calendar utilities and attendance service logic.

---

**End of BRD: Attendance Module**

