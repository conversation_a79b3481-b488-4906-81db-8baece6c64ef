-- School ERP Database Initialization Script
-- Creates additional databases for testing and staging

-- Create test database
CREATE DATABASE school_erp_test;

-- Create staging database (for future use)
CREATE DATABASE school_erp_staging;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE school_erp_dev TO school_erp_user;
GRANT ALL PRIVILEGES ON DATABASE school_erp_test TO school_erp_user;
GRANT ALL PRIVILEGES ON DATABASE school_erp_staging TO school_erp_user;
