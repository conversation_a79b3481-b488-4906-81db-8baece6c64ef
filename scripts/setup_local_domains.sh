#!/bin/bash

# School ERP Local Domain Setup Script
# Configures /etc/hosts for local development with subdomain support

set -e

# Configuration
PRIMARY_DOMAIN="myschoolerp.local"
SUBDOMAINS=("admin" "api" "demo" "test" "school1" "school2")
HOSTS_FILE="/etc/hosts"
BACKUP_FILE="/etc/hosts.backup.$(date +%Y%m%d_%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if running as root/sudo
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}❌ This script requires sudo privileges to modify /etc/hosts${NC}"
        echo -e "${YELLOW}Please run: sudo $0${NC}"
        exit 1
    fi
}

# Function to backup hosts file
backup_hosts_file() {
    echo -e "${YELLOW}📋 Creating backup of hosts file...${NC}"
    cp "$HOSTS_FILE" "$BACKUP_FILE"
    echo -e "${GREEN}✅ Backup created: $BACKUP_FILE${NC}"
    echo ""
}

# Function to add domain entries
add_domain_entries() {
    echo -e "${YELLOW}🌐 Adding domain entries to $HOSTS_FILE${NC}"
    
    # Add marker comments
    echo "" >> "$HOSTS_FILE"
    echo "# School ERP Local Development Domains - Added $(date)" >> "$HOSTS_FILE"
    echo "# Primary domain" >> "$HOSTS_FILE"
    echo "127.0.0.1    $PRIMARY_DOMAIN" >> "$HOSTS_FILE"
    
    # Add subdomain entries
    echo "# Subdomains" >> "$HOSTS_FILE"
    for subdomain in "${SUBDOMAINS[@]}"; do
        echo "127.0.0.1    ${subdomain}.${PRIMARY_DOMAIN}" >> "$HOSTS_FILE"
        echo -e "${GREEN}✅ Added: ${subdomain}.${PRIMARY_DOMAIN}${NC}"
    done
    
    echo "# End School ERP Domains" >> "$HOSTS_FILE"
    echo ""
}

# Function to remove existing entries
remove_existing_entries() {
    echo -e "${YELLOW}🧹 Removing existing School ERP domain entries...${NC}"
    
    # Create temporary file without School ERP entries
    grep -v "School ERP" "$HOSTS_FILE" > "${HOSTS_FILE}.tmp" || true
    grep -v "$PRIMARY_DOMAIN" "${HOSTS_FILE}.tmp" > "${HOSTS_FILE}.clean" || true
    
    # Replace hosts file
    mv "${HOSTS_FILE}.clean" "$HOSTS_FILE"
    rm -f "${HOSTS_FILE}.tmp"
    
    echo -e "${GREEN}✅ Existing entries removed${NC}"
    echo ""
}

# Function to verify domain resolution
verify_domains() {
    echo -e "${YELLOW}🔍 Verifying domain resolution...${NC}"
    
    # Test primary domain
    if ping -c 1 "$PRIMARY_DOMAIN" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Primary domain resolves: $PRIMARY_DOMAIN${NC}"
    else
        echo -e "${RED}❌ Primary domain failed: $PRIMARY_DOMAIN${NC}"
    fi
    
    # Test a few subdomains
    for subdomain in "${SUBDOMAINS[@]:0:3}"; do
        local full_domain="${subdomain}.${PRIMARY_DOMAIN}"
        if ping -c 1 "$full_domain" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Subdomain resolves: $full_domain${NC}"
        else
            echo -e "${RED}❌ Subdomain failed: $full_domain${NC}"
        fi
    done
    
    echo ""
}

# Function to show current configuration
show_configuration() {
    echo -e "${BLUE}📋 Current School ERP Domain Configuration:${NC}"
    echo ""
    echo -e "${YELLOW}Primary Domain:${NC}"
    echo -e "  $PRIMARY_DOMAIN"
    echo ""
    echo -e "${YELLOW}Configured Subdomains:${NC}"
    for subdomain in "${SUBDOMAINS[@]}"; do
        echo -e "  ${subdomain}.${PRIMARY_DOMAIN}"
    done
    echo ""
    echo -e "${YELLOW}All domains point to: 127.0.0.1 (localhost)${NC}"
    echo ""
}

# Function to test with curl
test_with_curl() {
    echo -e "${YELLOW}🌐 Testing HTTP connectivity...${NC}"
    
    # Test if backend is running
    if curl -s "http://localhost:8000/health" >/dev/null; then
        echo -e "${GREEN}✅ Backend is running on localhost:8000${NC}"
        
        # Test domain access (this will fail until we implement subdomain routing)
        echo -e "${YELLOW}Note: Subdomain routing will be implemented in later phases${NC}"
    else
        echo -e "${YELLOW}⚠️  Backend not running on localhost:8000${NC}"
        echo -e "${YELLOW}   Start with: docker-compose up -d${NC}"
    fi
    
    echo ""
}

# Function to show usage instructions
show_usage() {
    echo -e "${BLUE}🚀 Usage Instructions:${NC}"
    echo ""
    echo -e "${YELLOW}1. Start the backend services:${NC}"
    echo -e "   docker-compose up -d"
    echo ""
    echo -e "${YELLOW}2. Access the application:${NC}"
    echo -e "   Primary: http://$PRIMARY_DOMAIN:8000"
    echo -e "   API Docs: http://$PRIMARY_DOMAIN:8000/docs"
    echo -e "   Health: http://$PRIMARY_DOMAIN:8000/health"
    echo ""
    echo -e "${YELLOW}3. Subdomains (will work after subdomain routing implementation):${NC}"
    for subdomain in "${SUBDOMAINS[@]:0:3}"; do
        echo -e "   http://${subdomain}.${PRIMARY_DOMAIN}:8000"
    done
    echo ""
}

# Main function
main() {
    echo -e "${GREEN}🏫 School ERP Local Domain Setup${NC}"
    echo ""
    
    check_permissions
    backup_hosts_file
    remove_existing_entries
    add_domain_entries
    show_configuration
    verify_domains
    test_with_curl
    show_usage
    
    echo -e "${GREEN}🎉 Local domain setup completed successfully!${NC}"
    echo -e "${YELLOW}💡 Tip: Use 'sudo $0 remove' to remove all entries${NC}"
}

# Handle script arguments
case "${1:-}" in
    "remove")
        check_permissions
        backup_hosts_file
        remove_existing_entries
        echo -e "${GREEN}✅ School ERP domain entries removed${NC}"
        ;;
    "show")
        show_configuration
        ;;
    "test")
        verify_domains
        test_with_curl
        ;;
    "help"|"-h"|"--help")
        echo "School ERP Local Domain Setup Script"
        echo ""
        echo "Usage:"
        echo "  sudo $0         - Setup local domains"
        echo "  sudo $0 remove  - Remove domain entries"
        echo "  $0 show         - Show current configuration"
        echo "  $0 test         - Test domain resolution"
        echo "  $0 help         - Show this help message"
        echo ""
        ;;
    *)
        main
        ;;
esac
