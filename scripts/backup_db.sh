#!/bin/bash

# School ERP Database Backup Script
# Creates timestamped backups of PostgreSQL databases

set -e

# Configuration
BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
CONTAINER_NAME="school_erp_postgres"
DB_USER="school_erp_user"
DATABASES=("school_erp_dev" "school_erp_test" "school_erp_staging")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

echo -e "${GREEN}🗄️  School ERP Database Backup${NC}"
echo -e "${YELLOW}Timestamp: $TIMESTAMP${NC}"
echo ""

# Function to backup a single database
backup_database() {
    local db_name=$1
    local backup_file="$BACKUP_DIR/${db_name}_backup_${TIMESTAMP}.sql"
    
    echo -e "${YELLOW}📦 Backing up database: $db_name${NC}"
    
    # Check if container is running
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        echo -e "${RED}❌ Error: PostgreSQL container '$CONTAINER_NAME' is not running${NC}"
        return 1
    fi
    
    # Create backup
    if docker exec "$CONTAINER_NAME" pg_dump -U "$DB_USER" -d "$db_name" > "$backup_file"; then
        # Compress the backup
        gzip "$backup_file"
        echo -e "${GREEN}✅ Backup completed: ${backup_file}.gz${NC}"
        
        # Show file size
        local file_size=$(du -h "${backup_file}.gz" | cut -f1)
        echo -e "${GREEN}   File size: $file_size${NC}"
    else
        echo -e "${RED}❌ Error: Failed to backup database $db_name${NC}"
        return 1
    fi
    
    echo ""
}

# Function to cleanup old backups (keep last 7 days)
cleanup_old_backups() {
    echo -e "${YELLOW}🧹 Cleaning up old backups (keeping last 7 days)${NC}"
    
    # Find and delete backups older than 7 days
    local deleted_count=$(find "$BACKUP_DIR" -name "*.sql.gz" -mtime +7 -delete -print | wc -l)
    
    if [ "$deleted_count" -gt 0 ]; then
        echo -e "${GREEN}✅ Deleted $deleted_count old backup files${NC}"
    else
        echo -e "${GREEN}✅ No old backups to clean up${NC}"
    fi
    
    echo ""
}

# Function to show backup statistics
show_backup_stats() {
    echo -e "${YELLOW}📊 Backup Statistics${NC}"
    
    local total_backups=$(find "$BACKUP_DIR" -name "*.sql.gz" | wc -l)
    local total_size=$(du -sh "$BACKUP_DIR" 2>/dev/null | cut -f1 || echo "0B")
    
    echo -e "${GREEN}   Total backups: $total_backups${NC}"
    echo -e "${GREEN}   Total size: $total_size${NC}"
    
    # Show latest backups
    echo -e "${YELLOW}   Latest backups:${NC}"
    find "$BACKUP_DIR" -name "*.sql.gz" -printf "%T@ %Tc %p\n" | sort -n | tail -5 | while read -r line; do
        echo -e "${GREEN}     $(echo "$line" | cut -d' ' -f2- | cut -d' ' -f1-6) - $(basename "$(echo "$line" | awk '{print $NF}')")${NC}"
    done
    
    echo ""
}

# Main backup process
main() {
    echo -e "${GREEN}Starting backup process...${NC}"
    echo ""
    
    # Backup each database
    for db in "${DATABASES[@]}"; do
        backup_database "$db"
    done
    
    # Cleanup old backups
    cleanup_old_backups
    
    # Show statistics
    show_backup_stats
    
    echo -e "${GREEN}🎉 Backup process completed successfully!${NC}"
}

# Handle script arguments
case "${1:-}" in
    "cleanup")
        cleanup_old_backups
        ;;
    "stats")
        show_backup_stats
        ;;
    "help"|"-h"|"--help")
        echo "School ERP Database Backup Script"
        echo ""
        echo "Usage:"
        echo "  $0           - Run full backup"
        echo "  $0 cleanup   - Clean up old backups only"
        echo "  $0 stats     - Show backup statistics"
        echo "  $0 help      - Show this help message"
        echo ""
        ;;
    *)
        main
        ;;
esac
