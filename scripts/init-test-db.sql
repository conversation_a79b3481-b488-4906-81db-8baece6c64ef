-- School ERP Test Database Initialization Script
-- Creates test-specific databases and configurations

-- Create additional test databases for isolation
CREATE DATABASE school_erp_test_integration;
CREATE DATABASE school_erp_test_unit;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE school_erp_test TO school_erp_user;
GRANT ALL PRIVILEGES ON DATABASE school_erp_test_integration TO school_erp_user;
GRANT ALL PRIVILEGES ON DATABASE school_erp_test_unit TO school_erp_user;
