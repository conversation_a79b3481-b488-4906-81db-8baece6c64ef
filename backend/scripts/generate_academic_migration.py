#!/usr/bin/env python3
"""
Script to generate Alembic migration for academic structure models
This script creates the migration file manually since alembic autogenerate has path conflicts
"""

import os
import sys
from datetime import datetime
import uuid

# Add the app directory to Python path
sys.path.insert(0, '/app')

def generate_migration_content():
    """Generate the migration file content"""
    
    revision_id = str(uuid.uuid4())[:12]  # Short revision ID
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    migration_content = f'''"""Add academic structure models

Revision ID: {revision_id}
Revises: 
Create Date: {datetime.now().isoformat()}

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '{revision_id}'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Create academic structure tables"""
    
    # Create academic_years table
    op.create_table('academic_years',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('school_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('year_label', sa.String(length=20), nullable=False),
        sa.Column('display_name', sa.String(length=100), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('start_date', sa.Date(), nullable=False),
        sa.Column('end_date', sa.Date(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('total_working_days', sa.Integer(), nullable=True),
        sa.Column('actual_working_days', sa.Integer(), nullable=False),
        sa.Column('terms_config', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('holidays_config', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('settings', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('academic_calendar_approved', sa.Boolean(), nullable=False),
        sa.Column('approved_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('approved_at', sa.Date(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('deletion_reason', sa.String(length=255), nullable=True),
        sa.Column('audit_created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('audit_updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('audit_version', sa.Integer(), nullable=False),
        sa.CheckConstraint('end_date > start_date', name='ck_academic_year_date_range'),
        sa.CheckConstraint("status IN ('draft', 'active', 'completed', 'archived')", name='ck_academic_year_status'),
        sa.ForeignKeyConstraint(['school_id'], ['schools.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('school_id', 'is_active', name='uq_academic_year_active_per_school'),
        sa.UniqueConstraint('school_id', 'year_label', name='uq_academic_year_label_per_school')
    )
    
    # Create indexes for academic_years
    op.create_index('idx_academic_years_school_status', 'academic_years', ['school_id', 'status'])
    op.create_index('idx_academic_years_school_dates', 'academic_years', ['school_id', 'start_date', 'end_date'])
    op.create_index('idx_academic_years_active_lookup', 'academic_years', ['school_id', 'is_active', 'status'])
    op.create_index(op.f('ix_academic_years_year_label'), 'academic_years', ['year_label'])
    op.create_index(op.f('ix_academic_years_start_date'), 'academic_years', ['start_date'])
    op.create_index(op.f('ix_academic_years_end_date'), 'academic_years', ['end_date'])
    op.create_index(op.f('ix_academic_years_is_active'), 'academic_years', ['is_active'])
    op.create_index(op.f('ix_academic_years_status'), 'academic_years', ['status'])
    
    # Create classes table
    op.create_table('classes',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('school_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('display_name', sa.String(length=200), nullable=True),
        sa.Column('short_name', sa.String(length=20), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('class_code', sa.String(length=20), nullable=True),
        sa.Column('display_order', sa.Integer(), nullable=False),
        sa.Column('academic_level', sa.String(length=50), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('max_students_per_section', sa.Integer(), nullable=False),
        sa.Column('settings', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('deletion_reason', sa.String(length=255), nullable=True),
        sa.Column('audit_created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('audit_updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('audit_version', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['school_id'], ['schools.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('school_id', 'name', name='uq_class_name_per_school'),
        sa.UniqueConstraint('school_id', 'display_order', name='uq_class_display_order_per_school')
    )
    
    # Create indexes for classes
    op.create_index('idx_classes_school_active', 'classes', ['school_id', 'is_active'])
    op.create_index('idx_classes_school_level', 'classes', ['school_id', 'academic_level'])
    op.create_index('idx_classes_school_order', 'classes', ['school_id', 'display_order'])
    op.create_index(op.f('ix_classes_name'), 'classes', ['name'])
    op.create_index(op.f('ix_classes_class_code'), 'classes', ['class_code'], unique=True)
    op.create_index(op.f('ix_classes_display_order'), 'classes', ['display_order'])
    op.create_index(op.f('ix_classes_academic_level'), 'classes', ['academic_level'])
    op.create_index(op.f('ix_classes_is_active'), 'classes', ['is_active'])
    
    # Create sections table
    op.create_table('sections',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('school_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=50), nullable=False),
        sa.Column('display_name', sa.String(length=100), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('section_code', sa.String(length=20), nullable=True),
        sa.Column('max_capacity', sa.Integer(), nullable=False),
        sa.Column('current_strength', sa.Integer(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('settings', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('deletion_reason', sa.String(length=255), nullable=True),
        sa.Column('audit_created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('audit_updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('audit_version', sa.Integer(), nullable=False),
        sa.CheckConstraint('current_strength <= max_capacity', name='ck_section_capacity'),
        sa.ForeignKeyConstraint(['school_id'], ['schools.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('school_id', 'name', name='uq_section_name_per_school')
    )
    
    # Create indexes for sections
    op.create_index('idx_sections_school_active', 'sections', ['school_id', 'is_active'])
    op.create_index('idx_sections_school_capacity', 'sections', ['school_id', 'max_capacity'])
    op.create_index(op.f('ix_sections_name'), 'sections', ['name'])
    op.create_index(op.f('ix_sections_section_code'), 'sections', ['section_code'])
    op.create_index(op.f('ix_sections_is_active'), 'sections', ['is_active'])


def downgrade():
    """Drop academic structure tables"""
    
    # Drop indexes first
    op.drop_index(op.f('ix_sections_is_active'), table_name='sections')
    op.drop_index(op.f('ix_sections_section_code'), table_name='sections')
    op.drop_index(op.f('ix_sections_name'), table_name='sections')
    op.drop_index('idx_sections_school_capacity', table_name='sections')
    op.drop_index('idx_sections_school_active', table_name='sections')
    
    op.drop_index(op.f('ix_classes_is_active'), table_name='classes')
    op.drop_index(op.f('ix_classes_academic_level'), table_name='classes')
    op.drop_index(op.f('ix_classes_display_order'), table_name='classes')
    op.drop_index(op.f('ix_classes_class_code'), table_name='classes')
    op.drop_index(op.f('ix_classes_name'), table_name='classes')
    op.drop_index('idx_classes_school_order', table_name='classes')
    op.drop_index('idx_classes_school_level', table_name='classes')
    op.drop_index('idx_classes_school_active', table_name='classes')
    
    op.drop_index(op.f('ix_academic_years_status'), table_name='academic_years')
    op.drop_index(op.f('ix_academic_years_is_active'), table_name='academic_years')
    op.drop_index(op.f('ix_academic_years_end_date'), table_name='academic_years')
    op.drop_index(op.f('ix_academic_years_start_date'), table_name='academic_years')
    op.drop_index(op.f('ix_academic_years_year_label'), table_name='academic_years')
    op.drop_index('idx_academic_years_active_lookup', table_name='academic_years')
    op.drop_index('idx_academic_years_school_dates', table_name='academic_years')
    op.drop_index('idx_academic_years_school_status', table_name='academic_years')
    
    # Drop tables
    op.drop_table('sections')
    op.drop_table('classes')
    op.drop_table('academic_years')
'''
    
    return migration_content, revision_id, timestamp

def main():
    """Generate and save the migration file"""
    
    # Generate migration content
    content, revision_id, timestamp = generate_migration_content()
    
    # Create filename
    filename = f"{timestamp}_add_academic_structure_models_{revision_id}.py"
    filepath = f"/app/alembic/versions/{filename}"
    
    # Write migration file
    with open(filepath, 'w') as f:
        f.write(content)
    
    print(f"Migration file created: {filepath}")
    print(f"Revision ID: {revision_id}")
    
    return filepath

if __name__ == "__main__":
    main()
