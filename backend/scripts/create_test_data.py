#!/usr/bin/env python3
"""
Create test data for School ERP authentication testing
"""

import sys
import os
import uuid

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.models.base import School, Organization
from app.models.auth import User, Role, Permission
from app.core.database import get_db_context
from app.core.security import password_manager
from app.core.logging import get_logger

logger = get_logger(__name__)

# Test configuration
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "TestPassword123!"
TEST_SCHOOL_SUBDOMAIN = "testschool"


def create_test_data():
    """Create test data in database"""
    print("🔧 Creating test data...")
    
    with get_db_context() as db:
        try:
            # Check if test data already exists
            existing_school = db.query(School).filter(School.subdomain == TEST_SCHOOL_SUBDOMAIN).first()
            if existing_school:
                print(f"✅ Test school '{TEST_SCHOOL_SUBDOMAIN}' already exists")
                return existing_school.id
            
            # Create test organization
            org = Organization(
                name="Test Organization",
                email="<EMAIL>",
                subdomain="testorg"
            )
            db.add(org)
            db.flush()
            
            # Create test school with pre-generated ID
            school_id = uuid.uuid4()
            school = School(
                id=school_id,
                school_id=school_id,  # Self-reference for multi-tenant
                organization_id=org.id,
                name="Test School",
                email="<EMAIL>",
                subdomain=TEST_SCHOOL_SUBDOMAIN,
                school_code="TEST001"
            )
            db.add(school)
            db.flush()
            
            # Create test user
            user = User(
                school_id=school.id,
                email=TEST_EMAIL,
                first_name="Test",
                last_name="User",
                user_type="admin"
            )
            user.set_password(TEST_PASSWORD)
            user.is_verified = True
            user.is_active = True
            db.add(user)
            db.flush()
            
            # Create test role and permission
            permission = Permission(
                school_id=school.id,
                code="admin.all",
                name="Admin All Permission",
                category="admin",
                resource="all",
                action="all"
            )
            db.add(permission)

            role = Role(
                school_id=school.id,
                name="admin",
                display_name="Administrator"
            )
            db.add(role)
            
            db.commit()
            
            print(f"✅ Test data created successfully:")
            print(f"   School ID: {school.id}")
            print(f"   User ID: {user.id}")
            print(f"   Email: {TEST_EMAIL}")
            print(f"   Password: {TEST_PASSWORD}")
            print(f"   Subdomain: {TEST_SCHOOL_SUBDOMAIN}")
            
            return school.id
            
        except Exception as e:
            db.rollback()
            print(f"❌ Failed to create test data: {str(e)}")
            raise


def main():
    """Create test data"""
    try:
        create_test_data()
        print("🎉 Test data creation completed!")
    except Exception as e:
        print(f"💥 Test data creation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
