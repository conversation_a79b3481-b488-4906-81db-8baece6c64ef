#!/usr/bin/env python3
"""
<PERSON>ript to run Alembic migration for academic structure models
"""

import os
import sys

# Add the app directory to Python path
sys.path.insert(0, '/app')

# Set environment variables
os.environ['PYTHONPATH'] = '/app'

try:
    from alembic.config import Config
    from alembic import command
    
    # Create alembic config
    config = Config('/app/alembic.ini')
    
    # Run migration
    print("Running migration to create academic structure tables...")
    command.upgrade(config, 'head')
    print("Migration completed successfully!")
    
except Exception as e:
    print(f"Migration failed: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
