#!/usr/bin/env python3
"""
Comprehensive API endpoint testing for School ERP
Tests all implemented endpoints with proper validation
"""

import sys
import os
import json
import uuid
from datetime import datetime
import requests
import time

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.models.base import School, Organization
from app.models.auth import User, Role, Permission
from app.core.database import get_db_context
from app.core.security import password_manager
from app.core.logging import get_logger

logger = get_logger(__name__)

# Test configuration
BASE_URL = "http://myschoolerp.local:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "TestPassword123!"
TEST_SCHOOL_SUBDOMAIN = "testschool"


class APITester:
    """Comprehensive API endpoint tester"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.access_token = None
        self.refresh_token = None
        self.test_school_id = None
        self.test_user_id = None
        self.session = requests.Session()
        
    def setup_test_data(self):
        """Create test data in database"""
        print("🔧 Setting up test data...")
        
        with get_db_context() as db:
            try:
                # Create test organization
                org = Organization(
                    name="Test Organization",
                    email="<EMAIL>",
                    subdomain="testorg"
                )
                db.add(org)
                db.flush()
                
                # Create test school with pre-generated ID
                school_id = uuid.uuid4()
                school = School(
                    id=school_id,
                    school_id=school_id,  # Self-reference for multi-tenant
                    organization_id=org.id,
                    name="Test School",
                    email="<EMAIL>",
                    subdomain=TEST_SCHOOL_SUBDOMAIN,
                    school_code="TEST001"
                )
                db.add(school)
                db.flush()
                self.test_school_id = school.id
                
                # Create test user
                user = User(
                    school_id=school.id,
                    email=TEST_EMAIL,
                    first_name="Test",
                    last_name="User",
                    user_type="admin"
                )
                user.set_password(TEST_PASSWORD)
                user.is_verified = True
                db.add(user)
                db.flush()
                self.test_user_id = user.id
                
                # Create test role and permission (without associations for now)
                permission = Permission(
                    school_id=school.id,
                    code="test.read",
                    name="Test Read Permission",
                    category="test",
                    resource="test",
                    action="read"
                )
                db.add(permission)

                role = Role(
                    school_id=school.id,
                    name="test_admin",
                    display_name="Test Admin"
                )
                db.add(role)

                # Note: Skipping role-permission associations for now due to association table complexity
                
                db.commit()
                
                print(f"✅ Test data created:")
                print(f"   School ID: {self.test_school_id}")
                print(f"   User ID: {self.test_user_id}")
                print(f"   Email: {TEST_EMAIL}")
                print(f"   Subdomain: {TEST_SCHOOL_SUBDOMAIN}")
                
            except Exception as e:
                db.rollback()
                print(f"❌ Failed to create test data: {str(e)}")
                raise
    
    def cleanup_test_data(self):
        """Clean up test data"""
        print("🧹 Cleaning up test data...")

        with get_db_context() as db:
            try:
                # Delete test data in proper order (foreign key constraints)
                db.query(Permission).filter(Permission.code.like("test.%")).delete()
                db.query(Role).filter(Role.name.like("test_%")).delete()
                db.query(User).filter(User.email == TEST_EMAIL).delete()
                db.query(School).filter(School.subdomain == TEST_SCHOOL_SUBDOMAIN).delete()
                db.query(Organization).filter(Organization.subdomain == "testorg").delete()
                db.commit()
                print("✅ Test data cleaned up")

            except Exception as e:
                db.rollback()
                print(f"❌ Failed to cleanup test data: {str(e)}")
    
    def test_health_endpoint(self):
        """Test health check endpoint"""
        print("\n🏥 Testing Health Endpoint...")
        
        try:
            response = self.session.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    print("✅ Health endpoint working")
                    return True
                else:
                    print(f"❌ Health endpoint returned wrong status: {data}")
                    return False
            else:
                print(f"❌ Health endpoint failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Health endpoint error: {str(e)}")
            return False
    
    def test_subdomain_endpoints(self):
        """Test subdomain management endpoints"""
        print("\n🌐 Testing Subdomain Endpoints...")
        
        results = []
        
        # Test subdomain validation
        try:
            response = self.session.post(
                f"{self.base_url}/api/v1/subdomain/validate",
                json={"subdomain": "validtest"}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("is_valid"):
                    print("✅ Subdomain validation working")
                    results.append(True)
                else:
                    print(f"❌ Subdomain validation failed: {data}")
                    results.append(False)
            else:
                print(f"❌ Subdomain validation endpoint failed: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Subdomain validation error: {str(e)}")
            results.append(False)
        
        # Test subdomain suggestions
        try:
            response = self.session.post(
                f"{self.base_url}/api/v1/subdomain/suggestions",
                json={"school_name": "Test School", "limit": 3}
            )
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data.get("suggestions"), list):
                    print("✅ Subdomain suggestions working")
                    results.append(True)
                else:
                    print(f"❌ Subdomain suggestions failed: {data}")
                    results.append(False)
            else:
                print(f"❌ Subdomain suggestions endpoint failed: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Subdomain suggestions error: {str(e)}")
            results.append(False)
        
        # Test reserved subdomains
        try:
            response = self.session.get(f"{self.base_url}/api/v1/subdomain/reserved")
            
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list) and len(data) > 0:
                    print(f"✅ Reserved subdomains working ({len(data)} reserved)")
                    results.append(True)
                else:
                    print(f"❌ Reserved subdomains failed: {data}")
                    results.append(False)
            else:
                print(f"❌ Reserved subdomains endpoint failed: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Reserved subdomains error: {str(e)}")
            results.append(False)
        
        return all(results)
    
    def test_authentication_endpoints(self):
        """Test authentication endpoints"""
        print("\n🔐 Testing Authentication Endpoints...")
        
        results = []
        
        # Test login
        try:
            login_data = {
                "email": TEST_EMAIL,
                "password": TEST_PASSWORD,
                "school_subdomain": TEST_SCHOOL_SUBDOMAIN
            }
            
            response = self.session.post(
                f"{self.base_url}/api/v1/auth/login",
                json=login_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and data.get("access_token"):
                    self.access_token = data["access_token"]
                    self.refresh_token = data["refresh_token"]
                    print("✅ Login endpoint working")
                    results.append(True)
                else:
                    print(f"❌ Login failed: {data}")
                    results.append(False)
            else:
                print(f"❌ Login endpoint failed: {response.status_code} - {response.text}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ Login error: {str(e)}")
            results.append(False)
        
        # Test /me endpoint (requires authentication)
        if self.access_token:
            try:
                headers = {"Authorization": f"Bearer {self.access_token}"}
                response = self.session.get(
                    f"{self.base_url}/api/v1/auth/me",
                    headers=headers
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("user") and data["user"].get("email") == TEST_EMAIL:
                        print("✅ /me endpoint working")
                        results.append(True)
                    else:
                        print(f"❌ /me endpoint returned wrong data: {data}")
                        results.append(False)
                else:
                    print(f"❌ /me endpoint failed: {response.status_code}")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ /me endpoint error: {str(e)}")
                results.append(False)
        
        # Test token refresh
        if self.refresh_token:
            try:
                refresh_data = {
                    "refresh_token": self.refresh_token,
                    "school_subdomain": TEST_SCHOOL_SUBDOMAIN
                }
                
                response = self.session.post(
                    f"{self.base_url}/api/v1/auth/refresh",
                    json=refresh_data
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("success") and data.get("access_token"):
                        print("✅ Token refresh working")
                        results.append(True)
                    else:
                        print(f"❌ Token refresh failed: {data}")
                        results.append(False)
                else:
                    print(f"❌ Token refresh endpoint failed: {response.status_code}")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ Token refresh error: {str(e)}")
                results.append(False)
        
        return all(results)
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        print("\n⏱️ Testing Rate Limiting...")
        
        try:
            # Make rapid requests to trigger rate limiting
            responses = []
            for i in range(35):  # More than burst limit
                response = self.session.get(f"{self.base_url}/health")
                responses.append(response.status_code)
                if i > 30 and response.status_code == 429:
                    print("✅ Rate limiting working (429 Too Many Requests)")
                    return True
            
            # If we didn't get rate limited, check if all requests succeeded
            if all(status == 200 for status in responses):
                print("⚠️ Rate limiting not triggered (might be configured higher)")
                return True
            else:
                print(f"❌ Unexpected response codes: {set(responses)}")
                return False
                
        except Exception as e:
            print(f"❌ Rate limiting test error: {str(e)}")
            return False
    
    def test_security_headers(self):
        """Test security headers"""
        print("\n🛡️ Testing Security Headers...")
        
        try:
            response = self.session.get(f"{self.base_url}/health")
            headers = response.headers
            
            required_headers = [
                "X-Frame-Options",
                "X-Content-Type-Options", 
                "X-XSS-Protection",
                "Content-Security-Policy",
                "Referrer-Policy"
            ]
            
            missing_headers = []
            for header in required_headers:
                if header not in headers:
                    missing_headers.append(header)
            
            if not missing_headers:
                print("✅ All security headers present")
                return True
            else:
                print(f"❌ Missing security headers: {missing_headers}")
                return False
                
        except Exception as e:
            print(f"❌ Security headers test error: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all API tests"""
        print("🧪 School ERP API Endpoint Testing Suite")
        print("=" * 60)
        
        # Setup
        self.setup_test_data()
        
        test_results = []
        
        try:
            # Run tests
            test_results.append(("Health Endpoint", self.test_health_endpoint()))
            test_results.append(("Subdomain Endpoints", self.test_subdomain_endpoints()))
            test_results.append(("Authentication Endpoints", self.test_authentication_endpoints()))
            test_results.append(("Rate Limiting", self.test_rate_limiting()))
            test_results.append(("Security Headers", self.test_security_headers()))
            
            # Results summary
            print("\n" + "=" * 60)
            print("📊 Test Results Summary:")
            
            passed = 0
            total = len(test_results)
            
            for test_name, result in test_results:
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"   {test_name}: {status}")
                if result:
                    passed += 1
            
            print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
            
            if passed == total:
                print("🎉 All API tests passed! Endpoints are working correctly.")
                return True
            else:
                print("⚠️ Some tests failed. Please check the implementation.")
                return False
                
        finally:
            # Cleanup
            self.cleanup_test_data()


def main():
    """Run API endpoint tests"""
    tester = APITester()
    success = tester.run_all_tests()
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
