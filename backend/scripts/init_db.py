#!/usr/bin/env python3
"""
Database initialization script for School ERP
Creates tables and runs initial setup
"""

import sys
import os

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core.database import init_db, check_db_connection
from app.core.logging import get_logger

logger = get_logger(__name__)


def main():
    """Initialize the database"""
    logger.info("Starting database initialization...")
    
    # Check database connection
    if not check_db_connection():
        logger.error("Database connection failed. Exiting.")
        sys.exit(1)
    
    # Initialize database tables
    try:
        init_db()
        logger.info("Database initialization completed successfully!")
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
