["app/tests/test_academic.py::TestAcademicYearModel::test_academic_year_computed_fields", "app/tests/test_academic.py::TestAcademicYearModel::test_academic_year_unique_constraints", "app/tests/test_academic.py::TestAcademicYearModel::test_academic_year_validation", "app/tests/test_academic.py::TestAcademicYearModel::test_create_academic_year", "app/tests/test_academic.py::TestAcademicYearService::test_activate_academic_year", "app/tests/test_academic.py::TestAcademicYearService::test_complete_academic_year", "app/tests/test_academic.py::TestAcademicYearService::test_create_academic_year_success", "app/tests/test_academic.py::TestAcademicYearService::test_create_academic_year_validation_errors", "app/tests/test_academic.py::TestAcademicYearService::test_create_academic_year_with_auto_activate", "app/tests/test_academic.py::TestAcademicYearService::test_delete_academic_year", "app/tests/test_academic.py::TestAcademicYearService::test_generate_academic_year_label", "app/tests/test_academic.py::TestAcademicYearService::test_get_academic_year", "app/tests/test_academic.py::TestAcademicYearService::test_get_active_academic_year", "app/tests/test_academic.py::TestAcademicYearService::test_get_current_indian_academic_year", "app/tests/test_academic.py::TestAcademicYearService::test_list_academic_years", "app/tests/test_academic.py::TestSubjectModel::test_create_subject", "app/tests/test_academic.py::TestSubjectModel::test_subject_computed_properties", "app/tests/test_academic.py::TestSubjectModel::test_subject_validation", "app/tests/test_academic.py::TestSubjectService::test_create_subject_duplicate_code", "app/tests/test_academic.py::TestSubjectService::test_create_subject_success", "app/tests/test_academic.py::TestSubjectService::test_delete_subject", "app/tests/test_academic.py::TestSubjectService::test_list_subjects", "app/tests/test_academic.py::TestSubjectService::test_update_subject", "app/tests/test_audit.py::TestAuditCompliance::test_audit_log_immutability", "app/tests/test_audit.py::TestAuditCompliance::test_audit_log_retention", "app/tests/test_audit.py::TestAuditFiltering::test_filter_by_action", "app/tests/test_audit.py::TestAuditFiltering::test_filter_by_date_range", "app/tests/test_audit.py::TestAuditFiltering::test_filter_by_severity", "app/tests/test_audit.py::TestAuditIntegration::test_audit_with_authentication", "app/tests/test_audit.py::TestAuditService::test_get_audit_trail", "app/tests/test_audit.py::TestAuditService::test_get_security_events", "app/tests/test_audit.py::TestAuditService::test_get_user_activity", "app/tests/test_audit.py::TestAuditService::test_log_audit_event_basic", "app/tests/test_audit.py::TestAuditService::test_log_audit_event_with_changes", "app/tests/test_audit.py::TestAuditService::test_log_critical_security_event", "app/tests/test_audit.py::TestAuditService::test_log_security_event", "app/tests/test_auth.py::TestAuthAPI::test_change_password_success", "app/tests/test_auth.py::TestAuthAPI::test_change_password_unauthorized", "app/tests/test_auth.py::TestAuthAPI::test_login_invalid_credentials", "app/tests/test_auth.py::TestAuthAPI::test_login_missing_subdomain", "app/tests/test_auth.py::TestAuthAPI::test_login_success", "app/tests/test_auth.py::TestAuthService::test_authenticate_user_inactive_account", "app/tests/test_auth.py::TestAuthService::test_authenticate_user_invalid_email", "app/tests/test_auth.py::TestAuthService::test_authenticate_user_invalid_password", "app/tests/test_auth.py::TestAuthService::test_authenticate_user_locked_account", "app/tests/test_auth.py::TestAuthService::test_authenticate_user_success", "app/tests/test_auth.py::TestAuthService::test_change_password_invalid_current", "app/tests/test_auth.py::TestAuthService::test_change_password_success", "app/tests/test_auth.py::TestPasswordManager::test_hash_password", "app/tests/test_auth.py::TestPasswordManager::test_validate_password_strength_invalid", "app/tests/test_auth.py::TestPasswordManager::test_validate_password_strength_valid", "app/tests/test_auth.py::TestPasswordManager::test_verify_password_correct", "app/tests/test_auth.py::TestPasswordManager::test_verify_password_incorrect", "app/tests/test_auth.py::TestSecurityManager::test_create_access_token", "app/tests/test_auth.py::TestSecurityManager::test_create_refresh_token", "app/tests/test_auth.py::TestSecurityManager::test_verify_token_expired", "app/tests/test_auth.py::TestSecurityManager::test_verify_token_invalid", "app/tests/test_auth.py::TestSecurityManager::test_verify_token_valid", "app/tests/test_models.py::TestModelRelationships::test_cascade_delete_organization_schools", "app/tests/test_models.py::TestModelRelationships::test_multi_tenant_isolation", "app/tests/test_models.py::TestOrganizationModel::test_create_organization", "app/tests/test_models.py::TestOrganizationModel::test_organization_soft_delete", "app/tests/test_models.py::TestOrganizationModel::test_organization_unique_subdomain", "app/tests/test_models.py::TestPermissionModel::test_create_permission", "app/tests/test_models.py::TestPermissionModel::test_permission_unique_code", "app/tests/test_models.py::TestRoleModel::test_create_role", "app/tests/test_models.py::TestRoleModel::test_role_hierarchy", "app/tests/test_models.py::TestSchoolModel::test_create_school", "app/tests/test_models.py::TestSchoolModel::test_school_multi_tenant_setup", "app/tests/test_models.py::TestSchoolModel::test_school_organization_relationship", "app/tests/test_models.py::TestSchoolModel::test_school_unique_constraints", "app/tests/test_models.py::TestUserModel::test_create_user", "app/tests/test_models.py::TestUserModel::test_user_account_locking", "app/tests/test_models.py::TestUserModel::test_user_failed_login_tracking", "app/tests/test_models.py::TestUserModel::test_user_password_hashing", "app/tests/test_models.py::TestUserModel::test_user_permissions", "app/tests/test_models.py::TestUserSessionModel::test_create_user_session", "app/tests/test_models.py::TestUserSessionModel::test_session_activity_update", "app/tests/test_onboarding.py::TestOnboardingAPI::test_check_subdomain_api_available", "app/tests/test_onboarding.py::TestOnboardingAPI::test_health_check_api", "app/tests/test_onboarding.py::TestOnboardingAPI::test_register_school_api_success", "app/tests/test_onboarding.py::TestOnboardingAPI::test_register_school_api_validation_error", "app/tests/test_onboarding.py::TestOnboardingAPI::test_subdomain_suggestions_api", "app/tests/test_onboarding.py::TestOnboardingService::test_academic_year_calculation_april_onwards", "app/tests/test_onboarding.py::TestOnboardingService::test_academic_year_calculation_january_march", "app/tests/test_onboarding.py::TestOnboardingService::test_check_subdomain_availability_available", "app/tests/test_onboarding.py::TestOnboardingService::test_check_subdomain_availability_taken", "app/tests/test_onboarding.py::TestOnboardingService::test_generate_school_code_unique", "app/tests/test_onboarding.py::TestOnboardingService::test_get_subdomain_suggestions", "app/tests/test_onboarding.py::TestOnboardingService::test_register_school_duplicate_email_error", "app/tests/test_onboarding.py::TestOnboardingService::test_register_school_empty_school_name_error", "app/tests/test_onboarding.py::TestOnboardingService::test_register_school_invalid_email_error", "app/tests/test_onboarding.py::TestOnboardingService::test_register_school_invalid_subdomain_error", "app/tests/test_onboarding.py::TestOnboardingService::test_register_school_multi_branch_success", "app/tests/test_onboarding.py::TestOnboardingService::test_register_school_single_branch_success", "app/tests/test_onboarding.py::TestOnboardingService::test_register_school_weak_password_error", "app/tests/test_onboarding.py::TestOnboardingService::test_trial_license_assignment", "app/tests/test_subdomain.py::TestSubdomainAPI::test_get_reserved_subdomains_api", "app/tests/test_subdomain.py::TestSubdomainAPI::test_get_subdomain_info_api", "app/tests/test_subdomain.py::TestSubdomainAPI::test_get_subdomain_suggestions_api", "app/tests/test_subdomain.py::TestSubdomainAPI::test_reserve_subdomain_api_success", "app/tests/test_subdomain.py::TestSubdomainAPI::test_reserve_subdomain_api_unauthorized", "app/tests/test_subdomain.py::TestSubdomainAPI::test_validate_subdomain_api_invalid", "app/tests/test_subdomain.py::TestSubdomainAPI::test_validate_subdomain_api_success", "app/tests/test_subdomain.py::TestSubdomainAPI::test_validate_subdomain_api_taken", "app/tests/test_subdomain.py::TestSubdomainAvailability::test_check_availability_available", "app/tests/test_subdomain.py::TestSubdomainAvailability::test_check_availability_invalid_format", "app/tests/test_subdomain.py::TestSubdomainAvailability::test_check_availability_reserved", "app/tests/test_subdomain.py::TestSubdomainAvailability::test_check_availability_taken", "app/tests/test_subdomain.py::TestSubdomainPerformance::test_availability_check_performance", "app/tests/test_subdomain.py::TestSubdomainPerformance::test_subdomain_validation_performance", "app/tests/test_subdomain.py::TestSubdomainReservation::test_reserve_subdomain_invalid", "app/tests/test_subdomain.py::TestSubdomainReservation::test_reserve_subdomain_success", "app/tests/test_subdomain.py::TestSubdomainReservation::test_reserve_subdomain_taken", "app/tests/test_subdomain.py::TestSubdomainResolution::test_resolve_subdomain_inactive_school", "app/tests/test_subdomain.py::TestSubdomainResolution::test_resolve_subdomain_not_found", "app/tests/test_subdomain.py::TestSubdomainResolution::test_resolve_subdomain_success", "app/tests/test_subdomain.py::TestSubdomainSuggestions::test_generate_suggestions_basic", "app/tests/test_subdomain.py::TestSubdomainSuggestions::test_generate_suggestions_with_numbers", "app/tests/test_subdomain.py::TestSubdomainSuggestions::test_generate_suggestions_with_variations", "app/tests/test_subdomain.py::TestSubdomainValidation::test_validate_subdomain_invalid_format", "app/tests/test_subdomain.py::TestSubdomainValidation::test_validate_subdomain_reserved", "app/tests/test_subdomain.py::TestSubdomainValidation::test_validate_subdomain_valid"]