{"app/tests/test_models.py::TestOrganizationModel::test_organization_unique_subdomain": true, "app/tests/test_audit.py::TestAuditService::test_log_audit_event_basic": true, "app/tests/test_audit.py::TestAuditService::test_log_audit_event_with_changes": true, "app/tests/test_audit.py::TestAuditService::test_log_security_event": true, "app/tests/test_audit.py::TestAuditService::test_log_critical_security_event": true, "app/tests/test_audit.py::TestAuditService::test_get_audit_trail": true, "app/tests/test_audit.py::TestAuditService::test_get_user_activity": true, "app/tests/test_audit.py::TestAuditService::test_get_security_events": true, "app/tests/test_audit.py::TestAuditFiltering::test_filter_by_date_range": true, "app/tests/test_audit.py::TestAuditFiltering::test_filter_by_action": true, "app/tests/test_audit.py::TestAuditFiltering::test_filter_by_severity": true, "app/tests/test_audit.py::TestAuditCompliance::test_audit_log_immutability": true, "app/tests/test_audit.py::TestAuditCompliance::test_audit_log_retention": true, "app/tests/test_audit.py::TestAuditIntegration::test_audit_with_authentication": true, "app/tests/test_auth.py::TestPasswordManager::test_validate_password_strength_valid": true, "app/tests/test_auth.py::TestPasswordManager::test_validate_password_strength_invalid": true, "app/tests/test_auth.py::TestAuthService::test_authenticate_user_success": true, "app/tests/test_auth.py::TestAuthService::test_authenticate_user_inactive_account": true, "app/tests/test_auth.py::TestAuthService::test_authenticate_user_locked_account": true, "app/tests/test_auth.py::TestAuthService::test_change_password_success": true, "app/tests/test_auth.py::TestAuthService::test_change_password_invalid_current": true, "app/tests/test_auth.py::TestAuthAPI::test_login_success": true, "app/tests/test_auth.py::TestAuthAPI::test_login_invalid_credentials": true, "app/tests/test_auth.py::TestAuthAPI::test_login_missing_subdomain": true, "app/tests/test_auth.py::TestAuthAPI::test_change_password_success": true, "app/tests/test_models.py::TestSchoolModel::test_school_unique_constraints": true, "app/tests/test_models.py::TestPermissionModel::test_permission_unique_code": true, "app/tests/test_models.py::TestUserSessionModel::test_create_user_session": true, "app/tests/test_models.py::TestUserSessionModel::test_session_activity_update": true, "app/tests/test_onboarding.py::TestOnboardingService::test_register_school_single_branch_success": true, "app/tests/test_onboarding.py::TestOnboardingService::test_register_school_multi_branch_success": true, "app/tests/test_onboarding.py::TestOnboardingService::test_register_school_duplicate_email_error": true, "app/tests/test_onboarding.py::TestOnboardingService::test_register_school_invalid_subdomain_error": true, "app/tests/test_onboarding.py::TestOnboardingService::test_register_school_weak_password_error": true, "app/tests/test_onboarding.py::TestOnboardingService::test_check_subdomain_availability_taken": true, "app/tests/test_onboarding.py::TestOnboardingService::test_academic_year_calculation_april_onwards": true, "app/tests/test_onboarding.py::TestOnboardingService::test_academic_year_calculation_january_march": true, "app/tests/test_onboarding.py::TestOnboardingAPI::test_register_school_api_success": true, "app/tests/test_onboarding.py::TestOnboardingAPI::test_register_school_api_validation_error": true, "app/tests/test_onboarding.py::TestOnboardingAPI::test_check_subdomain_api_available": true, "app/tests/test_onboarding.py::TestOnboardingAPI::test_subdomain_suggestions_api": true, "app/tests/test_onboarding.py::TestOnboardingAPI::test_health_check_api": true, "app/tests/test_subdomain.py::TestSubdomainValidation::test_validate_subdomain_valid": true, "app/tests/test_subdomain.py::TestSubdomainValidation::test_validate_subdomain_invalid_format": true, "app/tests/test_subdomain.py::TestSubdomainValidation::test_validate_subdomain_reserved": true, "app/tests/test_subdomain.py::TestSubdomainAvailability::test_check_availability_available": true, "app/tests/test_subdomain.py::TestSubdomainAvailability::test_check_availability_taken": true, "app/tests/test_subdomain.py::TestSubdomainAvailability::test_check_availability_invalid_format": true, "app/tests/test_subdomain.py::TestSubdomainAvailability::test_check_availability_reserved": true, "app/tests/test_subdomain.py::TestSubdomainSuggestions::test_generate_suggestions_basic": true, "app/tests/test_subdomain.py::TestSubdomainSuggestions::test_generate_suggestions_with_numbers": true, "app/tests/test_subdomain.py::TestSubdomainSuggestions::test_generate_suggestions_with_variations": true, "app/tests/test_subdomain.py::TestSubdomainResolution::test_resolve_subdomain_success": true, "app/tests/test_subdomain.py::TestSubdomainResolution::test_resolve_subdomain_inactive_school": true, "app/tests/test_subdomain.py::TestSubdomainReservation::test_reserve_subdomain_success": true, "app/tests/test_subdomain.py::TestSubdomainReservation::test_reserve_subdomain_invalid": true, "app/tests/test_subdomain.py::TestSubdomainReservation::test_reserve_subdomain_taken": true, "app/tests/test_subdomain.py::TestSubdomainAPI::test_validate_subdomain_api_success": true, "app/tests/test_subdomain.py::TestSubdomainAPI::test_validate_subdomain_api_invalid": true, "app/tests/test_subdomain.py::TestSubdomainAPI::test_validate_subdomain_api_taken": true, "app/tests/test_subdomain.py::TestSubdomainAPI::test_get_reserved_subdomains_api": true, "app/tests/test_subdomain.py::TestSubdomainAPI::test_get_subdomain_suggestions_api": true, "app/tests/test_subdomain.py::TestSubdomainAPI::test_get_subdomain_info_api": true, "app/tests/test_subdomain.py::TestSubdomainAPI::test_reserve_subdomain_api_success": true, "app/tests/test_academic.py::TestAcademicYearModel::test_academic_year_unique_constraints": true, "app/tests/test_academic.py::TestAcademicYearService::test_create_academic_year_success": true, "app/tests/test_academic.py::TestAcademicYearService::test_create_academic_year_with_auto_activate": true, "app/tests/test_academic.py::TestAcademicYearService::test_create_academic_year_validation_errors": true, "app/tests/test_academic.py::TestAcademicYearService::test_get_academic_year": true, "app/tests/test_academic.py::TestAcademicYearService::test_get_active_academic_year": true, "app/tests/test_academic.py::TestAcademicYearService::test_list_academic_years": true, "app/tests/test_academic.py::TestAcademicYearService::test_activate_academic_year": true, "app/tests/test_academic.py::TestAcademicYearService::test_complete_academic_year": true, "app/tests/test_academic.py::TestAcademicYearService::test_delete_academic_year": true, "app/tests/test_academic.py::TestAcademicYearService::test_get_current_indian_academic_year": true, "app/tests/test_academic.py::TestSubjectService::test_create_subject_success": true, "app/tests/test_academic.py::TestSubjectService::test_create_subject_duplicate_code": true, "app/tests/test_academic.py::TestSubjectService::test_list_subjects": true, "app/tests/test_academic.py::TestSubjectService::test_update_subject": true, "app/tests/test_academic.py::TestSubjectService::test_delete_subject": true}