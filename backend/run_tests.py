#!/usr/bin/env python3
"""
Test runner script for School ERP Backend
Production-ready test execution with comprehensive reporting
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))


def run_tests(test_type="all", verbose=False, coverage=True, parallel=False):
    """Run tests with specified configuration."""
    
    # Base pytest command
    cmd = ["python", "-m", "pytest"]
    
    # Add test path
    cmd.append("app/tests")
    
    # Test type selection
    if test_type == "unit":
        cmd.extend(["-m", "unit"])
    elif test_type == "integration":
        cmd.extend(["-m", "integration"])
    elif test_type == "api":
        cmd.extend(["-m", "api"])
    elif test_type == "auth":
        cmd.extend(["-m", "auth"])
    elif test_type == "audit":
        cmd.extend(["-m", "audit"])
    elif test_type == "subdomain":
        cmd.extend(["-m", "subdomain"])
    elif test_type == "performance":
        cmd.extend(["-m", "performance"])
    elif test_type == "slow":
        cmd.extend(["-m", "slow"])
    elif test_type != "all":
        print(f"Unknown test type: {test_type}")
        return False
    
    # Verbosity
    if verbose:
        cmd.append("-v")
    else:
        cmd.append("-q")
    
    # Coverage
    if coverage:
        cmd.extend([
            "--cov=app",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov",
            "--cov-fail-under=80"
        ])
    
    # Parallel execution
    if parallel:
        cmd.extend(["-n", "auto"])
    
    # Additional options
    cmd.extend([
        "--tb=short",
        "--strict-markers",
        "--strict-config"
    ])
    
    print(f"Running command: {' '.join(cmd)}")
    print("-" * 80)
    
    # Run tests
    try:
        result = subprocess.run(cmd, cwd=os.path.dirname(__file__))
        return result.returncode == 0
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
        return False
    except Exception as e:
        print(f"Error running tests: {e}")
        return False


def setup_test_database():
    """Setup test database."""
    print("Setting up test database...")
    
    # Create test database if it doesn't exist
    try:
        import psycopg2
        from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
        
        # Connect to PostgreSQL server
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            user="school_erp_user",
            password="school_erp_password",
            database="postgres"
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        
        cursor = conn.cursor()
        
        # Check if test database exists
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = 'school_erp_test'")
        exists = cursor.fetchone()
        
        if not exists:
            cursor.execute("CREATE DATABASE school_erp_test")
            print("✅ Test database created")
        else:
            print("✅ Test database already exists")
        
        cursor.close()
        conn.close()
        
    except ImportError:
        print("⚠️  psycopg2 not installed, skipping database setup")
    except Exception as e:
        print(f"⚠️  Could not setup test database: {e}")
        print("Please ensure PostgreSQL is running and accessible")


def cleanup_test_data():
    """Cleanup test data and temporary files."""
    print("Cleaning up test data...")
    
    # Remove coverage files
    coverage_files = ["htmlcov", ".coverage"]
    for file_path in coverage_files:
        if os.path.exists(file_path):
            if os.path.isdir(file_path):
                import shutil
                shutil.rmtree(file_path)
            else:
                os.remove(file_path)
    
    # Remove pytest cache
    pytest_cache = ".pytest_cache"
    if os.path.exists(pytest_cache):
        import shutil
        shutil.rmtree(pytest_cache)
    
    print("✅ Cleanup completed")


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="School ERP Backend Test Runner")
    
    parser.add_argument(
        "--type", "-t",
        choices=["all", "unit", "integration", "api", "auth", "audit", "subdomain", "performance", "slow"],
        default="all",
        help="Type of tests to run"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--no-coverage",
        action="store_true",
        help="Disable coverage reporting"
    )
    
    parser.add_argument(
        "--parallel", "-p",
        action="store_true",
        help="Run tests in parallel"
    )
    
    parser.add_argument(
        "--setup-db",
        action="store_true",
        help="Setup test database before running tests"
    )
    
    parser.add_argument(
        "--cleanup",
        action="store_true",
        help="Cleanup test data and temporary files"
    )
    
    args = parser.parse_args()
    
    # Setup database if requested
    if args.setup_db:
        setup_test_database()
    
    # Cleanup if requested
    if args.cleanup:
        cleanup_test_data()
        return
    
    # Run tests
    success = run_tests(
        test_type=args.type,
        verbose=args.verbose,
        coverage=not args.no_coverage,
        parallel=args.parallel
    )
    
    if success:
        print("\n✅ All tests passed!")
        
        # Show coverage report location
        if not args.no_coverage and os.path.exists("htmlcov/index.html"):
            print(f"📊 Coverage report: file://{os.path.abspath('htmlcov/index.html')}")
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
