# School ERP Backend Environment Configuration
# Copy this file to .env and update values as needed

# Environment
ENVIRONMENT=development
DEBUG=true

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database
POSTGRES_SERVER=localhost
POSTGRES_USER=school_erp_user
POSTGRES_PASSWORD=school_erp_password
POSTGRES_DB=school_erp_dev
POSTGRES_PORT=5432

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Domain Configuration (Development)
PRIMARY_DOMAIN=myschoolerp.local
SUBDOMAIN_PATTERN={subdomain}.myschoolerp.local

# File Storage
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# Email (Placeholder)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
EMAILS_FROM_EMAIL=
EMAILS_FROM_NAME=

# Licensing
DEFAULT_TRIAL_DAYS=14
