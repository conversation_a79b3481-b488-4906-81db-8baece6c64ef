[tool:pytest]
# Pytest configuration for School ERP Backend
testpaths = app/tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Test discovery patterns
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80
    --asyncio-mode=auto

# Markers for test categorization
markers =
    unit: Unit tests (fast, isolated)
    integration: Integration tests (database, external services)
    api: API endpoint tests
    auth: Authentication and authorization tests
    audit: Audit system tests
    subdomain: Subdomain management tests
    onboarding: User onboarding flow tests
    rbac: Role-based access control tests
    performance: Performance and load tests
    slow: Slow running tests
    external: Tests requiring external services

# Test environment
env =
    ENVIRONMENT = testing
    DEBUG = true
    TESTING = true
    DATABASE_URL = **************************************************************/school_erp_test
    REDIS_URL = redis://redis:6379/1
    SECRET_KEY = test-secret-key-for-testing-only
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    REFRESH_TOKEN_EXPIRE_DAYS = 7

# Async test configuration
asyncio_mode = auto

# Warnings configuration
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore:.*unclosed.*:ResourceWarning

# Minimum Python version
minversion = 3.9
