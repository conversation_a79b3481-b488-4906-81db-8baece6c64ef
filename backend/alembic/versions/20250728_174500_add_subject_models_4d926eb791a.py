"""Add subject and class_subject models

Revision ID: 4d926eb791a
Revises: 3c825da680f
Create Date: 2025-07-28T17:45:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4d926eb791a'
down_revision = '3c825da680f'
branch_labels = None
depends_on = None


def upgrade():
    """Create subject and class_subject tables"""
    
    # Create subjects table
    op.create_table('subjects',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('school_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('subject_code', sa.String(length=20), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('subject_type', sa.String(length=30), nullable=False),
        sa.Column('academic_level', sa.String(length=30), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('display_order', sa.Integer(), nullable=False),
        sa.Column('default_credits', sa.Integer(), nullable=True),
        sa.Column('default_hours_per_week', sa.Integer(), nullable=True),
        sa.Column('settings', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('approved_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('approved_at', sa.Date(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('deletion_reason', sa.String(length=255), nullable=True),
        sa.Column('audit_log', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['approved_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['school_id'], ['schools.id'], ),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('school_id', 'subject_code', name='uq_subject_code_per_school'),
        sa.UniqueConstraint('school_id', 'name', name='uq_subject_name_per_school'),
        sa.CheckConstraint("subject_type IN ('core', 'elective', 'extra_curricular')", name='ck_subject_type_valid'),
        sa.CheckConstraint("academic_level IS NULL OR academic_level IN ('primary', 'secondary', 'higher_secondary')", name='ck_academic_level_valid'),
        sa.CheckConstraint("default_credits IS NULL OR default_credits > 0", name='ck_default_credits_positive'),
        sa.CheckConstraint("default_hours_per_week IS NULL OR default_hours_per_week > 0", name='ck_default_hours_positive')
    )
    
    # Create indexes for subjects table
    op.create_index('ix_subjects_school_active', 'subjects', ['school_id', 'is_active'])
    op.create_index('ix_subjects_type_level', 'subjects', ['subject_type', 'academic_level'])
    op.create_index('ix_subjects_display_order', 'subjects', ['display_order'])
    op.create_index('ix_subjects_subject_code', 'subjects', ['subject_code'])
    op.create_index('ix_subjects_name', 'subjects', ['name'])
    op.create_index('ix_subjects_is_active', 'subjects', ['is_active'])
    
    # Create class_subjects table
    op.create_table('class_subjects',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('school_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('academic_year_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('class_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('subject_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('teacher_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('is_mandatory', sa.Boolean(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('credits', sa.Integer(), nullable=True),
        sa.Column('hours_per_week', sa.Integer(), nullable=True),
        sa.Column('max_marks', sa.Integer(), nullable=True),
        sa.Column('pass_marks', sa.Integer(), nullable=True),
        sa.Column('display_order', sa.Integer(), nullable=False),
        sa.Column('settings', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('assigned_at', sa.Date(), nullable=True),
        sa.Column('assigned_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('updated_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('version', sa.Integer(), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('deleted_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('deletion_reason', sa.String(length=255), nullable=True),
        sa.Column('audit_log', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['academic_year_id'], ['academic_years.id'], ),
        sa.ForeignKeyConstraint(['assigned_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['class_id'], ['classes.id'], ),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['deleted_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['school_id'], ['schools.id'], ),
        sa.ForeignKeyConstraint(['subject_id'], ['subjects.id'], ),
        sa.ForeignKeyConstraint(['teacher_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('school_id', 'academic_year_id', 'class_id', 'subject_id', name='uq_class_subject_per_academic_year'),
        sa.CheckConstraint("credits IS NULL OR credits > 0", name='ck_credits_positive'),
        sa.CheckConstraint("hours_per_week IS NULL OR hours_per_week > 0", name='ck_hours_per_week_positive'),
        sa.CheckConstraint("max_marks IS NULL OR max_marks > 0", name='ck_max_marks_positive'),
        sa.CheckConstraint("pass_marks IS NULL OR pass_marks >= 0", name='ck_pass_marks_non_negative'),
        sa.CheckConstraint("pass_marks IS NULL OR max_marks IS NULL OR pass_marks <= max_marks", name='ck_pass_marks_not_exceed_max')
    )
    
    # Create indexes for class_subjects table
    op.create_index('ix_class_subjects_academic_year_class', 'class_subjects', ['academic_year_id', 'class_id'])
    op.create_index('ix_class_subjects_teacher', 'class_subjects', ['teacher_id'])
    op.create_index('ix_class_subjects_active', 'class_subjects', ['school_id', 'is_active'])
    op.create_index('ix_class_subjects_mandatory', 'class_subjects', ['is_mandatory'])
    op.create_index('ix_class_subjects_display_order', 'class_subjects', ['display_order'])
    op.create_index('ix_class_subjects_academic_year_id', 'class_subjects', ['academic_year_id'])
    op.create_index('ix_class_subjects_class_id', 'class_subjects', ['class_id'])
    op.create_index('ix_class_subjects_subject_id', 'class_subjects', ['subject_id'])
    op.create_index('ix_class_subjects_is_active', 'class_subjects', ['is_active'])


def downgrade():
    """Drop subject and class_subject tables"""
    
    # Drop class_subjects table and its indexes
    op.drop_index('ix_class_subjects_is_active', table_name='class_subjects')
    op.drop_index('ix_class_subjects_subject_id', table_name='class_subjects')
    op.drop_index('ix_class_subjects_class_id', table_name='class_subjects')
    op.drop_index('ix_class_subjects_academic_year_id', table_name='class_subjects')
    op.drop_index('ix_class_subjects_display_order', table_name='class_subjects')
    op.drop_index('ix_class_subjects_mandatory', table_name='class_subjects')
    op.drop_index('ix_class_subjects_active', table_name='class_subjects')
    op.drop_index('ix_class_subjects_teacher', table_name='class_subjects')
    op.drop_index('ix_class_subjects_academic_year_class', table_name='class_subjects')
    op.drop_table('class_subjects')
    
    # Drop subjects table and its indexes
    op.drop_index('ix_subjects_is_active', table_name='subjects')
    op.drop_index('ix_subjects_name', table_name='subjects')
    op.drop_index('ix_subjects_subject_code', table_name='subjects')
    op.drop_index('ix_subjects_display_order', table_name='subjects')
    op.drop_index('ix_subjects_type_level', table_name='subjects')
    op.drop_index('ix_subjects_school_active', table_name='subjects')
    op.drop_table('subjects')
