"""
Base database models for School ERP
Includes multi-tenant support and localization fields
"""

from datetime import datetime
from typing import Any, Dict
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Boolean, Text, JSON
from sqlalchemy.ext.declarative import declarative_base, declared_attr
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import uuid

from app.core.config import settings


class BaseModel:
    """Base model with common fields for all tables"""
    
    @declared_attr
    def __tablename__(cls):
        """Generate table name from class name"""
        return cls.__name__.lower()
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Audit fields
    created_by = Column(UUID(as_uuid=True), nullable=True, index=True)
    updated_by = Column(UUID(as_uuid=True), nullable=True, index=True)

    # Version for optimistic locking
    version = Column(Integer, default=1, nullable=False)

    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }


class MultiTenantMixin:
    """Mixin for multi-tenant models with school isolation"""
    
    # Multi-tenant isolation
    school_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    @declared_attr
    def __table_args__(cls):
        """Add composite indexes for multi-tenant queries"""
        from sqlalchemy import Index
        indexes = [
            # Basic multi-tenant index
            Index(f"idx_{cls.__tablename__}_school_id_created_at", "school_id", "created_at"),
        ]

        # Add soft delete index only if the model has is_deleted column
        if hasattr(cls, 'is_deleted'):
            indexes.append(
                Index(f"idx_{cls.__tablename__}_school_id_is_deleted", "school_id", "is_deleted")
            )

        return tuple(indexes)


class LocalizationMixin:
    """Mixin for models that need localization support"""
    
    # Localization preferences
    language = Column(String(5), default=settings.DEFAULT_LANGUAGE, nullable=False)
    timezone = Column(String(50), default=settings.DEFAULT_TIMEZONE, nullable=False)
    currency = Column(String(3), default=settings.DEFAULT_CURRENCY, nullable=False)
    date_format = Column(String(20), default=settings.DEFAULT_DATE_FORMAT, nullable=False)
    time_format = Column(String(20), default=settings.DEFAULT_TIME_FORMAT, nullable=False)
    
    # Localized content storage (JSONB for flexible multi-language content)
    localized_content = Column(JSON, nullable=True, comment="Multi-language content storage")
    
    def get_localized_field(self, field_name: str, language: str = None) -> str:
        """Get localized version of a field"""
        if not self.localized_content:
            return getattr(self, field_name, "")
        
        lang = language or self.language
        localized = self.localized_content.get(field_name, {})
        
        # Return localized version if available, otherwise default
        return localized.get(lang, getattr(self, field_name, ""))
    
    def set_localized_field(self, field_name: str, value: str, language: str = None):
        """Set localized version of a field"""
        lang = language or self.language
        
        if not self.localized_content:
            self.localized_content = {}
        
        if field_name not in self.localized_content:
            self.localized_content[field_name] = {}
        
        self.localized_content[field_name][lang] = value


class SoftDeleteMixin:
    """Mixin for models that require soft deletion (critical data only)"""

    # Soft delete fields
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    deleted_by = Column(UUID(as_uuid=True), nullable=True)
    deletion_reason = Column(String(255), nullable=True)

    def soft_delete(self, deleted_by: uuid.UUID = None, reason: str = None):
        """Soft delete the record with audit trail"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
        self.deleted_by = deleted_by
        self.deletion_reason = reason
        self.version += 1

    def restore(self, restored_by: uuid.UUID = None):
        """Restore soft-deleted record"""
        self.is_deleted = False
        self.deleted_at = None
        self.deleted_by = None
        self.deletion_reason = None
        self.version += 1

        # Log restoration in audit
        if hasattr(self, 'add_light_audit_entry'):
            self.add_light_audit_entry(
                action="restored",
                changes={"is_deleted": [True, False]},
                user_id=restored_by
            )

    @property
    def is_active(self) -> bool:
        """Check if record is active (not soft deleted)"""
        return not self.is_deleted


class LightAuditMixin:
    """Mixin for lightweight per-model audit logging (optional)"""

    # Light audit trail (last 20 entries max)
    audit_log = Column(JSON, nullable=True, comment="Light audit trail (last 20 entries)")

    def add_light_audit_entry(self, action: str, changes: Dict[str, Any], user_id: uuid.UUID = None):
        """Add entry to light audit log with size limit"""
        if not self.audit_log:
            self.audit_log = []

        entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "action": action,
            "changes": changes,
            "user_id": str(user_id) if user_id else None,
            "version": self.version
        }

        self.audit_log.append(entry)

        # Keep only last 20 entries for performance
        if len(self.audit_log) > 20:
            self.audit_log = self.audit_log[-20:]

        self.version += 1


# Create the base class with all mixins
Base = declarative_base(cls=BaseModel)


class Organization(Base, LocalizationMixin, SoftDeleteMixin, LightAuditMixin):
    """Organization model for multi-branch school management"""
    
    __tablename__ = "organizations"
    
    # Basic information
    name = Column(String(255), nullable=False, index=True)
    display_name = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)

    # Organization identification
    subdomain = Column(String(63), nullable=True, unique=True, index=True)  # For multi-tenant routing
    
    # Contact information
    email = Column(String(255), nullable=True, index=True)
    phone = Column(String(20), nullable=True)
    website = Column(String(255), nullable=True)
    
    # Address
    address_line1 = Column(String(255), nullable=True)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), default="India", nullable=False)
    postal_code = Column(String(20), nullable=True)
    
    # Organization type
    org_type = Column(String(50), default="school", nullable=False)  # school, trust, company
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    
    # Branding and customization
    logo_url = Column(String(500), nullable=True)
    primary_color = Column(String(7), nullable=True)  # Hex color code
    secondary_color = Column(String(7), nullable=True)
    theme_config = Column(JSON, nullable=True, comment="Custom theme configuration")
    
    # Settings
    settings = Column(JSON, nullable=True, comment="Organization-wide settings")


class School(Base, MultiTenantMixin, LocalizationMixin, SoftDeleteMixin, LightAuditMixin):
    """School model with multi-tenant support"""
    
    __tablename__ = "schools"
    
    # Organization relationship
    organization_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Basic information
    name = Column(String(255), nullable=False, index=True)
    display_name = Column(String(255), nullable=True)
    short_name = Column(String(50), nullable=True)
    description = Column(Text, nullable=True)
    
    # School identification
    school_code = Column(String(50), nullable=True, unique=True, index=True)
    subdomain = Column(String(63), nullable=True, unique=True, index=True)  # For multi-tenant routing
    registration_number = Column(String(100), nullable=True)
    affiliation_number = Column(String(100), nullable=True)
    
    # Educational details
    board_type = Column(String(50), nullable=True)  # CBSE, ICSE, State Board
    school_type = Column(String(50), default="school", nullable=False)  # school, college, coaching
    establishment_year = Column(Integer, nullable=True)
    
    # Contact information
    email = Column(String(255), nullable=True, index=True)
    phone = Column(String(20), nullable=True)
    website = Column(String(255), nullable=True)
    
    # Address
    address_line1 = Column(String(255), nullable=True)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), default="India", nullable=False)
    postal_code = Column(String(20), nullable=True)
    
    # Subdomain and branding
    subdomain = Column(String(63), nullable=False, unique=True, index=True)
    logo_url = Column(String(500), nullable=True)
    favicon_url = Column(String(500), nullable=True)
    primary_color = Column(String(7), nullable=True)
    secondary_color = Column(String(7), nullable=True)
    theme_config = Column(JSON, nullable=True, comment="School-specific theme")
    
    # Status and configuration
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # School-specific settings
    settings = Column(JSON, nullable=True, comment="School-specific configuration")
    
    # Academic configuration
    academic_year_start_month = Column(Integer, default=4, nullable=False)  # April for Indian schools
    working_days = Column(JSON, nullable=True, comment="Working days configuration")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Set school_id to self.id for the school record itself
        if not self.school_id:
            self.school_id = self.id


# Note: We'll add more models (User, AcademicYear, Class, Section, etc.) in subsequent files
# This base.py establishes the foundation with multi-tenancy and localization support
