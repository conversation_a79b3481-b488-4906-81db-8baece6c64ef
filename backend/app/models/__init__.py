# SQLAlchemy database models

# Import all models to ensure they are registered with SQLAlchemy
from app.models.base import Base, Organization, School
from app.models.auth import User, Role, Permission, UserRole, RolePermission, UserSession, APIKey
from app.models.audit import AuditLog, DataRetentionPolicy, ArchivedData
from app.models.academic import AcademicYear, Class, Section, ClassSectionMapping, Subject, ClassSubject

# Export all models for easy importing
__all__ = [
    # Base models
    'Base', 'Organization', 'School',

    # Authentication models
    'User', 'Role', 'Permission', 'UserRole', 'RolePermission', 'UserSession', 'APIKey',

    # Audit models
    'AuditLog', 'DataRetentionPolicy', 'ArchivedData',

    # Academic models
    'AcademicYear', 'Class', 'Section', 'ClassSectionMapping', 'Subject', 'ClassSubject',
]
