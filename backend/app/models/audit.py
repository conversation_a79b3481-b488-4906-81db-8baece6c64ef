"""
Audit and compliance models for School ERP
Global audit logging and data lifecycle management
"""

from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy import Column, String, DateTime, Text, Index, Integer, Boolean
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid

from app.models.base import Base, MultiTenantMixin
from app.core.logging import get_logger

logger = get_logger(__name__)


class AuditLog(Base, MultiTenantMixin):
    """
    Global audit log for critical system events and compliance tracking
    Centralized logging for all important actions across the platform
    """
    
    __tablename__ = "audit_logs"
    
    # Event Information
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True)  # Can be null for system events
    module = Column(String(100), nullable=False, index=True)  # auth, student, fee, attendance, etc.
    action = Column(String(100), nullable=False, index=True)  # login, create, update, delete, etc.
    resource_type = Column(String(100), nullable=True, index=True)  # user, student, fee_record, etc.
    resource_id = Column(UUID(as_uuid=True), nullable=True, index=True)  # ID of affected resource
    
    # Event Details
    event_data = Column(JSONB, nullable=True, comment="Detailed event information")
    changes = Column(JSONB, nullable=True, comment="Before/after values for updates")
    event_metadata = Column(JSONB, nullable=True, comment="Additional context (IP, user agent, etc.)")
    
    # Event Context
    session_id = Column(String(255), nullable=True, index=True)
    correlation_id = Column(String(255), nullable=True, index=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(Text, nullable=True)
    
    # Event Classification
    severity = Column(String(20), default="info", nullable=False, index=True)  # info, warning, error, critical
    category = Column(String(50), nullable=False, index=True)  # security, data, system, compliance
    
    # Compliance and Retention
    retention_policy = Column(String(50), default="standard", nullable=False)  # standard, extended, permanent
    compliance_flags = Column(JSONB, nullable=True, comment="GDPR, DPDPA, etc. compliance markers")
    
    # Performance Indexes
    __table_args__ = (
        # Composite indexes for efficient querying
        Index('idx_audit_logs_school_module_action', 'school_id', 'module', 'action'),
        Index('idx_audit_logs_user_created', 'user_id', 'created_at'),
        Index('idx_audit_logs_resource', 'resource_type', 'resource_id'),
        Index('idx_audit_logs_severity_category', 'severity', 'category'),
        Index('idx_audit_logs_compliance', 'school_id', 'category', 'created_at'),
    )
    
    @classmethod
    def log_event(
        cls,
        school_id: uuid.UUID,
        module: str,
        action: str,
        user_id: Optional[uuid.UUID] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[uuid.UUID] = None,
        event_data: Optional[Dict[str, Any]] = None,
        changes: Optional[Dict[str, Any]] = None,
        event_metadata: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        severity: str = "info",
        category: str = "data",
        retention_policy: str = "standard"
    ) -> 'AuditLog':
        """
        Create audit log entry with comprehensive information
        """
        audit_entry = cls(
            school_id=school_id,
            user_id=user_id,
            module=module,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            event_data=event_data,
            changes=changes,
            event_metadata=event_metadata,
            session_id=session_id,
            correlation_id=correlation_id,
            ip_address=ip_address,
            user_agent=user_agent,
            severity=severity,
            category=category,
            retention_policy=retention_policy
        )
        
        return audit_entry


class DataRetentionPolicy(Base, MultiTenantMixin):
    """
    Data retention and archival policies per school
    Configurable retention periods for different data types
    """
    
    __tablename__ = "data_retention_policies"
    
    # Policy Information
    policy_name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Retention Configuration
    data_type = Column(String(100), nullable=False, index=True)  # audit_logs, deleted_users, etc.
    retention_days = Column(Integer, default=180, nullable=False)  # 6 months default
    archive_after_days = Column(Integer, nullable=True)  # Move to archive storage
    hard_delete_after_days = Column(Integer, nullable=True)  # Permanent deletion
    
    # Policy Status
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_system_policy = Column(Boolean, default=False, nullable=False)  # Cannot be modified
    
    # Compliance Settings
    compliance_requirements = Column(JSONB, nullable=True, comment="GDPR, DPDPA requirements")
    
    # Policy Metadata
    last_cleanup_run = Column(DateTime(timezone=True), nullable=True)
    next_cleanup_due = Column(DateTime(timezone=True), nullable=True)
    
    @classmethod
    def get_retention_days(cls, school_id: uuid.UUID, data_type: str) -> int:
        """Get retention period for specific data type"""
        # This would query the database for school-specific policy
        # For now, return default values based on data type
        defaults = {
            "audit_logs": 365,  # 1 year for audit logs
            "deleted_users": 180,  # 6 months for soft-deleted users
            "deleted_students": 365,  # 1 year for students (compliance)
            "financial_records": 2555,  # 7 years for financial data
            "session_logs": 90,  # 3 months for session data
        }
        return defaults.get(data_type, 180)  # Default 6 months


class ArchivedData(Base, MultiTenantMixin):
    """
    Archived data storage for compliance and recovery
    Cold storage for data that's past active retention period
    """
    
    __tablename__ = "archived_data"
    
    # Archive Information
    original_table = Column(String(100), nullable=False, index=True)
    original_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    archive_reason = Column(String(100), nullable=False, index=True)  # retention, deletion, compliance
    
    # Archived Content
    archived_data = Column(JSONB, nullable=False, comment="Complete original record")
    archive_metadata = Column(JSONB, nullable=True, comment="Archive metadata and context")
    
    # Archive Lifecycle
    archived_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    archived_by = Column(UUID(as_uuid=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)  # When to permanently delete
    
    # Compliance and Access
    access_log = Column(JSONB, nullable=True, comment="Who accessed archived data when")
    compliance_hold = Column(Boolean, default=False, nullable=False)  # Legal hold prevents deletion
    
    # Performance Indexes
    __table_args__ = (
        Index('idx_archived_data_original', 'original_table', 'original_id'),
        Index('idx_archived_data_school_archived', 'school_id', 'archived_at'),
        Index('idx_archived_data_expires', 'expires_at'),
    )
    
    @classmethod
    def archive_record(
        cls,
        school_id: uuid.UUID,
        original_table: str,
        original_id: uuid.UUID,
        record_data: Dict[str, Any],
        archive_reason: str = "retention",
        archived_by: Optional[uuid.UUID] = None,
        expires_at: Optional[datetime] = None,
        archive_metadata: Optional[Dict[str, Any]] = None
    ) -> 'ArchivedData':
        """
        Archive a record for compliance and recovery
        """
        archive_entry = cls(
            school_id=school_id,
            original_table=original_table,
            original_id=original_id,
            archive_reason=archive_reason,
            archived_data=record_data,
            archived_by=archived_by,
            expires_at=expires_at,
            archive_metadata=archive_metadata or {}
        )
        
        return archive_entry
    
    def log_access(self, accessed_by: uuid.UUID, access_reason: str = "view"):
        """Log access to archived data for compliance"""
        if not self.access_log:
            self.access_log = []
        
        access_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "accessed_by": str(accessed_by),
            "reason": access_reason,
            "ip_address": None  # Would be populated by middleware
        }
        
        self.access_log.append(access_entry)
