"""
Authentication and User models for School ERP
Secure user management with RBAC support
"""

from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey, Table, UniqueConstraint, Index
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, ARRAY
import uuid

from app.models.base import Base, MultiTenantMixin, LocalizationMixin, SoftDeleteMixin, LightAuditMixin
from app.core.security import password_manager


# Note: UserRole and RolePermission are now proper models defined at the end of this file
# instead of simple association tables for better audit and control


class User(Base, MultiTenantMixin, LocalizationMixin, SoftDeleteMixin, LightAuditMixin):
    """User model with comprehensive security features"""
    
    __tablename__ = "users"
    
    # Basic Information
    email = Column(String(255), nullable=False, index=True)
    username = Column(String(100), nullable=True, index=True)
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    middle_name = Column(String(100), nullable=True)
    display_name = Column(String(200), nullable=True)
    
    # Authentication
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    
    # Security Fields
    last_login = Column(DateTime(timezone=True), nullable=True)
    last_password_change = Column(DateTime(timezone=True), nullable=True)
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    account_locked_until = Column(DateTime(timezone=True), nullable=True)
    password_reset_token = Column(String(255), nullable=True, index=True)
    password_reset_expires = Column(DateTime(timezone=True), nullable=True)
    email_verification_token = Column(String(255), nullable=True, index=True)
    email_verification_expires = Column(DateTime(timezone=True), nullable=True)
    
    # Contact Information
    phone = Column(String(20), nullable=True)
    alternate_email = Column(String(255), nullable=True)
    
    # Profile Information
    avatar_url = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    date_of_birth = Column(DateTime(timezone=True), nullable=True)
    gender = Column(String(20), nullable=True)
    
    # Address Information
    address_line1 = Column(String(255), nullable=True)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    
    # Employment Information (for staff)
    employee_id = Column(String(50), nullable=True, index=True)
    department = Column(String(100), nullable=True)
    designation = Column(String(100), nullable=True)
    joining_date = Column(DateTime(timezone=True), nullable=True)
    
    # User Type and Status
    user_type = Column(String(50), nullable=False, default="staff", index=True)  # staff, student, parent, admin
    status = Column(String(50), nullable=False, default="active", index=True)  # active, inactive, suspended, terminated
    
    # Security Preferences
    two_factor_enabled = Column(Boolean, default=False, nullable=False)
    two_factor_secret = Column(String(255), nullable=True)
    backup_codes = Column(ARRAY(String), nullable=True)
    
    # Session Management
    max_concurrent_sessions = Column(Integer, default=3, nullable=False)
    force_password_change = Column(Boolean, default=False, nullable=False)
    
    # Relationships (using proper UserRole model)
    # roles accessible via user_roles relationship
    
    def set_password(self, password: str) -> None:
        """Set user password with hashing"""
        self.password_hash = password_manager.hash_password(password)
        self.last_password_change = datetime.utcnow()
        self.failed_login_attempts = 0
        self.account_locked_until = None
    
    def verify_password(self, password: str) -> bool:
        """Verify password against stored hash"""
        return password_manager.verify_password(password, self.password_hash)
    
    def is_account_locked(self) -> bool:
        """Check if account is currently locked"""
        if self.account_locked_until:
            return datetime.utcnow() < self.account_locked_until
        return False
    
    def increment_failed_login(self) -> None:
        """Increment failed login attempts and lock if necessary"""
        self.failed_login_attempts += 1
        
        # Lock account after 5 failed attempts for 30 minutes
        if self.failed_login_attempts >= 5:
            self.account_locked_until = datetime.utcnow() + timedelta(minutes=30)
    
    def reset_failed_login(self) -> None:
        """Reset failed login attempts after successful login"""
        self.failed_login_attempts = 0
        self.account_locked_until = None
        self.last_login = datetime.utcnow()
    
    def get_full_name(self) -> str:
        """Get user's full name"""
        parts = [self.first_name]
        if self.middle_name:
            parts.append(self.middle_name)
        parts.append(self.last_name)
        return " ".join(parts)
    
    def get_permissions(self) -> List[str]:
        """Get all permissions for user through roles"""
        permissions = set()

        # Get permissions through UserRole relationships
        for user_role in self.user_roles:
            if user_role.is_active and not user_role.is_expired():
                role = user_role.role
                if role.is_active:
                    # Get permissions through RolePermission relationships
                    for role_permission in role.role_permissions:
                        if role_permission.is_active:
                            permission = role_permission.permission
                            if permission.is_active:
                                permissions.add(permission.code)

        return list(permissions)
    
    def has_permission(self, permission_code: str) -> bool:
        """Check if user has specific permission"""
        return permission_code in self.get_permissions()
    
    def has_role(self, role_name: str) -> bool:
        """Check if user has specific role"""
        return any(role.name == role_name and role.is_active for role in self.roles)
    
    @property
    def is_staff(self) -> bool:
        """Check if user is staff member"""
        return self.user_type in ["staff", "admin"]
    
    @property
    def is_student(self) -> bool:
        """Check if user is student"""
        return self.user_type == "student"
    
    @property
    def is_parent(self) -> bool:
        """Check if user is parent"""
        return self.user_type == "parent"


class Role(Base, MultiTenantMixin, LightAuditMixin):
    """Role model for RBAC system"""
    
    __tablename__ = "roles"
    
    # Basic Information
    name = Column(String(100), nullable=False, index=True)
    display_name = Column(String(200), nullable=True)
    description = Column(Text, nullable=True)
    
    # Role Properties
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_system_role = Column(Boolean, default=False, nullable=False)  # Cannot be deleted
    is_default = Column(Boolean, default=False, nullable=False)  # Auto-assigned to new users
    
    # Role Hierarchy
    parent_role_id = Column(UUID(as_uuid=True), ForeignKey('roles.id'), nullable=True)
    level = Column(Integer, default=0, nullable=False)  # Hierarchy level
    
    # Role Scope
    scope = Column(String(50), default="school", nullable=False)  # school, organization, system
    
    # Relationships (using proper UserRole and RolePermission models)
    parent_role = relationship("Role", remote_side="Role.id", backref="child_roles")
    # users accessible via user_roles relationship
    # permissions accessible via role_permissions relationship
    
    def get_all_permissions(self) -> List[str]:
        """Get all permissions including inherited from parent roles"""
        permissions = set()
        
        # Add direct permissions
        for permission in self.permissions:
            if permission.is_active:
                permissions.add(permission.code)
        
        # Add inherited permissions from parent roles
        if self.parent_role and self.parent_role.is_active:
            permissions.update(self.parent_role.get_all_permissions())
        
        return list(permissions)


class Permission(Base, MultiTenantMixin, LightAuditMixin):
    """Permission model for fine-grained access control"""
    
    __tablename__ = "permissions"
    
    # Basic Information
    code = Column(String(100), nullable=False, unique=True, index=True)
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    
    # Permission Properties
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_system_permission = Column(Boolean, default=False, nullable=False)
    
    # Permission Categorization
    category = Column(String(100), nullable=False, index=True)  # students, fees, attendance, etc.
    resource = Column(String(100), nullable=False, index=True)  # student, fee_record, attendance, etc.
    action = Column(String(50), nullable=False, index=True)  # create, read, update, delete, list, etc.
    
    # Permission Scope
    scope = Column(String(50), default="school", nullable=False)  # school, organization, system
    
    # Relationships (using proper RolePermission model)
    # roles accessible via role_permissions relationship


class UserSession(Base, MultiTenantMixin):
    """User session tracking for security and analytics"""
    
    __tablename__ = "user_sessions"
    
    # Session Information
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    session_id = Column(String(255), nullable=False, unique=True, index=True)
    
    # Session Details
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(Text, nullable=True)
    device_info = Column(Text, nullable=True)
    location = Column(String(255), nullable=True)
    
    # Session Status
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    login_time = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    last_activity = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    logout_time = Column(DateTime(timezone=True), nullable=True)
    
    # Security Information
    login_method = Column(String(50), nullable=True)  # password, sso, api_key, etc.
    two_factor_verified = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    user = relationship("User", backref="sessions")


class APIKey(Base, MultiTenantMixin, LightAuditMixin):
    """API key management for external integrations"""
    
    __tablename__ = "api_keys"
    
    # API Key Information
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    key_hash = Column(String(255), nullable=False, unique=True, index=True)
    key_prefix = Column(String(20), nullable=False, index=True)  # First few chars for identification
    
    # API Key Properties
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    last_used = Column(DateTime(timezone=True), nullable=True)
    usage_count = Column(Integer, default=0, nullable=False)
    
    # Rate Limiting
    rate_limit_per_minute = Column(Integer, default=60, nullable=False)
    rate_limit_per_hour = Column(Integer, default=1000, nullable=False)
    rate_limit_per_day = Column(Integer, default=10000, nullable=False)
    
    # Permissions
    permissions = Column(ARRAY(String), nullable=True)  # List of permission codes
    allowed_ips = Column(ARRAY(String), nullable=True)  # IP whitelist
    
    # Relationships
    created_by_user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    created_by = relationship("User", backref="created_api_keys")
    
    def is_expired(self) -> bool:
        """Check if API key is expired"""
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        return False
    
    def increment_usage(self) -> None:
        """Increment usage count and update last used"""
        self.usage_count += 1
        self.last_used = datetime.utcnow()


class UserRole(Base, MultiTenantMixin, LightAuditMixin):
    """User-Role relationship model for RBAC"""

    __tablename__ = "user_roles"

    # Relationships
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    role_id = Column(UUID(as_uuid=True), ForeignKey('roles.id'), nullable=False, index=True)

    # Assignment metadata
    assigned_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)
    assigned_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Status
    is_active = Column(Boolean, default=True, nullable=False, index=True)

    # Expiration (optional)
    expires_at = Column(DateTime, nullable=True)

    # Relationships
    user = relationship("User", foreign_keys=[user_id], backref="user_roles")
    role = relationship("Role", backref="user_roles")
    assigned_by_user = relationship("User", foreign_keys=[assigned_by])

    # Unique constraint
    __table_args__ = (
        UniqueConstraint('user_id', 'role_id', 'school_id', name='uq_user_role_school'),
        Index('idx_user_roles_user_id_is_active', 'user_id', 'is_active'),
        Index('idx_user_roles_role_id_is_active', 'role_id', 'is_active'),
    )

    def is_expired(self) -> bool:
        """Check if role assignment is expired"""
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        return False


class RolePermission(Base, MultiTenantMixin, LightAuditMixin):
    """Role-Permission relationship model for RBAC"""

    __tablename__ = "role_permissions"

    # Relationships
    role_id = Column(UUID(as_uuid=True), ForeignKey('roles.id'), nullable=False, index=True)
    permission_id = Column(UUID(as_uuid=True), ForeignKey('permissions.id'), nullable=False, index=True)

    # Assignment metadata
    assigned_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)
    assigned_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Status
    is_active = Column(Boolean, default=True, nullable=False, index=True)

    # Relationships
    role = relationship("Role", backref="role_permissions")
    permission = relationship("Permission", backref="role_permissions")
    assigned_by_user = relationship("User", foreign_keys=[assigned_by])

    # Unique constraint
    __table_args__ = (
        UniqueConstraint('role_id', 'permission_id', 'school_id', name='uq_role_permission_school'),
        Index('idx_role_permissions_role_id_is_active', 'role_id', 'is_active'),
        Index('idx_role_permissions_permission_id_is_active', 'permission_id', 'is_active'),
    )
