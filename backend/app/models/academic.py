"""
Academic structure models for School ERP
Handles academic years, classes, sections, and their relationships
"""

from datetime import datetime, date
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, String, Date, Boolean, Integer, Text, Index, UniqueConstraint, CheckConstraint, ForeignKey
from sqlalchemy.orm import relationship, validates
from sqlalchemy.dialects.postgresql import UUID, JSON
import uuid

from app.models.base import Base, MultiTenantMixin, SoftDeleteMixin, LightAuditMixin
from app.core.logging import get_logger

logger = get_logger(__name__)


class AcademicYear(Base, MultiTenantMixin, SoftDeleteMixin, LightAuditMixin):
    """
    Academic Year model for managing school academic calendar
    Supports Indian academic year pattern (April-March) by default
    """
    
    __tablename__ = "academic_years"
    
    # Basic Information
    year_label = Column(String(20), nullable=False, index=True)  # e.g., "2024-25"
    display_name = Column(String(100), nullable=True)  # e.g., "Academic Year 2024-25"
    description = Column(Text, nullable=True)
    
    # Academic Year Dates
    start_date = Column(Date, nullable=False, index=True)  # Academic year start date
    end_date = Column(Date, nullable=False, index=True)    # Academic year end date
    
    # Status Management
    is_active = Column(Boolean, default=False, nullable=False, index=True)  # Only one active per school
    status = Column(String(20), default="draft", nullable=False, index=True)  # draft, active, completed, archived
    
    # Academic Configuration
    total_working_days = Column(Integer, nullable=True)  # Expected working days in the year
    actual_working_days = Column(Integer, default=0, nullable=False)  # Actual working days completed
    
    # Terms/Semesters Configuration (JSON for flexibility)
    terms_config = Column(JSON, nullable=True, comment="Terms/semesters configuration")
    holidays_config = Column(JSON, nullable=True, comment="Holidays and breaks configuration")
    
    # Academic Settings
    settings = Column(JSON, nullable=True, comment="Academic year specific settings")
    
    # Metadata
    academic_calendar_approved = Column(Boolean, default=False, nullable=False)
    approved_by = Column(UUID(as_uuid=True), nullable=True)  # User who approved
    approved_at = Column(Date, nullable=True)
    
    # Constraints and Indexes
    __table_args__ = (
        # Unique constraint: Only one active academic year per school
        UniqueConstraint('school_id', 'is_active', name='uq_academic_year_active_per_school'),
        
        # Unique constraint: Year label must be unique per school
        UniqueConstraint('school_id', 'year_label', name='uq_academic_year_label_per_school'),
        
        # Check constraint: End date must be after start date
        CheckConstraint('end_date > start_date', name='ck_academic_year_date_range'),
        
        # Check constraint: Valid status values
        CheckConstraint(
            "status IN ('draft', 'active', 'completed', 'archived')", 
            name='ck_academic_year_status'
        ),
        
        # Indexes for common queries
        Index('idx_academic_years_school_status', 'school_id', 'status'),
        Index('idx_academic_years_school_dates', 'school_id', 'start_date', 'end_date'),
        Index('idx_academic_years_active_lookup', 'school_id', 'is_active', 'status'),
    )
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Set default display name if not provided
        if not self.display_name and self.year_label:
            self.display_name = f"Academic Year {self.year_label}"
    
    @validates('year_label')
    def validate_year_label(self, key, year_label):
        """Validate year label format (e.g., 2024-25)"""
        if not year_label:
            raise ValueError("Year label is required")
        
        # Basic format validation for YYYY-YY pattern
        if len(year_label) < 7 or '-' not in year_label:
            raise ValueError("Year label must be in format YYYY-YY (e.g., 2024-25)")
        
        return year_label.strip()
    
    @validates('start_date', 'end_date')
    def validate_dates(self, key, date_value):
        """Validate academic year dates"""
        if not date_value:
            raise ValueError(f"{key} is required")
        
        # Ensure date is a date object
        if isinstance(date_value, str):
            try:
                date_value = datetime.strptime(date_value, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError(f"Invalid date format for {key}. Use YYYY-MM-DD")
        
        return date_value
    
    @validates('status')
    def validate_status(self, key, status):
        """Validate academic year status"""
        valid_statuses = ['draft', 'active', 'completed', 'archived']
        if status not in valid_statuses:
            raise ValueError(f"Status must be one of: {', '.join(valid_statuses)}")
        return status
    
    def is_current_year(self) -> bool:
        """Check if this is the current academic year"""
        today = date.today()
        return self.start_date <= today <= self.end_date and self.is_active
    
    def get_duration_days(self) -> int:
        """Get total duration of academic year in days"""
        return (self.end_date - self.start_date).days + 1
    
    def get_progress_percentage(self) -> float:
        """Get academic year progress as percentage"""
        if not self.is_active:
            return 0.0
        
        today = date.today()
        if today < self.start_date:
            return 0.0
        elif today > self.end_date:
            return 100.0
        else:
            total_days = self.get_duration_days()
            elapsed_days = (today - self.start_date).days + 1
            return round((elapsed_days / total_days) * 100, 2)
    
    def get_remaining_days(self) -> int:
        """Get remaining days in academic year"""
        if not self.is_active:
            return 0
        
        today = date.today()
        if today > self.end_date:
            return 0
        elif today < self.start_date:
            return self.get_duration_days()
        else:
            return (self.end_date - today).days
    
    def can_be_activated(self) -> tuple[bool, str]:
        """Check if academic year can be activated"""
        if self.is_active:
            return False, "Academic year is already active"
        
        if self.status not in ['draft', 'completed']:
            return False, f"Cannot activate academic year with status: {self.status}"
        
        if self.end_date < date.today():
            return False, "Cannot activate academic year that has already ended"
        
        return True, "Academic year can be activated"
    
    def can_be_completed(self) -> tuple[bool, str]:
        """Check if academic year can be marked as completed"""
        if not self.is_active:
            return False, "Only active academic year can be completed"
        
        if self.status != 'active':
            return False, f"Cannot complete academic year with status: {self.status}"
        
        return True, "Academic year can be completed"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert academic year to dictionary with computed fields"""
        base_dict = super().to_dict()
        
        # Add computed fields
        base_dict.update({
            'is_current_year': self.is_current_year(),
            'duration_days': self.get_duration_days(),
            'progress_percentage': self.get_progress_percentage(),
            'remaining_days': self.get_remaining_days(),
            'can_be_activated': self.can_be_activated(),
            'can_be_completed': self.can_be_completed()
        })
        
        return base_dict

    # Relationships (defined here to avoid circular imports)
    class_subjects = relationship("ClassSubject", back_populates="academic_year", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<AcademicYear(id={self.id}, year_label='{self.year_label}', school_id={self.school_id}, is_active={self.is_active})>"
    
    def __str__(self):
        return f"{self.year_label} ({self.status})"


class Class(Base, MultiTenantMixin, SoftDeleteMixin, LightAuditMixin):
    """
    Class/Grade model for academic structure
    Represents different grade levels in the school
    """
    
    __tablename__ = "classes"
    
    # Basic Information
    name = Column(String(100), nullable=False, index=True)  # e.g., "Class 1", "Grade 10", "Nursery"
    display_name = Column(String(200), nullable=True)  # Custom display name
    short_name = Column(String(20), nullable=True)  # e.g., "1st", "10th", "N"
    description = Column(Text, nullable=True)
    
    # Class Properties
    class_code = Column(String(20), nullable=True, unique=True, index=True)  # Unique identifier
    display_order = Column(Integer, nullable=False, default=0, index=True)  # For sorting
    academic_level = Column(String(50), nullable=True, index=True)  # Primary, Secondary, Higher Secondary
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    
    # Class Configuration
    max_students_per_section = Column(Integer, default=40, nullable=False)
    settings = Column(JSON, nullable=True, comment="Class-specific settings")
    
    # Constraints and Indexes
    __table_args__ = (
        # Unique constraint: Class name must be unique per school
        UniqueConstraint('school_id', 'name', name='uq_class_name_per_school'),
        
        # Unique constraint: Display order must be unique per school
        UniqueConstraint('school_id', 'display_order', name='uq_class_display_order_per_school'),
        
        # Indexes for common queries
        Index('idx_classes_school_active', 'school_id', 'is_active'),
        Index('idx_classes_school_level', 'school_id', 'academic_level'),
        Index('idx_classes_school_order', 'school_id', 'display_order'),
    )
    
    @validates('name')
    def validate_name(self, key, name):
        """Validate class name"""
        if not name or not name.strip():
            raise ValueError("Class name is required")
        return name.strip()
    
    @validates('display_order')
    def validate_display_order(self, key, display_order):
        """Validate display order"""
        if display_order < 0:
            raise ValueError("Display order must be non-negative")
        return display_order
    
    @validates('max_students_per_section')
    def validate_max_students(self, key, max_students):
        """Validate maximum students per section"""
        if max_students <= 0:
            raise ValueError("Maximum students per section must be positive")
        return max_students

    # Relationships
    class_subjects = relationship("ClassSubject", back_populates="class_ref", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Class(id={self.id}, name='{self.name}', school_id={self.school_id}, display_order={self.display_order})>"
    
    def __str__(self):
        return self.display_name or self.name


class Section(Base, MultiTenantMixin, SoftDeleteMixin, LightAuditMixin):
    """
    Section model for dividing classes into smaller groups
    Represents sections within a class (e.g., A, B, C)
    """
    
    __tablename__ = "sections"
    
    # Basic Information
    name = Column(String(50), nullable=False, index=True)  # e.g., "A", "B", "Red", "Blue"
    display_name = Column(String(100), nullable=True)  # Custom display name
    description = Column(Text, nullable=True)
    
    # Section Properties
    section_code = Column(String(20), nullable=True, index=True)  # Optional unique identifier
    max_capacity = Column(Integer, default=40, nullable=False)  # Maximum students in this section
    current_strength = Column(Integer, default=0, nullable=False)  # Current number of students
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    
    # Section Configuration
    settings = Column(JSON, nullable=True, comment="Section-specific settings")
    
    # Constraints and Indexes
    __table_args__ = (
        # Unique constraint: Section name must be unique per school
        UniqueConstraint('school_id', 'name', name='uq_section_name_per_school'),
        
        # Check constraint: Current strength cannot exceed max capacity
        CheckConstraint('current_strength <= max_capacity', name='ck_section_capacity'),
        
        # Indexes for common queries
        Index('idx_sections_school_active', 'school_id', 'is_active'),
        Index('idx_sections_school_capacity', 'school_id', 'max_capacity'),
    )
    
    @validates('name')
    def validate_name(self, key, name):
        """Validate section name"""
        if not name or not name.strip():
            raise ValueError("Section name is required")
        return name.strip().upper()  # Store section names in uppercase
    
    @validates('max_capacity')
    def validate_max_capacity(self, key, max_capacity):
        """Validate maximum capacity"""
        if max_capacity <= 0:
            raise ValueError("Maximum capacity must be positive")
        return max_capacity
    
    @validates('current_strength')
    def validate_current_strength(self, key, current_strength):
        """Validate current strength"""
        if current_strength < 0:
            raise ValueError("Current strength cannot be negative")
        return current_strength
    
    def get_available_capacity(self) -> int:
        """Get available capacity in the section"""
        return max(0, self.max_capacity - self.current_strength)
    
    def is_full(self) -> bool:
        """Check if section is at full capacity"""
        return self.current_strength >= self.max_capacity
    
    def get_occupancy_percentage(self) -> float:
        """Get section occupancy as percentage"""
        if self.max_capacity == 0:
            return 0.0
        return round((self.current_strength / self.max_capacity) * 100, 2)
    
    def __repr__(self):
        return f"<Section(id={self.id}, name='{self.name}', school_id={self.school_id}, capacity={self.current_strength}/{self.max_capacity})>"
    
    def __str__(self):
        return self.display_name or self.name


class ClassSectionMapping(Base, MultiTenantMixin, LightAuditMixin):
    """
    Class-Section mapping model for academic year structure
    Links classes and sections for a specific academic year
    """

    __tablename__ = "class_section_mappings"

    # Relationships
    academic_year_id = Column(UUID(as_uuid=True), ForeignKey('academic_years.id'), nullable=False, index=True)
    class_id = Column(UUID(as_uuid=True), ForeignKey('classes.id'), nullable=False, index=True)
    section_id = Column(UUID(as_uuid=True), ForeignKey('sections.id'), nullable=False, index=True)

    # Optional assignments
    class_teacher_id = Column(UUID(as_uuid=True), nullable=True, index=True)  # FK to User (teacher)
    room_number = Column(String(50), nullable=True)  # Classroom assignment

    # Status
    is_active = Column(Boolean, default=True, nullable=False, index=True)

    # Mapping Configuration
    max_students = Column(Integer, nullable=True)  # Override section's max capacity for this mapping
    current_students = Column(Integer, default=0, nullable=False)  # Current enrolled students

    # Academic Configuration
    settings = Column(JSON, nullable=True, comment="Class-section mapping specific settings")

    # Relationships
    academic_year = relationship("AcademicYear", backref="class_section_mappings")
    class_ref = relationship("Class", backref="class_section_mappings")
    section = relationship("Section", backref="class_section_mappings")

    # Constraints and Indexes
    __table_args__ = (
        # Unique constraint: One mapping per academic year, class, section combination
        UniqueConstraint(
            'academic_year_id', 'class_id', 'section_id', 'school_id',
            name='uq_class_section_mapping_per_year'
        ),

        # Check constraint: Current students cannot exceed max students
        CheckConstraint('current_students <= COALESCE(max_students, 999)', name='ck_mapping_student_capacity'),

        # Indexes for common queries
        Index('idx_class_section_mappings_academic_year', 'academic_year_id', 'is_active'),
        Index('idx_class_section_mappings_class', 'class_id', 'is_active'),
        Index('idx_class_section_mappings_section', 'section_id', 'is_active'),
        Index('idx_class_section_mappings_teacher', 'class_teacher_id'),
        Index('idx_class_section_mappings_school_year', 'school_id', 'academic_year_id', 'is_active'),
    )

    @validates('current_students')
    def validate_current_students(self, key, current_students):
        """Validate current students count"""
        if current_students < 0:
            raise ValueError("Current students count cannot be negative")
        return current_students

    @validates('max_students')
    def validate_max_students(self, key, max_students):
        """Validate maximum students"""
        if max_students is not None and max_students <= 0:
            raise ValueError("Maximum students must be positive")
        return max_students

    def get_effective_max_students(self) -> int:
        """Get effective maximum students (mapping override or section default)"""
        if self.max_students is not None:
            return self.max_students
        return self.section.max_capacity if self.section else 40

    def get_available_capacity(self) -> int:
        """Get available capacity in this class-section mapping"""
        return max(0, self.get_effective_max_students() - self.current_students)

    def is_full(self) -> bool:
        """Check if class-section mapping is at full capacity"""
        return self.current_students >= self.get_effective_max_students()

    def get_occupancy_percentage(self) -> float:
        """Get occupancy percentage for this mapping"""
        max_students = self.get_effective_max_students()
        if max_students == 0:
            return 0.0
        return round((self.current_students / max_students) * 100, 2)

    def get_full_name(self) -> str:
        """Get full name of the class-section combination"""
        class_name = self.class_ref.name if self.class_ref else "Unknown Class"
        section_name = self.section.name if self.section else "Unknown Section"
        return f"{class_name} - {section_name}"

    def __repr__(self):
        return f"<ClassSectionMapping(id={self.id}, academic_year_id={self.academic_year_id}, class_id={self.class_id}, section_id={self.section_id})>"

    def __str__(self):
        return self.get_full_name()


class Subject(Base, MultiTenantMixin, SoftDeleteMixin, LightAuditMixin):
    """
    Subject model for managing academic subjects
    Supports multi-tenant isolation and comprehensive subject management
    """

    __tablename__ = "subjects"

    # Basic Information
    subject_code = Column(String(20), nullable=False, index=True)  # e.g., "MATH101", "ENG201"
    name = Column(String(100), nullable=False, index=True)  # e.g., "Mathematics", "English Literature"
    description = Column(Text, nullable=True)  # Detailed subject description

    # Subject Classification
    subject_type = Column(String(30), default="core", nullable=False, index=True)  # core, elective, extra_curricular
    academic_level = Column(String(30), nullable=True, index=True)  # primary, secondary, higher_secondary

    # Subject Configuration
    is_active = Column(Boolean, default=True, nullable=False, index=True)  # Subject availability
    display_order = Column(Integer, default=0, nullable=False)  # For ordering in UI

    # Academic Settings
    default_credits = Column(Integer, nullable=True)  # Default credit hours
    default_hours_per_week = Column(Integer, nullable=True)  # Default weekly hours

    # Subject-specific settings (JSON for flexibility)
    settings = Column(JSON, nullable=True, comment="Subject-specific configuration")

    # Approval and Metadata
    approved_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    approved_at = Column(Date, nullable=True)

    # Relationships
    class_subjects = relationship("ClassSubject", back_populates="subject", cascade="all, delete-orphan")

    # Database Constraints
    __table_args__ = (
        # Unique constraint for subject code per school
        UniqueConstraint('school_id', 'subject_code', name='uq_subject_code_per_school'),

        # Unique constraint for subject name per school (case-insensitive)
        UniqueConstraint('school_id', 'name', name='uq_subject_name_per_school'),

        # Check constraints for valid values
        CheckConstraint(
            "subject_type IN ('core', 'elective', 'extra_curricular')",
            name='ck_subject_type_valid'
        ),
        CheckConstraint(
            "academic_level IS NULL OR academic_level IN ('primary', 'secondary', 'higher_secondary')",
            name='ck_academic_level_valid'
        ),
        CheckConstraint(
            "default_credits IS NULL OR default_credits > 0",
            name='ck_default_credits_positive'
        ),
        CheckConstraint(
            "default_hours_per_week IS NULL OR default_hours_per_week > 0",
            name='ck_default_hours_positive'
        ),

        # Indexes for performance
        Index('ix_subjects_school_active', 'school_id', 'is_active'),
        Index('ix_subjects_type_level', 'subject_type', 'academic_level'),
        Index('ix_subjects_display_order', 'display_order'),
    )

    # Validation methods
    @validates('subject_code')
    def validate_subject_code(self, key, subject_code):
        """Validate subject code format"""
        if not subject_code or len(subject_code.strip()) == 0:
            raise ValueError("Subject code cannot be empty")

        # Convert to uppercase and remove extra spaces
        subject_code = subject_code.strip().upper()

        # Basic format validation (alphanumeric with optional special chars)
        if not subject_code.replace('-', '').replace('_', '').isalnum():
            raise ValueError("Subject code must contain only alphanumeric characters, hyphens, and underscores")

        if len(subject_code) > 20:
            raise ValueError("Subject code cannot exceed 20 characters")

        return subject_code

    @validates('name')
    def validate_name(self, key, name):
        """Validate subject name"""
        if not name or len(name.strip()) == 0:
            raise ValueError("Subject name cannot be empty")

        name = name.strip()
        if len(name) > 100:
            raise ValueError("Subject name cannot exceed 100 characters")

        return name

    @validates('default_credits')
    def validate_default_credits(self, key, default_credits):
        """Validate default credits"""
        if default_credits is not None and default_credits <= 0:
            raise ValueError("Default credits must be positive")
        return default_credits

    @validates('default_hours_per_week')
    def validate_default_hours_per_week(self, key, default_hours_per_week):
        """Validate default hours per week"""
        if default_hours_per_week is not None and default_hours_per_week <= 0:
            raise ValueError("Default hours per week must be positive")
        return default_hours_per_week

    # Computed properties
    @property
    def is_core_subject(self) -> bool:
        """Check if this is a core subject"""
        return self.subject_type == "core"

    @property
    def is_elective_subject(self) -> bool:
        """Check if this is an elective subject"""
        return self.subject_type == "elective"

    @property
    def is_extra_curricular(self) -> bool:
        """Check if this is an extra-curricular subject"""
        return self.subject_type == "extra_curricular"

    def get_active_class_mappings(self) -> List['ClassSubject']:
        """Get active class-subject mappings for this subject"""
        return [cs for cs in self.class_subjects if cs.is_active and not cs.is_deleted]

    def get_assigned_classes_count(self) -> int:
        """Get count of classes this subject is assigned to"""
        return len(self.get_active_class_mappings())

    def __repr__(self):
        return f"<Subject(id={self.id}, code={self.subject_code}, name={self.name})>"

    def __str__(self):
        return f"{self.subject_code} - {self.name}"


class ClassSubject(Base, MultiTenantMixin, SoftDeleteMixin, LightAuditMixin):
    """
    ClassSubject model for mapping subjects to classes for specific academic years
    Handles teacher assignments, subject configuration per class, and academic year scoping
    """

    __tablename__ = "class_subjects"

    # Foreign Key Relationships
    academic_year_id = Column(UUID(as_uuid=True), ForeignKey("academic_years.id"), nullable=False, index=True)
    class_id = Column(UUID(as_uuid=True), ForeignKey("classes.id"), nullable=False, index=True)
    subject_id = Column(UUID(as_uuid=True), ForeignKey("subjects.id"), nullable=False, index=True)

    # Teacher Assignment
    teacher_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, index=True)  # Primary teacher

    # Subject Configuration for this Class
    is_mandatory = Column(Boolean, default=True, nullable=False)  # Required vs optional subject
    is_active = Column(Boolean, default=True, nullable=False, index=True)  # Active in current academic year

    # Academic Configuration
    credits = Column(Integer, nullable=True)  # Credit hours for this subject in this class
    hours_per_week = Column(Integer, nullable=True)  # Weekly hours for this class
    max_marks = Column(Integer, nullable=True)  # Maximum marks for assessments
    pass_marks = Column(Integer, nullable=True)  # Minimum marks to pass

    # Scheduling and Organization
    display_order = Column(Integer, default=0, nullable=False)  # Order in class timetable

    # Subject-specific settings for this class (JSON for flexibility)
    settings = Column(JSON, nullable=True, comment="Class-specific subject configuration")

    # Metadata
    assigned_at = Column(Date, nullable=True)  # When subject was assigned to class
    assigned_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # Relationships
    academic_year = relationship("AcademicYear", back_populates="class_subjects")
    class_ref = relationship("Class", back_populates="class_subjects")
    subject = relationship("Subject", back_populates="class_subjects")

    # Database Constraints
    __table_args__ = (
        # Unique constraint: One subject per class per academic year
        UniqueConstraint(
            'school_id', 'academic_year_id', 'class_id', 'subject_id',
            name='uq_class_subject_per_academic_year'
        ),

        # Check constraints for valid values
        CheckConstraint(
            "credits IS NULL OR credits > 0",
            name='ck_credits_positive'
        ),
        CheckConstraint(
            "hours_per_week IS NULL OR hours_per_week > 0",
            name='ck_hours_per_week_positive'
        ),
        CheckConstraint(
            "max_marks IS NULL OR max_marks > 0",
            name='ck_max_marks_positive'
        ),
        CheckConstraint(
            "pass_marks IS NULL OR pass_marks >= 0",
            name='ck_pass_marks_non_negative'
        ),
        CheckConstraint(
            "pass_marks IS NULL OR max_marks IS NULL OR pass_marks <= max_marks",
            name='ck_pass_marks_not_exceed_max'
        ),

        # Indexes for performance
        Index('ix_class_subjects_academic_year_class', 'academic_year_id', 'class_id'),
        Index('ix_class_subjects_teacher', 'teacher_id'),
        Index('ix_class_subjects_active', 'school_id', 'is_active'),
        Index('ix_class_subjects_mandatory', 'is_mandatory'),
        Index('ix_class_subjects_display_order', 'display_order'),
    )

    # Validation methods
    @validates('credits')
    def validate_credits(self, key, credits):
        """Validate credits"""
        if credits is not None and credits <= 0:
            raise ValueError("Credits must be positive")
        return credits

    @validates('hours_per_week')
    def validate_hours_per_week(self, key, hours_per_week):
        """Validate hours per week"""
        if hours_per_week is not None and hours_per_week <= 0:
            raise ValueError("Hours per week must be positive")
        return hours_per_week

    @validates('max_marks')
    def validate_max_marks(self, key, max_marks):
        """Validate maximum marks"""
        if max_marks is not None and max_marks <= 0:
            raise ValueError("Maximum marks must be positive")
        return max_marks

    @validates('pass_marks')
    def validate_pass_marks(self, key, pass_marks):
        """Validate pass marks"""
        if pass_marks is not None and pass_marks < 0:
            raise ValueError("Pass marks cannot be negative")

        # Additional validation against max_marks if both are set
        if pass_marks is not None and self.max_marks is not None and pass_marks > self.max_marks:
            raise ValueError("Pass marks cannot exceed maximum marks")

        return pass_marks

    # Computed properties
    @property
    def pass_percentage(self) -> Optional[float]:
        """Calculate pass percentage"""
        if self.pass_marks is not None and self.max_marks is not None and self.max_marks > 0:
            return round((self.pass_marks / self.max_marks) * 100, 2)
        return None

    @property
    def is_assigned_to_teacher(self) -> bool:
        """Check if subject is assigned to a teacher"""
        return self.teacher_id is not None

    def get_effective_credits(self) -> int:
        """Get effective credits (class-specific or subject default)"""
        if self.credits is not None:
            return self.credits
        return self.subject.default_credits if self.subject else 0

    def get_effective_hours_per_week(self) -> int:
        """Get effective hours per week (class-specific or subject default)"""
        if self.hours_per_week is not None:
            return self.hours_per_week
        return self.subject.default_hours_per_week if self.subject else 0

    def get_full_name(self) -> str:
        """Get full name of the class-subject combination"""
        class_name = self.class_ref.name if self.class_ref else "Unknown Class"
        subject_name = self.subject.name if self.subject else "Unknown Subject"
        academic_year = self.academic_year.year_label if self.academic_year else "Unknown Year"
        return f"{class_name} - {subject_name} ({academic_year})"

    def __repr__(self):
        return f"<ClassSubject(id={self.id}, class_id={self.class_id}, subject_id={self.subject_id}, academic_year_id={self.academic_year_id})>"

    def __str__(self):
        return self.get_full_name()
