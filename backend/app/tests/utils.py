"""
Test utilities and helper functions for School ERP Backend
Production-ready testing utilities for common test operations
"""

import json
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.models.base import Organization, School
from app.models.auth import User, Role, Permission
from app.core.security import security_manager, password_manager


class TestDataManager:
    """Utility class for managing test data creation and cleanup."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.created_objects = []
    
    def create_organization(self, **kwargs) -> Organization:
        """Create a test organization with default values."""
        defaults = {
            "name": "Test Organization",
            "email": "<EMAIL>",
            "subdomain": f"testorg{uuid.uuid4().hex[:8]}",
            "country": "India"
        }
        defaults.update(kwargs)
        
        org = Organization(**defaults)
        self.db.add(org)
        self.db.commit()
        self.db.refresh(org)
        self.created_objects.append(org)
        return org
    
    def create_school(self, organization: Optional[Organization] = None, **kwargs) -> School:
        """Create a test school with default values."""
        if not organization:
            organization = self.create_organization()
        
        school_id = uuid.uuid4()
        defaults = {
            "id": school_id,
            "school_id": school_id,
            "organization_id": organization.id,
            "name": "Test School",
            "email": "<EMAIL>",
            "subdomain": f"testschool{uuid.uuid4().hex[:8]}",
            "school_code": f"SCH{uuid.uuid4().hex[:6].upper()}",
            "board_type": "CBSE",
            "country": "India"
        }
        defaults.update(kwargs)
        
        school = School(**defaults)
        self.db.add(school)
        self.db.commit()
        self.db.refresh(school)
        self.created_objects.append(school)
        return school
    
    def create_user(self, school: Optional[School] = None, password: str = "TestPassword123!", **kwargs) -> User:
        """Create a test user with default values."""
        if not school:
            school = self.create_school()
        
        defaults = {
            "school_id": school.id,
            "email": f"user{uuid.uuid4().hex[:8]}@test.com",
            "first_name": "Test",
            "last_name": "User",
            "user_type": "admin",
            "is_active": True,
            "is_verified": True
        }
        defaults.update(kwargs)
        
        user = User(**defaults)
        user.set_password(password)
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        self.created_objects.append(user)
        return user
    
    def create_role(self, school: Optional[School] = None, **kwargs) -> Role:
        """Create a test role with default values."""
        if not school:
            school = self.create_school()
        
        defaults = {
            "school_id": school.id,
            "name": f"role_{uuid.uuid4().hex[:8]}",
            "display_name": "Test Role",
            "description": "Test role description",
            "level": 5,
            "is_active": True
        }
        defaults.update(kwargs)
        
        role = Role(**defaults)
        self.db.add(role)
        self.db.commit()
        self.db.refresh(role)
        self.created_objects.append(role)
        return role
    
    def create_permission(self, school: Optional[School] = None, **kwargs) -> Permission:
        """Create a test permission with default values."""
        if not school:
            school = self.create_school()
        
        defaults = {
            "school_id": school.id,
            "code": f"permission_{uuid.uuid4().hex[:8]}",
            "name": "Test Permission",
            "description": "Test permission description",
            "category": "admin",
            "resource": "test",
            "action": "read",
            "is_active": True
        }
        defaults.update(kwargs)
        
        permission = Permission(**defaults)
        self.db.add(permission)
        self.db.commit()
        self.db.refresh(permission)
        self.created_objects.append(permission)
        return permission
    
    def cleanup(self):
        """Clean up all created test objects."""
        for obj in reversed(self.created_objects):
            try:
                self.db.delete(obj)
            except Exception:
                pass  # Object might already be deleted
        self.db.commit()
        self.created_objects.clear()


class APITestHelper:
    """Helper class for API testing operations."""
    
    def __init__(self, client: TestClient):
        self.client = client
    
    def create_auth_headers(self, user: User, school: School) -> Dict[str, str]:
        """Create authentication headers for API requests."""
        access_token = security_manager.create_access_token(
            subject=user.id,
            school_id=school.id,
            permissions=user.get_permissions()
        )
        return {"Authorization": f"Bearer {access_token}"}
    
    def post_json(self, url: str, data: Dict[str, Any], headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Make a POST request with JSON data and return parsed response."""
        response = self.client.post(url, json=data, headers=headers or {})
        return {
            "status_code": response.status_code,
            "data": response.json() if response.content else None,
            "headers": dict(response.headers)
        }
    
    def get_json(self, url: str, headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Make a GET request and return parsed response."""
        response = self.client.get(url, headers=headers or {})
        return {
            "status_code": response.status_code,
            "data": response.json() if response.content else None,
            "headers": dict(response.headers)
        }
    
    def put_json(self, url: str, data: Dict[str, Any], headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Make a PUT request with JSON data and return parsed response."""
        response = self.client.put(url, json=data, headers=headers or {})
        return {
            "status_code": response.status_code,
            "data": response.json() if response.content else None,
            "headers": dict(response.headers)
        }
    
    def delete_json(self, url: str, headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Make a DELETE request and return parsed response."""
        response = self.client.delete(url, headers=headers or {})
        return {
            "status_code": response.status_code,
            "data": response.json() if response.content else None,
            "headers": dict(response.headers)
        }


class AssertionHelper:
    """Helper class for common test assertions."""
    
    @staticmethod
    def assert_response_success(response: Dict[str, Any], expected_status: int = 200):
        """Assert that API response is successful."""
        assert response["status_code"] == expected_status, f"Expected {expected_status}, got {response['status_code']}"
        assert response["data"] is not None, "Response data should not be None"
        if "success" in response["data"]:
            assert response["data"]["success"] is True, "Response should indicate success"
    
    @staticmethod
    def assert_response_error(response: Dict[str, Any], expected_status: int, expected_detail: Optional[str] = None):
        """Assert that API response is an error."""
        assert response["status_code"] == expected_status, f"Expected {expected_status}, got {response['status_code']}"
        if expected_detail:
            assert expected_detail in str(response["data"]), f"Expected '{expected_detail}' in response"
    
    @staticmethod
    def assert_has_keys(data: Dict[str, Any], required_keys: List[str]):
        """Assert that dictionary has all required keys."""
        missing_keys = [key for key in required_keys if key not in data]
        assert not missing_keys, f"Missing required keys: {missing_keys}"
    
    @staticmethod
    def assert_valid_uuid(value: str):
        """Assert that string is a valid UUID."""
        try:
            uuid.UUID(value)
        except ValueError:
            assert False, f"'{value}' is not a valid UUID"
    
    @staticmethod
    def assert_valid_email(email: str):
        """Assert that string is a valid email format."""
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        assert re.match(email_pattern, email), f"'{email}' is not a valid email format"
    
    @staticmethod
    def assert_valid_subdomain(subdomain: str):
        """Assert that string is a valid subdomain format."""
        import re
        subdomain_pattern = r'^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$'
        assert re.match(subdomain_pattern, subdomain), f"'{subdomain}' is not a valid subdomain format"


class DatabaseTestHelper:
    """Helper class for database testing operations."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def count_records(self, model_class) -> int:
        """Count total records in a table."""
        return self.db.query(model_class).count()
    
    def get_by_id(self, model_class, record_id: uuid.UUID):
        """Get record by ID."""
        return self.db.query(model_class).filter(model_class.id == record_id).first()
    
    def get_by_field(self, model_class, field_name: str, field_value: Any):
        """Get record by field value."""
        field = getattr(model_class, field_name)
        return self.db.query(model_class).filter(field == field_value).first()
    
    def assert_record_exists(self, model_class, record_id: uuid.UUID):
        """Assert that record exists in database."""
        record = self.get_by_id(model_class, record_id)
        assert record is not None, f"Record with ID {record_id} not found in {model_class.__name__}"
        return record
    
    def assert_record_not_exists(self, model_class, record_id: uuid.UUID):
        """Assert that record does not exist in database."""
        record = self.get_by_id(model_class, record_id)
        assert record is None, f"Record with ID {record_id} should not exist in {model_class.__name__}"
    
    def assert_field_value(self, model_class, record_id: uuid.UUID, field_name: str, expected_value: Any):
        """Assert that record field has expected value."""
        record = self.assert_record_exists(model_class, record_id)
        actual_value = getattr(record, field_name)
        assert actual_value == expected_value, f"Expected {field_name}={expected_value}, got {actual_value}"


def create_test_jwt_token(user_id: uuid.UUID, school_id: uuid.UUID, permissions: List[str] = None) -> str:
    """Create a test JWT token for authentication testing."""
    return security_manager.create_access_token(
        subject=user_id,
        school_id=school_id,
        permissions=permissions or []
    )


def create_expired_jwt_token(user_id: uuid.UUID, school_id: uuid.UUID) -> str:
    """Create an expired JWT token for testing token expiration."""
    # Create token with past expiration time
    from app.core.security import create_jwt_token
    from datetime import datetime, timedelta
    
    payload = {
        "sub": str(user_id),
        "school_id": str(school_id),
        "exp": datetime.utcnow() - timedelta(hours=1),  # Expired 1 hour ago
        "iat": datetime.utcnow() - timedelta(hours=2),
        "type": "access"
    }
    
    return create_jwt_token(payload)


def mock_current_time(target_time: datetime):
    """Mock current time for testing time-dependent functionality."""
    from unittest.mock import patch
    return patch('app.core.security.datetime')


def generate_test_file_upload():
    """Generate test file upload data."""
    return {
        "filename": "test_file.txt",
        "content": b"Test file content",
        "content_type": "text/plain"
    }


def assert_audit_log_created(db_session: Session, action: str, resource_type: str, user_id: uuid.UUID):
    """Assert that audit log entry was created."""
    from app.models.audit import AuditLog
    
    audit_log = db_session.query(AuditLog).filter(
        AuditLog.action == action,
        AuditLog.resource_type == resource_type,
        AuditLog.user_id == user_id
    ).first()
    
    assert audit_log is not None, f"Audit log not found for action: {action}, resource: {resource_type}"
    return audit_log


def cleanup_test_files(file_paths: List[str]):
    """Clean up test files after testing."""
    import os
    for file_path in file_paths:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception:
            pass  # Ignore cleanup errors
