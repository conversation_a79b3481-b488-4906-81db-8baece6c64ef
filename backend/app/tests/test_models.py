"""
Unit tests for database models
Comprehensive testing of model functionality, relationships, and validation
"""

import pytest
import uuid
from datetime import datetime, timedelta
from sqlalchemy.exc import IntegrityError

from app.models.base import Organization, School
from app.models.auth import User, Role, Permission, UserSession
from app.tests.utils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AssertionHelper
from app.tests.factories import (
    OrganizationFactory, SchoolFactory, UserFactory, 
    RoleFactory, PermissionFactory, UserSessionFactory
)


class TestOrganizationModel:
    """Test Organization model functionality."""
    
    def test_create_organization(self, db_session):
        """Test creating an organization."""
        data_manager = TestDataManager(db_session)
        
        try:
            org = data_manager.create_organization(
                name="Test Organization",
                email="<EMAIL>",
                subdomain="testorg"
            )
            
            assert org.id is not None
            assert org.name == "Test Organization"
            assert org.email == "<EMAIL>"
            assert org.subdomain == "testorg"
            assert org.country == "India"  # Default
            assert org.is_active is True
            assert org.created_at is not None
            
        finally:
            data_manager.cleanup()
    
    def test_organization_unique_subdomain(self, db_session):
        """Test organization subdomain uniqueness constraint."""
        data_manager = TestDataManager(db_session)
        
        try:
            # Create first organization
            org1 = data_manager.create_organization(subdomain="unique")
            
            # Attempt to create second organization with same subdomain
            with pytest.raises(IntegrityError):
                data_manager.create_organization(subdomain="unique")
                
        finally:
            data_manager.cleanup()
    
    def test_organization_soft_delete(self, db_session):
        """Test organization soft delete functionality."""
        data_manager = TestDataManager(db_session)
        
        try:
            org = data_manager.create_organization()
            org_id = org.id
            
            # Soft delete
            org.soft_delete()
            db_session.commit()
            
            # Verify soft delete
            assert org.is_deleted is True
            assert org.deleted_at is not None
            
            # Verify record still exists in database
            db_record = db_session.query(Organization).filter(Organization.id == org_id).first()
            assert db_record is not None
            assert db_record.is_deleted is True
            
        finally:
            data_manager.cleanup()


class TestSchoolModel:
    """Test School model functionality."""
    
    def test_create_school(self, db_session):
        """Test creating a school."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school(
                name="Test School",
                subdomain="testschool",
                school_code="TEST001"
            )
            
            assert school.id is not None
            assert school.school_id == school.id  # Self-reference
            assert school.name == "Test School"
            assert school.subdomain == "testschool"
            assert school.school_code == "TEST001"
            assert school.is_active is True
            assert school.academic_year_start_month == 4  # April default
            
        finally:
            data_manager.cleanup()
    
    def test_school_organization_relationship(self, db_session):
        """Test school-organization relationship."""
        data_manager = TestDataManager(db_session)
        
        try:
            org = data_manager.create_organization()
            school = data_manager.create_school(organization=org)
            
            assert school.organization_id == org.id
            
            # Test relationship access (if defined)
            # Note: Relationship needs to be defined in model for this to work
            # assert school.organization == org
            
        finally:
            data_manager.cleanup()
    
    def test_school_unique_constraints(self, db_session):
        """Test school unique constraints."""
        data_manager = TestDataManager(db_session)
        
        try:
            # Create first school
            school1 = data_manager.create_school(
                subdomain="unique",
                school_code="UNIQUE001"
            )
            
            # Test subdomain uniqueness
            with pytest.raises(IntegrityError):
                data_manager.create_school(subdomain="unique")
            
            # Reset session after error
            db_session.rollback()
            
            # Test school_code uniqueness
            with pytest.raises(IntegrityError):
                data_manager.create_school(school_code="UNIQUE001")
                
        finally:
            data_manager.cleanup()
    
    def test_school_multi_tenant_setup(self, db_session):
        """Test school multi-tenant setup."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            
            # Verify multi-tenant fields
            assert school.school_id is not None
            assert school.school_id == school.id
            
            # Test that school_id is used for multi-tenant scoping
            assert hasattr(school, 'school_id')
            
        finally:
            data_manager.cleanup()


class TestUserModel:
    """Test User model functionality."""
    
    def test_create_user(self, db_session):
        """Test creating a user."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            user = data_manager.create_user(
                school=school,
                email="<EMAIL>",
                password="TestPassword123!"
            )
            
            assert user.id is not None
            assert user.school_id == school.id
            assert user.email == "<EMAIL>"
            assert user.is_active is True
            assert user.is_verified is True
            assert user.password_hash is not None
            assert user.password_hash != "TestPassword123!"  # Should be hashed
            
        finally:
            data_manager.cleanup()
    
    def test_user_password_hashing(self, db_session):
        """Test user password hashing and verification."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school, password="TestPassword123!")
            
            # Test password verification
            assert user.verify_password("TestPassword123!") is True
            assert user.verify_password("WrongPassword") is False
            
            # Test password change
            user.set_password("NewPassword123!")
            db_session.commit()
            
            assert user.verify_password("NewPassword123!") is True
            assert user.verify_password("TestPassword123!") is False
            
        finally:
            data_manager.cleanup()
    
    def test_user_failed_login_tracking(self, db_session):
        """Test user failed login attempt tracking."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)
            
            # Test increment failed login
            initial_attempts = user.failed_login_attempts
            user.increment_failed_login()
            db_session.commit()
            
            assert user.failed_login_attempts == initial_attempts + 1
            
            # Test reset failed login
            user.reset_failed_login()
            db_session.commit()
            
            assert user.failed_login_attempts == 0
            assert user.account_locked_until is None
            
        finally:
            data_manager.cleanup()
    
    def test_user_account_locking(self, db_session):
        """Test user account locking mechanism."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)
            
            # Test account locking
            lock_until = datetime.utcnow() + timedelta(hours=1)
            user.account_locked_until = lock_until
            db_session.commit()
            
            # Test is_account_locked method (if implemented)
            # assert user.is_account_locked() is True
            
            # Test unlocking
            user.account_locked_until = None
            db_session.commit()
            
            # assert user.is_account_locked() is False
            
        finally:
            data_manager.cleanup()
    
    def test_user_permissions(self, db_session):
        """Test user permission retrieval."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)
            
            # Test get_permissions method
            permissions = user.get_permissions()
            assert isinstance(permissions, list)
            
            # Initially should be empty or have default permissions
            # This depends on the implementation
            
        finally:
            data_manager.cleanup()


class TestRoleModel:
    """Test Role model functionality."""
    
    def test_create_role(self, db_session):
        """Test creating a role."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            role = data_manager.create_role(
                school=school,
                name="admin",
                display_name="Administrator"
            )
            
            assert role.id is not None
            assert role.school_id == school.id
            assert role.name == "admin"
            assert role.display_name == "Administrator"
            assert role.is_active is True
            assert role.level is not None
            
        finally:
            data_manager.cleanup()
    
    def test_role_hierarchy(self, db_session):
        """Test role hierarchy functionality."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            
            # Create parent role
            parent_role = data_manager.create_role(
                school=school,
                name="admin",
                level=1
            )
            
            # Create child role
            child_role = data_manager.create_role(
                school=school,
                name="teacher",
                level=2,
                parent_role_id=parent_role.id
            )
            
            assert child_role.parent_role_id == parent_role.id
            assert child_role.level > parent_role.level
            
        finally:
            data_manager.cleanup()


class TestPermissionModel:
    """Test Permission model functionality."""
    
    def test_create_permission(self, db_session):
        """Test creating a permission."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            permission = data_manager.create_permission(
                school=school,
                code="user.create",
                name="Create User",
                category="admin",
                resource="user",
                action="create"
            )
            
            assert permission.id is not None
            assert permission.school_id == school.id
            assert permission.code == "user.create"
            assert permission.name == "Create User"
            assert permission.category == "admin"
            assert permission.resource == "user"
            assert permission.action == "create"
            assert permission.is_active is True
            
        finally:
            data_manager.cleanup()
    
    def test_permission_unique_code(self, db_session):
        """Test permission code uniqueness within school."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            
            # Create first permission
            perm1 = data_manager.create_permission(
                school=school,
                code="unique.permission"
            )
            
            # Attempt to create second permission with same code in same school
            with pytest.raises(IntegrityError):
                data_manager.create_permission(
                    school=school,
                    code="unique.permission"
                )
                
        finally:
            data_manager.cleanup()


class TestUserSessionModel:
    """Test UserSession model functionality."""
    
    def test_create_user_session(self, db_session):
        """Test creating a user session."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)
            
            session = UserSession(
                user_id=user.id,
                school_id=school.id,
                session_id=str(uuid.uuid4()),
                ip_address="127.0.0.1",
                user_agent="Test Browser",
                login_method="password"
            )
            
            db_session.add(session)
            db_session.commit()
            db_session.refresh(session)
            
            assert session.id is not None
            assert session.user_id == user.id
            assert session.school_id == school.id
            assert session.is_active is True
            assert session.login_time is not None
            assert session.last_activity is not None
            
        finally:
            data_manager.cleanup()
    
    def test_session_activity_update(self, db_session):
        """Test session activity tracking."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)
            
            session = UserSession(
                user_id=user.id,
                school_id=school.id,
                session_id=str(uuid.uuid4()),
                ip_address="127.0.0.1",
                user_agent="Test Browser"
            )
            
            db_session.add(session)
            db_session.commit()
            
            original_activity = session.last_activity
            
            # Update activity (simulate user action)
            session.last_activity = datetime.utcnow()
            db_session.commit()
            
            assert session.last_activity > original_activity
            
        finally:
            data_manager.cleanup()


@pytest.mark.integration
class TestModelRelationships:
    """Test model relationships and foreign key constraints."""
    
    def test_cascade_delete_organization_schools(self, db_session):
        """Test cascade behavior when deleting organization."""
        data_manager = TestDataManager(db_session)
        
        try:
            org = data_manager.create_organization()
            school = data_manager.create_school(organization=org)
            
            org_id = org.id
            school_id = school.id
            
            # Delete organization (should handle cascade appropriately)
            db_session.delete(org)
            db_session.commit()
            
            # Check if school still exists (depends on cascade configuration)
            remaining_school = db_session.query(School).filter(School.id == school_id).first()
            
            # This test depends on the actual cascade configuration
            # If CASCADE is set, school should be deleted
            # If RESTRICT is set, deletion should fail
            # If SET NULL is set, school.organization_id should be None
            
        finally:
            data_manager.cleanup()
    
    def test_multi_tenant_isolation(self, db_session):
        """Test multi-tenant data isolation."""
        data_manager = TestDataManager(db_session)
        
        try:
            # Create two schools
            school1 = data_manager.create_school(subdomain="school1")
            school2 = data_manager.create_school(subdomain="school2")
            
            # Create users in each school
            user1 = data_manager.create_user(school=school1, email="<EMAIL>")
            user2 = data_manager.create_user(school=school2, email="<EMAIL>")
            
            # Verify isolation
            assert user1.school_id == school1.id
            assert user2.school_id == school2.id
            assert user1.school_id != user2.school_id
            
            # Test querying with school_id filter
            school1_users = db_session.query(User).filter(User.school_id == school1.id).all()
            school2_users = db_session.query(User).filter(User.school_id == school2.id).all()
            
            assert len(school1_users) == 1
            assert len(school2_users) == 1
            assert school1_users[0].id == user1.id
            assert school2_users[0].id == user2.id
            
        finally:
            data_manager.cleanup()
