"""
Unit and integration tests for authentication system
Comprehensive testing of JWT authentication, user management, and security features
"""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from fastapi import HTTPException

from app.services.auth_service.auth import auth_service, AuthenticationError
from app.models.auth import User, UserSession
from app.core.security import security_manager, password_manager
from app.tests.utils import TestDataManager, APITestHelper, <PERSON><PERSON><PERSON>Hel<PERSON>
from app.tests.factories import UserFactory, SchoolFactory, OrganizationFactory


class TestPasswordManager:
    """Test password hashing and verification."""
    
    def test_hash_password(self):
        """Test password hashing."""
        password = "TestPassword123!"
        hashed = password_manager.hash_password(password)
        
        assert hashed != password
        assert len(hashed) > 50  # bcrypt hashes are long
        assert hashed.startswith("$2b$")  # bcrypt prefix
    
    def test_verify_password_correct(self):
        """Test password verification with correct password."""
        password = "TestPassword123!"
        hashed = password_manager.hash_password(password)
        
        assert password_manager.verify_password(password, hashed) is True
    
    def test_verify_password_incorrect(self):
        """Test password verification with incorrect password."""
        password = "TestPassword123!"
        wrong_password = "WrongPassword123!"
        hashed = password_manager.hash_password(password)
        
        assert password_manager.verify_password(wrong_password, hashed) is False
    
    def test_validate_password_strength_valid(self):
        """Test password strength validation with valid password."""
        valid_passwords = [
            "TestPassword123!",
            "MySecure@Pass1",
            "Complex#Password9"
        ]
        
        for password in valid_passwords:
            assert password_manager.validate_password_strength(password) is True
    
    def test_validate_password_strength_invalid(self):
        """Test password strength validation with invalid passwords."""
        invalid_passwords = [
            "short",  # Too short
            "nouppercase123!",  # No uppercase
            "NOLOWERCASE123!",  # No lowercase
            "NoNumbers!",  # No numbers
            "NoSpecialChars123",  # No special characters
            "password123!"  # Common password
        ]
        
        for password in invalid_passwords:
            assert password_manager.validate_password_strength(password) is False


class TestSecurityManager:
    """Test JWT token creation and validation."""
    
    def test_create_access_token(self):
        """Test access token creation."""
        user_id = uuid.uuid4()
        school_id = uuid.uuid4()
        permissions = ["admin.all", "user.read"]
        
        token = security_manager.create_access_token(
            subject=user_id,
            school_id=school_id,
            permissions=permissions
        )
        
        assert isinstance(token, str)
        assert len(token) > 100  # JWT tokens are long
        assert token.count('.') == 2  # JWT has 3 parts separated by dots
    
    def test_create_refresh_token(self):
        """Test refresh token creation."""
        user_id = uuid.uuid4()
        school_id = uuid.uuid4()
        
        with patch('app.core.security.redis_client') as mock_redis:
            mock_redis.setex.return_value = True
            
            token = security_manager.create_refresh_token(
                user_id=user_id,
                school_id=school_id
            )
            
            assert isinstance(token, str)
            assert len(token) > 100
            mock_redis.setex.assert_called_once()
    
    def test_verify_token_valid(self):
        """Test token verification with valid token."""
        user_id = uuid.uuid4()
        school_id = uuid.uuid4()
        
        token = security_manager.create_access_token(
            subject=user_id,
            school_id=school_id,
            permissions=[]
        )
        
        payload = security_manager.verify_token(token)
        
        assert payload is not None
        assert payload["sub"] == str(user_id)
        assert payload["school_id"] == str(school_id)
        assert payload["type"] == "access"
    
    def test_verify_token_invalid(self):
        """Test token verification with invalid token."""
        invalid_token = "invalid.jwt.token"
        
        payload = security_manager.verify_token(invalid_token)
        assert payload is None
    
    def test_verify_token_expired(self):
        """Test token verification with expired token."""
        # Create token with very short expiration
        user_id = uuid.uuid4()
        school_id = uuid.uuid4()
        
        with patch('app.core.config.settings.ACCESS_TOKEN_EXPIRE_MINUTES', -1):  # Expired
            token = security_manager.create_access_token(
                subject=user_id,
                school_id=school_id,
                permissions=[]
            )
        
        payload = security_manager.verify_token(token)
        assert payload is None


@pytest.mark.asyncio
class TestAuthService:
    """Test authentication service functionality."""
    
    async def test_authenticate_user_success(self, db_session):
        """Test successful user authentication."""
        # Create test data
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school, password="TestPassword123!")
        
        try:
            # Authenticate user
            result = await auth_service.authenticate_user(
                email=user.email,
                password="TestPassword123!",
                school_id=school.id,
                ip_address="127.0.0.1",
                user_agent="Test Agent"
            )
            
            # Verify result
            assert result["success"] is True
            assert "access_token" in result
            assert "refresh_token" in result
            assert result["user"]["id"] == str(user.id)
            assert result["user"]["email"] == user.email
            
        finally:
            data_manager.cleanup()
    
    async def test_authenticate_user_invalid_email(self, db_session):
        """Test authentication with invalid email."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        
        try:
            with pytest.raises(AuthenticationError, match="Invalid credentials"):
                await auth_service.authenticate_user(
                    email="<EMAIL>",
                    password="TestPassword123!",
                    school_id=school.id,
                    ip_address="127.0.0.1"
                )
        finally:
            data_manager.cleanup()
    
    async def test_authenticate_user_invalid_password(self, db_session):
        """Test authentication with invalid password."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school, password="TestPassword123!")
        
        try:
            with pytest.raises(AuthenticationError, match="Invalid credentials"):
                await auth_service.authenticate_user(
                    email=user.email,
                    password="WrongPassword123!",
                    school_id=school.id,
                    ip_address="127.0.0.1"
                )
        finally:
            data_manager.cleanup()
    
    async def test_authenticate_user_inactive_account(self, db_session):
        """Test authentication with inactive account."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school, password="TestPassword123!", is_active=False)
        
        try:
            with pytest.raises(AuthenticationError, match="Account is inactive"):
                await auth_service.authenticate_user(
                    email=user.email,
                    password="TestPassword123!",
                    school_id=school.id,
                    ip_address="127.0.0.1"
                )
        finally:
            data_manager.cleanup()
    
    async def test_authenticate_user_locked_account(self, db_session):
        """Test authentication with locked account."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(
            school=school, 
            password="TestPassword123!",
            account_locked_until=datetime.utcnow() + timedelta(hours=1)
        )
        
        try:
            with pytest.raises(AuthenticationError, match="Account is temporarily locked"):
                await auth_service.authenticate_user(
                    email=user.email,
                    password="TestPassword123!",
                    school_id=school.id,
                    ip_address="127.0.0.1"
                )
        finally:
            data_manager.cleanup()
    
    async def test_change_password_success(self, db_session):
        """Test successful password change."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school, password="OldPassword123!")
        
        try:
            result = await auth_service.change_password(
                user_id=user.id,
                current_password="OldPassword123!",
                new_password="NewPassword123!"
            )
            
            assert result["success"] is True
            
            # Verify password was changed
            db_session.refresh(user)
            assert user.verify_password("NewPassword123!") is True
            assert user.verify_password("OldPassword123!") is False
            
        finally:
            data_manager.cleanup()
    
    async def test_change_password_invalid_current(self, db_session):
        """Test password change with invalid current password."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school, password="OldPassword123!")
        
        try:
            with pytest.raises(AuthenticationError, match="Current password is incorrect"):
                await auth_service.change_password(
                    user_id=user.id,
                    current_password="WrongPassword123!",
                    new_password="NewPassword123!"
                )
        finally:
            data_manager.cleanup()


@pytest.mark.integration
class TestAuthAPI:
    """Integration tests for authentication API endpoints."""
    
    def test_login_success(self, client, db_session):
        """Test successful login via API."""
        # Create test data
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school, password="TestPassword123!")
        
        api_helper = APITestHelper(client)
        
        try:
            # Login request
            response = api_helper.post_json(
                "/api/v1/auth/login",
                {
                    "email": user.email,
                    "password": "TestPassword123!",
                    "school_subdomain": school.subdomain
                }
            )
            
            # Verify response
            AssertionHelper.assert_response_success(response)
            data = response["data"]
            
            AssertionHelper.assert_has_keys(data, [
                "success", "access_token", "refresh_token", "user", "school"
            ])
            
            assert data["user"]["email"] == user.email
            assert data["school"]["subdomain"] == school.subdomain
            
        finally:
            data_manager.cleanup()
    
    def test_login_invalid_credentials(self, client, db_session):
        """Test login with invalid credentials."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        
        api_helper = APITestHelper(client)
        
        try:
            response = api_helper.post_json(
                "/api/v1/auth/login",
                {
                    "email": "<EMAIL>",
                    "password": "WrongPassword123!",
                    "school_subdomain": school.subdomain
                }
            )
            
            AssertionHelper.assert_response_error(response, 401, "Authentication failed")
            
        finally:
            data_manager.cleanup()
    
    def test_login_missing_subdomain(self, client):
        """Test login without subdomain information."""
        api_helper = APITestHelper(client)
        
        response = api_helper.post_json(
            "/api/v1/auth/login",
            {
                "email": "<EMAIL>",
                "password": "TestPassword123!"
            }
        )
        
        AssertionHelper.assert_response_error(response, 400, "School identification required")
    
    def test_change_password_success(self, client, db_session):
        """Test successful password change via API."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school, password="OldPassword123!")
        
        api_helper = APITestHelper(client)
        auth_headers = api_helper.create_auth_headers(user, school)
        
        try:
            response = api_helper.post_json(
                "/api/v1/auth/change-password",
                {
                    "current_password": "OldPassword123!",
                    "new_password": "NewPassword123!"
                },
                headers=auth_headers
            )
            
            AssertionHelper.assert_response_success(response)
            
        finally:
            data_manager.cleanup()
    
    def test_change_password_unauthorized(self, client):
        """Test password change without authentication."""
        api_helper = APITestHelper(client)
        
        response = api_helper.post_json(
            "/api/v1/auth/change-password",
            {
                "current_password": "OldPassword123!",
                "new_password": "NewPassword123!"
            }
        )
        
        AssertionHelper.assert_response_error(response, 401)
