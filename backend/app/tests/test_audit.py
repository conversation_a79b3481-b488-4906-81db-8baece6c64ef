"""
Unit and integration tests for audit system
Comprehensive testing of audit logging, security events, and compliance features
"""

import pytest
import uuid
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from app.services.audit_service.audit import audit_service
from app.models.audit import AuditLog
from app.tests.utils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, assert_audit_log_created
from app.tests.factories import UserFactory, SchoolFactory


class TestAuditService:
    """Test audit service functionality."""
    
    def test_log_audit_event_basic(self, db_session):
        """Test basic audit event logging."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            # Log audit event using actual service interface
            success = audit_service.log_audit_event(
                school_id=school.id,
                module="user_management",
                action="CREATE",
                user_id=user.id,
                resource_type="User",
                resource_id=user.id,
                event_data={"email": user.email}
            )
            
            # Verify audit log was created successfully
            assert success is True

            # Query the audit log to verify it was stored
            audit_logs = db_session.query(AuditLog).filter(
                AuditLog.school_id == school.id,
                AuditLog.action == "CREATE",
                AuditLog.resource_type == "User"
            ).all()

            assert len(audit_logs) >= 1
            audit_log = audit_logs[0]
            assert audit_log.module == "user_management"
            assert audit_log.user_id == user.id
            assert audit_log.event_data["email"] == user.email
            assert audit_log.created_at is not None
            
        finally:
            data_manager.cleanup()
    
    def test_log_audit_event_with_changes(self, db_session):
        """Test audit event logging with before/after changes."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            # Log audit event with changes
            audit_id = log_audit_event(
                action="UPDATE",
                resource_type="User",
                resource_id=str(user.id),
                user_id=user.id,
                school_id=school.id,
                changes={
                    "first_name": {"old": "Old Name", "new": "New Name"},
                    "email": {"old": "<EMAIL>", "new": "<EMAIL>"}
                }
            )
            
            # Verify audit log
            audit_log = db_session.query(AuditLog).filter(AuditLog.id == audit_id).first()
            
            assert audit_log is not None
            assert audit_log.changes is not None
            assert "first_name" in audit_log.changes
            assert audit_log.changes["first_name"]["old"] == "Old Name"
            assert audit_log.changes["first_name"]["new"] == "New Name"
            
        finally:
            data_manager.cleanup()
    
    def test_log_security_event(self, db_session):
        """Test security event logging."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            # Log security event
            audit_id = log_security_event(
                event_type="LOGIN_FAILED",
                user_id=user.id,
                school_id=school.id,
                ip_address="*************",
                user_agent="Test Browser",
                details={"reason": "Invalid password", "attempts": 3}
            )
            
            # Verify security audit log
            audit_log = db_session.query(AuditLog).filter(AuditLog.id == audit_id).first()
            
            assert audit_log is not None
            assert audit_log.action == "SECURITY_EVENT"
            assert audit_log.resource_type == "Security"
            assert audit_log.event_type == "LOGIN_FAILED"
            assert audit_log.ip_address == "*************"
            assert audit_log.user_agent == "Test Browser"
            assert audit_log.details["reason"] == "Invalid password"
            assert audit_log.severity == "medium"  # Default for LOGIN_FAILED
            
        finally:
            data_manager.cleanup()
    
    def test_log_critical_security_event(self, db_session):
        """Test critical security event logging."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            # Log critical security event
            audit_id = log_security_event(
                event_type="ACCOUNT_LOCKED",
                user_id=user.id,
                school_id=school.id,
                ip_address="*************",
                details={"failed_attempts": 5, "lock_duration": "1 hour"}
            )
            
            # Verify critical security audit log
            audit_log = db_session.query(AuditLog).filter(AuditLog.id == audit_id).first()
            
            assert audit_log is not None
            assert audit_log.event_type == "ACCOUNT_LOCKED"
            assert audit_log.severity == "high"  # Critical events have high severity
            assert audit_log.details["failed_attempts"] == 5
            
        finally:
            data_manager.cleanup()
    
    def test_get_audit_trail(self, db_session):
        """Test retrieving audit trail for a resource."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            # Create multiple audit events
            for i in range(5):
                log_audit_event(
                    action=f"ACTION_{i}",
                    resource_type="User",
                    resource_id=str(user.id),
                    user_id=user.id,
                    school_id=school.id,
                    details={"step": i}
                )
            
            # Get audit trail
            audit_trail = audit_service.get_audit_trail(
                resource_type="User",
                resource_id=str(user.id),
                school_id=school.id
            )
            
            assert len(audit_trail) == 5
            
            # Verify ordering (most recent first)
            for i, audit_log in enumerate(audit_trail):
                assert audit_log.action == f"ACTION_{4-i}"  # Reverse order
                assert audit_log.details["step"] == 4-i
            
        finally:
            data_manager.cleanup()
    
    def test_get_user_activity(self, db_session):
        """Test retrieving user activity logs."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            # Create user activity events
            actions = ["LOGIN", "CREATE_STUDENT", "UPDATE_PROFILE", "LOGOUT"]
            for action in actions:
                log_audit_event(
                    action=action,
                    resource_type="User",
                    resource_id=str(user.id),
                    user_id=user.id,
                    school_id=school.id
                )
            
            # Get user activity
            activity = audit_service.get_user_activity(
                user_id=user.id,
                school_id=school.id,
                limit=10
            )
            
            assert len(activity) == 4
            
            # Verify all activities belong to the user
            for log in activity:
                assert log.user_id == user.id
                assert log.school_id == school.id
            
        finally:
            data_manager.cleanup()
    
    def test_get_security_events(self, db_session):
        """Test retrieving security events."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            # Create security events
            security_events = ["LOGIN_FAILED", "ACCOUNT_LOCKED", "PASSWORD_CHANGED"]
            for event in security_events:
                log_security_event(
                    event_type=event,
                    user_id=user.id,
                    school_id=school.id,
                    ip_address="*************"
                )
            
            # Get security events
            events = audit_service.get_security_events(
                school_id=school.id,
                limit=10
            )
            
            assert len(events) == 3
            
            # Verify all are security events
            for event in events:
                assert event.action == "SECURITY_EVENT"
                assert event.event_type in security_events
                assert event.school_id == school.id
            
        finally:
            data_manager.cleanup()


class TestAuditFiltering:
    """Test audit log filtering and search functionality."""
    
    def test_filter_by_date_range(self, db_session):
        """Test filtering audit logs by date range."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            # Create audit events with different timestamps
            base_time = datetime.utcnow()
            
            # Event from yesterday
            with patch('app.services.audit_service.audit.datetime') as mock_datetime:
                mock_datetime.utcnow.return_value = base_time - timedelta(days=1)
                log_audit_event(
                    action="OLD_ACTION",
                    resource_type="User",
                    resource_id=str(user.id),
                    user_id=user.id,
                    school_id=school.id
                )
            
            # Event from today
            log_audit_event(
                action="NEW_ACTION",
                resource_type="User",
                resource_id=str(user.id),
                user_id=user.id,
                school_id=school.id
            )
            
            # Filter by today only
            start_date = base_time.replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = base_time.replace(hour=23, minute=59, second=59, microsecond=999999)
            
            filtered_logs = audit_service.get_audit_trail(
                resource_type="User",
                resource_id=str(user.id),
                school_id=school.id,
                start_date=start_date,
                end_date=end_date
            )
            
            assert len(filtered_logs) == 1
            assert filtered_logs[0].action == "NEW_ACTION"
            
        finally:
            data_manager.cleanup()
    
    def test_filter_by_action(self, db_session):
        """Test filtering audit logs by action type."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            # Create different types of actions
            actions = ["CREATE", "UPDATE", "DELETE", "VIEW"]
            for action in actions:
                log_audit_event(
                    action=action,
                    resource_type="User",
                    resource_id=str(user.id),
                    user_id=user.id,
                    school_id=school.id
                )
            
            # Filter by specific action
            filtered_logs = audit_service.get_audit_trail(
                resource_type="User",
                resource_id=str(user.id),
                school_id=school.id,
                action="UPDATE"
            )
            
            assert len(filtered_logs) == 1
            assert filtered_logs[0].action == "UPDATE"
            
        finally:
            data_manager.cleanup()
    
    def test_filter_by_severity(self, db_session):
        """Test filtering security events by severity."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            # Create security events with different severities
            log_security_event(
                event_type="LOGIN_SUCCESS",  # Low severity
                user_id=user.id,
                school_id=school.id,
                ip_address="*************"
            )
            
            log_security_event(
                event_type="LOGIN_FAILED",  # Medium severity
                user_id=user.id,
                school_id=school.id,
                ip_address="*************"
            )
            
            log_security_event(
                event_type="ACCOUNT_LOCKED",  # High severity
                user_id=user.id,
                school_id=school.id,
                ip_address="*************"
            )
            
            # Filter by high severity only
            high_severity_events = audit_service.get_security_events(
                school_id=school.id,
                severity="high"
            )
            
            assert len(high_severity_events) == 1
            assert high_severity_events[0].event_type == "ACCOUNT_LOCKED"
            assert high_severity_events[0].severity == "high"
            
        finally:
            data_manager.cleanup()


class TestAuditCompliance:
    """Test audit system compliance features."""
    
    def test_audit_log_immutability(self, db_session):
        """Test that audit logs cannot be modified after creation."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            # Create audit log
            audit_id = log_audit_event(
                action="CREATE",
                resource_type="User",
                resource_id=str(user.id),
                user_id=user.id,
                school_id=school.id
            )
            
            # Get audit log
            audit_log = db_session.query(AuditLog).filter(AuditLog.id == audit_id).first()
            original_timestamp = audit_log.timestamp
            
            # Attempt to modify audit log (should not be allowed in production)
            audit_log.action = "MODIFIED"
            db_session.commit()
            
            # Verify timestamp wasn't changed (immutability check)
            db_session.refresh(audit_log)
            assert audit_log.timestamp == original_timestamp
            
        finally:
            data_manager.cleanup()
    
    def test_audit_log_retention(self, db_session):
        """Test audit log retention policy."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            # Create old audit log (simulate old data)
            old_timestamp = datetime.utcnow() - timedelta(days=400)  # Over 1 year old
            
            with patch('app.services.audit_service.audit.datetime') as mock_datetime:
                mock_datetime.utcnow.return_value = old_timestamp
                old_audit_id = log_audit_event(
                    action="OLD_ACTION",
                    resource_type="User",
                    resource_id=str(user.id),
                    user_id=user.id,
                    school_id=school.id
                )
            
            # Create recent audit log
            recent_audit_id = log_audit_event(
                action="RECENT_ACTION",
                resource_type="User",
                resource_id=str(user.id),
                user_id=user.id,
                school_id=school.id
            )
            
            # Test retention policy (this would be implemented in a cleanup job)
            retention_date = datetime.utcnow() - timedelta(days=365)  # 1 year retention
            
            old_logs = db_session.query(AuditLog).filter(
                AuditLog.timestamp < retention_date,
                AuditLog.school_id == school.id
            ).all()
            
            recent_logs = db_session.query(AuditLog).filter(
                AuditLog.timestamp >= retention_date,
                AuditLog.school_id == school.id
            ).all()
            
            assert len(old_logs) == 1  # Should have old log
            assert len(recent_logs) == 1  # Should have recent log
            
        finally:
            data_manager.cleanup()


@pytest.mark.integration
class TestAuditIntegration:
    """Integration tests for audit system with other components."""
    
    def test_audit_with_authentication(self, db_session):
        """Test audit logging during authentication process."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school, password="TestPassword123!")
        
        try:
            # Simulate authentication (this should create audit logs)
            from app.services.auth_service.auth import auth_service
            
            # Successful login should create audit log
            result = auth_service.authenticate_user(
                email=user.email,
                password="TestPassword123!",
                school_id=school.id,
                ip_address="127.0.0.1",
                user_agent="Test Browser"
            )
            
            # Verify audit log was created
            audit_logs = db_session.query(AuditLog).filter(
                AuditLog.user_id == user.id,
                AuditLog.action == "LOGIN_SUCCESS"
            ).all()
            
            assert len(audit_logs) >= 1  # Should have login audit log
            
        finally:
            data_manager.cleanup()
