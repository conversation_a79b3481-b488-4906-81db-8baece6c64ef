"""
Factory classes for generating test data using Factory Boy
Production-ready test data factories with realistic data generation
"""

import factory
import uuid
from datetime import datetime, timedelta, date
from factory import fuzzy
from sqlalchemy.orm import Session

from app.models.base import Organization, School
from app.models.auth import User, Role, Permission, UserSession
from app.models.academic import AcademicYear, Class, Section, ClassSectionMapping, Subject, ClassSubject
from app.core.database import get_db_context


class BaseFactory(factory.alchemy.SQLAlchemyModelFactory):
    """Base factory with common configuration."""
    
    class Meta:
        abstract = True
        sqlalchemy_session_persistence = "commit"
    
    @classmethod
    def _create(cls, model_class, *args, **kwargs):
        """Override create to use our database context."""
        with get_db_context() as session:
            cls._meta.sqlalchemy_session = session
            instance = super()._create(model_class, *args, **kwargs)
            session.commit()
            session.refresh(instance)
            return instance


class OrganizationFactory(BaseFactory):
    """Factory for creating test organizations."""
    
    class Meta:
        model = Organization
    
    id = factory.LazyFunction(uuid.uuid4)
    name = factory.Sequence(lambda n: f"Test Organization {n}")
    display_name = factory.LazyAttribute(lambda obj: f"{obj.name} Display")
    description = factory.Faker("text", max_nb_chars=200)
    subdomain = factory.Sequence(lambda n: f"testorg{n}")
    email = factory.LazyAttribute(lambda obj: f"contact@{obj.subdomain}.com")
    phone = factory.Faker("phone_number")
    website = factory.LazyAttribute(lambda obj: f"https://{obj.subdomain}.com")
    
    # Address
    address_line1 = factory.Faker("street_address")
    address_line2 = factory.Faker("secondary_address")
    city = factory.Faker("city")
    state = factory.Faker("state")
    country = "India"
    postal_code = factory.Faker("postcode")
    
    # Organization type
    org_type = "school"
    
    # Status
    is_active = True
    
    # Localization defaults
    language = "en"
    timezone = "Asia/Kolkata"
    currency = "INR"
    date_format = "DD/MM/YYYY"
    time_format = "HH:mm"


class SchoolFactory(BaseFactory):
    """Factory for creating test schools."""
    
    class Meta:
        model = School
    
    id = factory.LazyFunction(uuid.uuid4)
    school_id = factory.LazyAttribute(lambda obj: obj.id)  # Self-reference for multi-tenant
    organization = factory.SubFactory(OrganizationFactory)
    organization_id = factory.LazyAttribute(lambda obj: obj.organization.id)
    
    # Basic information
    name = factory.Sequence(lambda n: f"Test School {n}")
    display_name = factory.LazyAttribute(lambda obj: f"{obj.name} Display")
    short_name = factory.LazyAttribute(lambda obj: f"TS{obj.name.split()[-1]}")
    description = factory.Faker("text", max_nb_chars=200)
    
    # School identification
    school_code = factory.Sequence(lambda n: f"SCH{n:04d}")
    subdomain = factory.Sequence(lambda n: f"testschool{n}")
    registration_number = factory.Sequence(lambda n: f"REG{n:06d}")
    affiliation_number = factory.Sequence(lambda n: f"AFF{n:06d}")
    
    # Educational details
    board_type = factory.fuzzy.FuzzyChoice(["CBSE", "ICSE", "State Board", "IB"])
    school_type = "school"
    establishment_year = factory.fuzzy.FuzzyInteger(1950, 2020)
    
    # Contact information
    email = factory.LazyAttribute(lambda obj: f"admin@{obj.subdomain}.com")
    phone = factory.Faker("phone_number")
    website = factory.LazyAttribute(lambda obj: f"https://{obj.subdomain}.com")
    
    # Address
    address_line1 = factory.Faker("street_address")
    address_line2 = factory.Faker("secondary_address")
    city = factory.Faker("city")
    state = factory.Faker("state")
    country = "India"
    postal_code = factory.Faker("postcode")
    
    # Branding
    logo_url = factory.LazyAttribute(lambda obj: f"https://cdn.example.com/{obj.subdomain}/logo.png")
    favicon_url = factory.LazyAttribute(lambda obj: f"https://cdn.example.com/{obj.subdomain}/favicon.ico")
    primary_color = factory.Faker("hex_color")
    secondary_color = factory.Faker("hex_color")
    
    # Status and configuration
    is_active = True
    is_verified = True
    
    # Academic configuration
    academic_year_start_month = 4  # April for Indian schools
    
    # Localization
    language = "en"
    timezone = "Asia/Kolkata"
    currency = "INR"
    date_format = "DD/MM/YYYY"
    time_format = "HH:mm"


class UserFactory(BaseFactory):
    """Factory for creating test users."""
    
    class Meta:
        model = User
    
    id = factory.LazyFunction(uuid.uuid4)
    school = factory.SubFactory(SchoolFactory)
    school_id = factory.LazyAttribute(lambda obj: obj.school.id)
    
    # Basic information
    email = factory.Sequence(lambda n: f"user{n}@testschool.com")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    phone = factory.Faker("phone_number")
    
    # User type
    user_type = factory.fuzzy.FuzzyChoice(["admin", "teacher", "accountant", "parent", "student"])
    
    # Status
    is_active = True
    is_verified = True
    is_superuser = False
    
    # Security
    failed_login_attempts = 0
    account_locked_until = None
    last_login = factory.LazyFunction(datetime.utcnow)
    password_changed_at = factory.LazyFunction(datetime.utcnow)
    
    # Localization
    language = "en"
    timezone = "Asia/Kolkata"
    currency = "INR"
    date_format = "DD/MM/YYYY"
    time_format = "HH:mm"
    
    @factory.post_generation
    def password(self, create, extracted, **kwargs):
        """Set password after user creation."""
        if not create:
            return
        
        password = extracted or "TestPassword123!"
        self.set_password(password)


class RoleFactory(BaseFactory):
    """Factory for creating test roles."""
    
    class Meta:
        model = Role
    
    id = factory.LazyFunction(uuid.uuid4)
    school = factory.SubFactory(SchoolFactory)
    school_id = factory.LazyAttribute(lambda obj: obj.school.id)
    
    name = factory.Sequence(lambda n: f"role_{n}")
    display_name = factory.LazyAttribute(lambda obj: obj.name.replace("_", " ").title())
    description = factory.Faker("text", max_nb_chars=100)
    
    # Hierarchy
    level = factory.fuzzy.FuzzyInteger(1, 10)
    parent_role_id = None
    
    # Status
    is_active = True
    is_system_role = False


class PermissionFactory(BaseFactory):
    """Factory for creating test permissions."""
    
    class Meta:
        model = Permission
    
    id = factory.LazyFunction(uuid.uuid4)
    school = factory.SubFactory(SchoolFactory)
    school_id = factory.LazyAttribute(lambda obj: obj.school.id)
    
    code = factory.Sequence(lambda n: f"permission_{n}")
    name = factory.LazyAttribute(lambda obj: obj.code.replace("_", " ").title())
    description = factory.Faker("text", max_nb_chars=100)
    
    category = factory.fuzzy.FuzzyChoice(["admin", "academic", "finance", "communication"])
    resource = factory.fuzzy.FuzzyChoice(["user", "student", "class", "fee", "exam", "report"])
    action = factory.fuzzy.FuzzyChoice(["create", "read", "update", "delete", "all"])
    
    # Status
    is_active = True
    is_system_permission = False


class UserSessionFactory(BaseFactory):
    """Factory for creating test user sessions."""
    
    class Meta:
        model = UserSession
    
    id = factory.LazyFunction(uuid.uuid4)
    user = factory.SubFactory(UserFactory)
    user_id = factory.LazyAttribute(lambda obj: obj.user.id)
    school_id = factory.LazyAttribute(lambda obj: obj.user.school_id)
    
    session_id = factory.LazyFunction(lambda: str(uuid.uuid4()))
    ip_address = factory.Faker("ipv4")
    user_agent = factory.Faker("user_agent")
    login_method = "password"
    
    # Session timing
    login_time = factory.LazyFunction(datetime.utcnow)
    last_activity = factory.LazyFunction(datetime.utcnow)
    logout_time = None
    
    # Status
    is_active = True


# Specialized factories for common test scenarios
class AdminUserFactory(UserFactory):
    """Factory for creating admin users."""
    user_type = "admin"
    is_superuser = True


class TeacherUserFactory(UserFactory):
    """Factory for creating teacher users."""
    user_type = "teacher"


class ParentUserFactory(UserFactory):
    """Factory for creating parent users."""
    user_type = "parent"


class StudentUserFactory(UserFactory):
    """Factory for creating student users."""
    user_type = "student"


class AdminRoleFactory(RoleFactory):
    """Factory for creating admin roles."""
    name = "admin"
    display_name = "Administrator"
    description = "Full system access"
    level = 1
    is_system_role = True


class TeacherRoleFactory(RoleFactory):
    """Factory for creating teacher roles."""
    name = "teacher"
    display_name = "Teacher"
    description = "Teaching and academic access"
    level = 3
    is_system_role = True


# Batch creation utilities
def create_test_school_with_users(num_users=5, **kwargs):
    """Create a school with multiple users for testing."""
    school = SchoolFactory(**kwargs)
    users = []
    
    # Create admin user
    admin_user = AdminUserFactory(school=school)
    users.append(admin_user)
    
    # Create other users
    for i in range(num_users - 1):
        user = UserFactory(school=school)
        users.append(user)
    
    return school, users


def create_complete_rbac_setup(school=None):
    """Create a complete RBAC setup with roles and permissions."""
    if not school:
        school = SchoolFactory()
    
    # Create roles
    admin_role = AdminRoleFactory(school=school)
    teacher_role = TeacherRoleFactory(school=school)
    
    # Create permissions
    admin_permission = PermissionFactory(
        school=school,
        code="admin.all",
        name="Admin All Permission",
        category="admin",
        resource="all",
        action="all"
    )
    
    teacher_permission = PermissionFactory(
        school=school,
        code="teacher.academic",
        name="Teacher Academic Permission",
        category="academic",
        resource="class",
        action="read"
    )

    return {
        "school": school,
        "roles": [admin_role, teacher_role],
        "permissions": [admin_permission, teacher_permission]
    }


# Academic structure factories

class AcademicYearFactory(factory.alchemy.SQLAlchemyModelFactory):
    """Factory for creating academic year test data."""

    class Meta:
        model = AcademicYear
        sqlalchemy_session_persistence = "commit"

    id = factory.LazyFunction(uuid.uuid4)
    school = factory.SubFactory(SchoolFactory)
    school_id = factory.LazyAttribute(lambda obj: obj.school.id)

    # Basic information
    year_label = factory.Sequence(lambda n: f"202{n % 10}-{(n % 10) + 1}")
    display_name = factory.LazyAttribute(lambda obj: f"Academic Year {obj.year_label}")
    description = factory.Faker("sentence")

    # Academic year dates (Indian pattern: April-March)
    start_date = factory.LazyAttribute(lambda obj: datetime(2024, 4, 1).date())
    end_date = factory.LazyAttribute(lambda obj: datetime(2025, 3, 31).date())

    # Status
    is_active = False  # Only one active per school
    status = "draft"

    # Academic configuration
    total_working_days = factory.fuzzy.FuzzyInteger(200, 250)
    actual_working_days = 0

    # Configuration
    terms_config = factory.LazyFunction(lambda: {
        "terms": [
            {"name": "Term 1", "start_date": "2024-04-01", "end_date": "2024-09-30"},
            {"name": "Term 2", "start_date": "2024-10-01", "end_date": "2025-03-31"}
        ]
    })
    holidays_config = factory.LazyFunction(lambda: {
        "holidays": [
            {"name": "Summer Break", "start_date": "2024-05-15", "end_date": "2024-06-15"},
            {"name": "Winter Break", "start_date": "2024-12-25", "end_date": "2025-01-05"}
        ]
    })
    settings = factory.LazyFunction(lambda: {"auto_promotion": True, "attendance_required": True})

    # Approval
    academic_calendar_approved = False
    approved_by = None
    approved_at = None


class ActiveAcademicYearFactory(AcademicYearFactory):
    """Factory for creating active academic year."""
    is_active = True
    status = "active"
    academic_calendar_approved = True


class ClassFactory(factory.alchemy.SQLAlchemyModelFactory):
    """Factory for creating class test data."""

    class Meta:
        model = Class
        sqlalchemy_session_persistence = "commit"

    id = factory.LazyFunction(uuid.uuid4)
    school = factory.SubFactory(SchoolFactory)
    school_id = factory.LazyAttribute(lambda obj: obj.school.id)

    # Basic information
    name = factory.Sequence(lambda n: f"Class {n}")
    display_name = factory.LazyAttribute(lambda obj: obj.name)
    short_name = factory.Sequence(lambda n: f"{n}")
    description = factory.Faker("sentence")

    # Class properties
    class_code = factory.Sequence(lambda n: f"CLS{n:03d}")
    display_order = factory.Sequence(lambda n: n)
    academic_level = factory.fuzzy.FuzzyChoice(["Primary", "Secondary", "Higher Secondary"])

    # Status
    is_active = True

    # Configuration
    max_students_per_section = factory.fuzzy.FuzzyInteger(30, 50)
    settings = factory.LazyFunction(lambda: {"subjects_required": True, "attendance_tracking": True})


class SectionFactory(factory.alchemy.SQLAlchemyModelFactory):
    """Factory for creating section test data."""

    class Meta:
        model = Section
        sqlalchemy_session_persistence = "commit"

    id = factory.LazyFunction(uuid.uuid4)
    school = factory.SubFactory(SchoolFactory)
    school_id = factory.LazyAttribute(lambda obj: obj.school.id)

    # Basic information
    name = factory.fuzzy.FuzzyChoice(["A", "B", "C", "D", "Red", "Blue", "Green"])
    display_name = factory.LazyAttribute(lambda obj: f"Section {obj.name}")
    description = factory.Faker("sentence")

    # Section properties
    section_code = factory.Sequence(lambda n: f"SEC{n:03d}")
    max_capacity = factory.fuzzy.FuzzyInteger(30, 50)
    current_strength = factory.fuzzy.FuzzyInteger(0, 30)

    # Status
    is_active = True

    # Configuration
    settings = factory.LazyFunction(lambda: {"mixed_gender": True, "special_needs_support": False})


class ClassSectionMappingFactory(factory.alchemy.SQLAlchemyModelFactory):
    """Factory for creating class-section mapping test data."""

    class Meta:
        model = ClassSectionMapping
        sqlalchemy_session_persistence = "commit"

    id = factory.LazyFunction(uuid.uuid4)
    school = factory.SubFactory(SchoolFactory)
    school_id = factory.LazyAttribute(lambda obj: obj.school.id)

    # Relationships
    academic_year = factory.SubFactory(AcademicYearFactory)
    academic_year_id = factory.LazyAttribute(lambda obj: obj.academic_year.id)
    class_ref = factory.SubFactory(ClassFactory)
    class_id = factory.LazyAttribute(lambda obj: obj.class_ref.id)
    section = factory.SubFactory(SectionFactory)
    section_id = factory.LazyAttribute(lambda obj: obj.section.id)

    # Optional assignments
    class_teacher_id = None  # Will be set when teacher module is implemented
    room_number = factory.Sequence(lambda n: f"Room {n:03d}")

    # Status
    is_active = True

    # Mapping configuration
    max_students = None  # Use section's max_capacity
    current_students = factory.fuzzy.FuzzyInteger(0, 30)

    # Configuration
    settings = factory.LazyFunction(lambda: {"timetable_assigned": False, "subjects_mapped": False})


class SubjectFactory(BaseFactory):
    """Factory for creating Subject instances."""

    class Meta:
        model = Subject

    # Basic Information
    subject_code = factory.Sequence(lambda n: f"SUB{n:03d}")
    name = factory.Faker('word')
    description = factory.Faker('text', max_nb_chars=200)

    # Subject Classification
    subject_type = factory.fuzzy.FuzzyChoice(['core', 'elective', 'extra_curricular'])
    academic_level = factory.fuzzy.FuzzyChoice(['primary', 'secondary', 'higher_secondary'])

    # Subject Configuration
    is_active = True
    display_order = factory.Sequence(lambda n: n)

    # Academic Settings
    default_credits = factory.fuzzy.FuzzyInteger(1, 6)
    default_hours_per_week = factory.fuzzy.FuzzyInteger(2, 8)

    # Configuration
    settings = factory.LazyFunction(lambda: {"practical_required": False, "lab_sessions": 0})

    # Relationships - will be set by test
    school_id = None


class CoreSubjectFactory(SubjectFactory):
    """Factory for creating core Subject instances."""

    subject_type = "core"
    name = factory.fuzzy.FuzzyChoice(['Mathematics', 'English', 'Science', 'Social Studies', 'Hindi'])
    default_credits = factory.fuzzy.FuzzyInteger(4, 6)
    default_hours_per_week = factory.fuzzy.FuzzyInteger(5, 8)


class ElectiveSubjectFactory(SubjectFactory):
    """Factory for creating elective Subject instances."""

    subject_type = "elective"
    name = factory.fuzzy.FuzzyChoice(['Computer Science', 'Art', 'Music', 'Physical Education', 'French'])
    default_credits = factory.fuzzy.FuzzyInteger(2, 4)
    default_hours_per_week = factory.fuzzy.FuzzyInteger(2, 4)


class ClassSubjectFactory(BaseFactory):
    """Factory for creating ClassSubject instances."""

    class Meta:
        model = ClassSubject

    # Foreign Key Relationships
    academic_year = factory.SubFactory(AcademicYearFactory)
    academic_year_id = factory.LazyAttribute(lambda obj: obj.academic_year.id)

    class_ref = factory.SubFactory(ClassFactory)
    class_id = factory.LazyAttribute(lambda obj: obj.class_ref.id)

    subject = factory.SubFactory(SubjectFactory)
    subject_id = factory.LazyAttribute(lambda obj: obj.subject.id)

    # Teacher Assignment (optional)
    teacher = factory.SubFactory(UserFactory)
    teacher_id = factory.LazyAttribute(lambda obj: obj.teacher.id)

    # Subject Configuration
    is_mandatory = True
    is_active = True

    # Academic Configuration
    credits = factory.LazyAttribute(lambda obj: obj.subject.default_credits)
    hours_per_week = factory.LazyAttribute(lambda obj: obj.subject.default_hours_per_week)
    max_marks = factory.fuzzy.FuzzyInteger(80, 100)
    pass_marks = factory.LazyAttribute(lambda obj: int(obj.max_marks * 0.4) if obj.max_marks else 40)

    # Scheduling
    display_order = factory.Sequence(lambda n: n)

    # Configuration
    settings = factory.LazyFunction(lambda: {"assessment_type": "written", "practical_marks": 0})

    # Metadata
    assigned_at = factory.LazyFunction(date.today)

    # Relationships
    school = factory.LazyAttribute(lambda obj: obj.academic_year.school)
    school_id = factory.LazyAttribute(lambda obj: obj.school.id)


class MandatoryClassSubjectFactory(ClassSubjectFactory):
    """Factory for creating mandatory ClassSubject instances."""

    is_mandatory = True
    subject = factory.SubFactory(CoreSubjectFactory)
    max_marks = 100
    pass_marks = 40


class ElectiveClassSubjectFactory(ClassSubjectFactory):
    """Factory for creating elective ClassSubject instances."""

    is_mandatory = False
    subject = factory.SubFactory(ElectiveSubjectFactory)
    max_marks = factory.fuzzy.FuzzyInteger(50, 80)
    pass_marks = factory.LazyAttribute(lambda obj: int(obj.max_marks * 0.35) if obj.max_marks else 30)
