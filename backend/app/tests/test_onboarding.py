"""
Comprehensive tests for onboarding system
World-class testing of school registration, validation, and setup processes
"""

import pytest
import uuid
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from app.services.onboarding_service.onboarding import onboarding_service, OnboardingError
from app.models.base import Organization, School
from app.models.auth import User, Role, Permission, UserRole, RolePermission
from app.tests.utils import TestDataManager, APITestHelper, AssertionHelper
from app.tests.factories import OrganizationFactory, SchoolFactory, UserFactory


class TestOnboardingService:
    """Test onboarding service functionality"""
    
    def test_register_school_single_branch_success(self, db_session):
        """Test successful single-branch school registration"""
        data_manager = TestDataManager(db_session)
        
        try:
            # Test data
            registration_data = {
                "school_name": "Test Primary School",
                "admin_email": "<EMAIL>",
                "admin_password": "TestPassword123!",
                "preferred_subdomain": "testprimary",
                "country": "India",
                "phone": "+91-**********",
                "is_multi_branch": False,
                "admin_first_name": "<PERSON>",
                "admin_last_name": "<PERSON><PERSON>",
                "ip_address": "127.0.0.1",
                "user_agent": "Test Browser"
            }
            
            # Register school
            result = onboarding_service.register_school(**registration_data)
            
            # Verify result structure
            assert result["success"] is True
            assert "organization" in result
            assert "school" in result
            assert "admin_user" in result
            assert "trial_license" in result
            assert "academic_year" in result
            assert "next_steps" in result
            
            # Verify organization
            org_data = result["organization"]
            AssertionHelper.assert_valid_uuid(org_data["id"])
            assert org_data["name"] == "Test Primary School Organization"
            assert org_data["subdomain"] is None  # Single branch doesn't get org subdomain
            
            # Verify school
            school_data = result["school"]
            AssertionHelper.assert_valid_uuid(school_data["id"])
            assert school_data["name"] == "Test Primary School"
            assert school_data["subdomain"] == "testprimary"
            assert school_data["school_code"].startswith("TES")
            
            # Verify admin user
            user_data = result["admin_user"]
            AssertionHelper.assert_valid_uuid(user_data["id"])
            assert user_data["email"] == "<EMAIL>"
            assert user_data["first_name"] == "John"
            assert user_data["last_name"] == "Doe"
            
            # Verify trial license
            license_data = result["trial_license"]
            assert license_data["plan_type"] == "trial"
            assert license_data["max_students"] == 50
            assert license_data["max_staff"] == 10
            assert len(license_data["enabled_features"]) > 0
            
            # Verify academic year
            academic_data = result["academic_year"]
            assert academic_data["is_active"] is True
            assert "2024-25" in academic_data["label"] or "2025-26" in academic_data["label"]
            
            # Verify database records
            org_id = uuid.UUID(org_data["id"])
            school_id = uuid.UUID(school_data["id"])
            user_id = uuid.UUID(user_data["id"])
            
            # Check organization exists
            org = data_manager.assert_record_exists(Organization, org_id)
            assert org.name == "Test Primary School Organization"
            assert org.country == "India"
            
            # Check school exists
            school = data_manager.assert_record_exists(School, school_id)
            assert school.name == "Test Primary School"
            assert school.subdomain == "testprimary"
            assert school.organization_id == org_id
            assert school.school_id == school_id  # Multi-tenant self-reference
            
            # Check admin user exists
            user = data_manager.assert_record_exists(User, user_id)
            assert user.email == "<EMAIL>"
            assert user.school_id == school_id
            assert user.user_type == "admin"
            assert user.is_superuser is True
            
            # Check password is hashed
            assert user.password_hash != "TestPassword123!"
            assert user.verify_password("TestPassword123!") is True
            
            # Check roles were created
            roles = db_session.query(Role).filter(Role.school_id == school_id).all()
            assert len(roles) >= 5  # admin, teacher, accountant, parent, student
            
            role_names = [r.name for r in roles]
            assert "admin" in role_names
            assert "teacher" in role_names
            assert "accountant" in role_names
            
            # Check permissions were created
            permissions = db_session.query(Permission).filter(Permission.school_id == school_id).all()
            assert len(permissions) >= 6
            
            permission_codes = [p.code for p in permissions]
            assert "admin.all" in permission_codes
            assert "user.manage" in permission_codes
            
            # Check admin user has admin role
            user_roles = db_session.query(UserRole).filter(
                UserRole.user_id == user_id,
                UserRole.is_active == True
            ).all()
            assert len(user_roles) >= 1
            
            admin_role = next(r for r in roles if r.name == "admin")
            admin_user_role = next(ur for ur in user_roles if ur.role_id == admin_role.id)
            assert admin_user_role is not None
            
        finally:
            data_manager.cleanup()
    
    def test_register_school_multi_branch_success(self, db_session):
        """Test successful multi-branch organization registration"""
        data_manager = TestDataManager(db_session)
        
        try:
            registration_data = {
                "school_name": "ABC Education Trust",
                "admin_email": "<EMAIL>",
                "admin_password": "SecurePass123!",
                "preferred_subdomain": "abcedu",
                "country": "India",
                "is_multi_branch": True,
                "admin_first_name": "Jane",
                "admin_last_name": "Smith"
            }
            
            result = onboarding_service.register_school(**registration_data)
            
            # Verify multi-branch specific behavior
            org_data = result["organization"]
            assert org_data["name"] == "ABC Education Trust"
            assert org_data["subdomain"] == "abcedu"  # Multi-branch gets org subdomain
            
            school_data = result["school"]
            assert school_data["name"] == "ABC Education Trust"
            assert school_data["subdomain"] == "abcedu"
            
            # Verify organization type
            org_id = uuid.UUID(org_data["id"])
            org = data_manager.assert_record_exists(Organization, org_id)
            assert org.org_type == "trust"  # Multi-branch organizations are trusts
            
        finally:
            data_manager.cleanup()
    
    def test_register_school_duplicate_email_error(self, db_session):
        """Test registration fails with duplicate email"""
        data_manager = TestDataManager(db_session)
        
        try:
            # Create existing user
            school = data_manager.create_school()
            existing_user = data_manager.create_user(
                school=school,
                email="<EMAIL>"
            )
            
            # Attempt registration with same email
            with pytest.raises(OnboardingError, match="Email address is already registered"):
                onboarding_service.register_school(
                    school_name="New School",
                    admin_email="<EMAIL>",
                    admin_password="TestPassword123!",
                    preferred_subdomain="newschool"
                )
                
        finally:
            data_manager.cleanup()
    
    def test_register_school_invalid_subdomain_error(self, db_session):
        """Test registration fails with invalid subdomain"""
        data_manager = TestDataManager(db_session)
        
        try:
            # Test with taken subdomain
            existing_school = data_manager.create_school(subdomain="takenschool")
            
            with pytest.raises(OnboardingError, match="not available"):
                onboarding_service.register_school(
                    school_name="New School",
                    admin_email="<EMAIL>",
                    admin_password="TestPassword123!",
                    preferred_subdomain="takenschool"
                )
                
        finally:
            data_manager.cleanup()
    
    def test_register_school_weak_password_error(self, db_session):
        """Test registration fails with weak password"""
        with pytest.raises(OnboardingError, match="Password must be at least 8 characters"):
            onboarding_service.register_school(
                school_name="Test School",
                admin_email="<EMAIL>",
                admin_password="weak",
                preferred_subdomain="testschool"
            )
    
    def test_register_school_invalid_email_error(self, db_session):
        """Test registration fails with invalid email"""
        with pytest.raises(OnboardingError, match="Invalid email format"):
            onboarding_service.register_school(
                school_name="Test School",
                admin_email="invalid-email",
                admin_password="TestPassword123!",
                preferred_subdomain="testschool"
            )
    
    def test_register_school_empty_school_name_error(self, db_session):
        """Test registration fails with empty school name"""
        with pytest.raises(OnboardingError, match="School name must be at least 2 characters"):
            onboarding_service.register_school(
                school_name="",
                admin_email="<EMAIL>",
                admin_password="TestPassword123!",
                preferred_subdomain="testschool"
            )
    
    def test_generate_school_code_unique(self, db_session):
        """Test school code generation creates unique codes"""
        data_manager = TestDataManager(db_session)
        
        try:
            # Create multiple schools with similar names
            school1 = data_manager.create_school(name="Test School")
            school2 = data_manager.create_school(name="Test School")
            school3 = data_manager.create_school(name="Test School")
            
            # Verify all have different school codes
            codes = [school1.school_code, school2.school_code, school3.school_code]
            assert len(set(codes)) == 3  # All unique
            
            # Verify format
            for code in codes:
                assert len(code) >= 6
                assert code.isupper()
                
        finally:
            data_manager.cleanup()
    
    def test_check_subdomain_availability_available(self, db_session):
        """Test subdomain availability check for available subdomain"""
        result = onboarding_service.check_subdomain_availability("availablesubdomain")
        
        assert result["is_available"] is True
        assert result["subdomain"] == "availablesubdomain"
        assert result.get("suggestions") is None
    
    def test_check_subdomain_availability_taken(self, db_session):
        """Test subdomain availability check for taken subdomain"""
        data_manager = TestDataManager(db_session)
        
        try:
            # Create school with subdomain
            school = data_manager.create_school(subdomain="takenschool")
            
            result = onboarding_service.check_subdomain_availability("takenschool")
            
            assert result["is_available"] is False
            assert result["subdomain"] == "takenschool"
            assert isinstance(result.get("suggestions"), list)
            assert len(result["suggestions"]) > 0
            
        finally:
            data_manager.cleanup()
    
    def test_get_subdomain_suggestions(self, db_session):
        """Test subdomain suggestions generation"""
        suggestions = onboarding_service.get_subdomain_suggestions("My Test School", limit=5)
        
        assert isinstance(suggestions, list)
        assert len(suggestions) <= 5
        assert len(suggestions) > 0
        
        # All suggestions should be valid subdomains
        for suggestion in suggestions:
            assert isinstance(suggestion, str)
            assert len(suggestion) >= 3
            assert suggestion.islower()
    
    def test_academic_year_calculation_april_onwards(self, db_session):
        """Test academic year calculation for April onwards"""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            
            # Mock current date to April (new academic year start)
            with patch('app.services.onboarding_service.onboarding.datetime') as mock_datetime:
                mock_datetime.now.return_value = datetime(2024, 4, 15)  # April 15, 2024
                
                academic_year = onboarding_service._create_default_academic_year(school, db_session)
                
                assert academic_year["label"] == "2024-25"
                assert "2024-04-01" in academic_year["start_date"]
                assert "2025-03-31" in academic_year["end_date"]
                assert academic_year["is_active"] is True
                
        finally:
            data_manager.cleanup()
    
    def test_academic_year_calculation_january_march(self, db_session):
        """Test academic year calculation for January-March"""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            
            # Mock current date to February (previous academic year)
            with patch('app.services.onboarding_service.onboarding.datetime') as mock_datetime:
                mock_datetime.now.return_value = datetime(2024, 2, 15)  # February 15, 2024
                
                academic_year = onboarding_service._create_default_academic_year(school, db_session)
                
                assert academic_year["label"] == "2023-24"
                assert "2023-04-01" in academic_year["start_date"]
                assert "2024-03-31" in academic_year["end_date"]
                
        finally:
            data_manager.cleanup()
    
    def test_trial_license_assignment(self, db_session):
        """Test trial license assignment with proper configuration"""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            
            trial_license = onboarding_service._assign_trial_license(school, db_session)
            
            assert trial_license["plan_type"] == "trial"
            assert trial_license["plan_name"] == "Trial Plan"
            assert trial_license["is_active"] is True
            assert trial_license["max_students"] == 50
            assert trial_license["max_staff"] == 10
            assert len(trial_license["enabled_features"]) >= 4
            
            # Verify dates
            start_date = datetime.fromisoformat(trial_license["start_date"])
            end_date = datetime.fromisoformat(trial_license["end_date"])
            
            assert end_date > start_date
            assert (end_date - start_date).days == 14  # Default trial period
            
        finally:
            data_manager.cleanup()


@pytest.mark.integration
class TestOnboardingAPI:
    """Integration tests for onboarding API endpoints"""
    
    def test_register_school_api_success(self, client, db_session):
        """Test successful school registration via API"""
        data_manager = TestDataManager(db_session)
        api_helper = APITestHelper(client)
        
        try:
            registration_data = {
                "school_name": "API Test School",
                "admin_email": "<EMAIL>",
                "admin_password": "ApiTestPass123!",
                "preferred_subdomain": "apitest",
                "country": "India",
                "phone": "+91-**********",
                "is_multi_branch": False,
                "admin_first_name": "API",
                "admin_last_name": "Tester"
            }
            
            response = api_helper.post_json("/api/v1/onboarding/register", registration_data)
            
            AssertionHelper.assert_response_success(response, 201)
            data = response["data"]
            
            # Verify response structure
            AssertionHelper.assert_has_keys(data, [
                "success", "message", "organization", "school", 
                "admin_user", "trial_license", "academic_year", "next_steps"
            ])
            
            assert data["success"] is True
            assert data["school"]["name"] == "API Test School"
            assert data["school"]["subdomain"] == "apitest"
            assert data["admin_user"]["email"] == "<EMAIL>"
            
        finally:
            data_manager.cleanup()
    
    def test_register_school_api_validation_error(self, client):
        """Test registration API with validation errors"""
        api_helper = APITestHelper(client)
        
        # Test with invalid email
        invalid_data = {
            "school_name": "Test School",
            "admin_email": "invalid-email",
            "admin_password": "TestPassword123!",
            "preferred_subdomain": "testschool"
        }
        
        response = api_helper.post_json("/api/v1/onboarding/register", invalid_data)
        AssertionHelper.assert_response_error(response, 422)  # Validation error
    
    def test_check_subdomain_api_available(self, client):
        """Test subdomain availability check API"""
        api_helper = APITestHelper(client)
        
        response = api_helper.post_json(
            "/api/v1/onboarding/check-subdomain",
            {"subdomain": "availabletest"}
        )
        
        AssertionHelper.assert_response_success(response)
        data = response["data"]
        
        assert data["is_valid"] is True
        assert data["is_available"] is True
        assert data["subdomain"] == "availabletest"
    
    def test_subdomain_suggestions_api(self, client):
        """Test subdomain suggestions API"""
        api_helper = APITestHelper(client)
        
        response = api_helper.post_json(
            "/api/v1/onboarding/subdomain-suggestions",
            {"school_name": "My Test School", "limit": 3}
        )
        
        AssertionHelper.assert_response_success(response)
        data = response["data"]
        
        assert "suggestions" in data
        assert isinstance(data["suggestions"], list)
        assert len(data["suggestions"]) <= 3
        assert data["school_name"] == "My Test School"
    
    def test_health_check_api(self, client):
        """Test onboarding service health check"""
        api_helper = APITestHelper(client)
        
        response = api_helper.get_json("/api/v1/onboarding/health")
        
        AssertionHelper.assert_response_success(response)
        data = response["data"]
        
        assert data["status"] == "healthy"
        assert data["service"] == "onboarding"
