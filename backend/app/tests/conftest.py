"""
Pytest configuration and fixtures for School ERP Backend
Production-ready test setup with database isolation and comprehensive fixtures
"""

import asyncio
import os
import pytest
import uuid
from typing import Generator, Dict, Any
from unittest.mock import Mock
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Import application components
from app.main import app
from app.core.database import get_db
from app.core.config import settings
from app.models.base import Base, Organization, School
from app.models.auth import User, Role, Permission
from app.core.security import password_manager

# Test database configuration
TEST_DATABASE_URL = "**************************************************************/school_erp_test"

# Create test engine with connection pooling
test_engine = create_engine(
    TEST_DATABASE_URL,
    poolclass=StaticPool,
    connect_args={"check_same_thread": False} if "sqlite" in TEST_DATABASE_URL else {},
    echo=False  # Set to True for SQL debugging
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session", autouse=True)
def setup_test_database():
    """Setup test database schema before all tests."""
    # Create all tables
    Base.metadata.create_all(bind=test_engine)
    yield
    # Drop all tables after tests
    Base.metadata.drop_all(bind=test_engine)


@pytest.fixture
def db_session() -> Generator[Session, None, None]:
    """
    Create a fresh database session for each test.
    Provides transaction isolation - each test gets a clean database state.
    """
    connection = test_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    
    # Enable nested transactions for test isolation
    nested = connection.begin_nested()
    
    @event.listens_for(session, "after_transaction_end")
    def end_savepoint(session, transaction):
        nonlocal nested
        if not nested.is_active:
            nested = connection.begin_nested()
    
    yield session
    
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture
def override_get_db(db_session: Session):
    """Override the get_db dependency to use test database."""
    def _override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()


@pytest.fixture
def client(override_get_db) -> TestClient:
    """Create a test client for API testing."""
    return TestClient(app)


@pytest.fixture
async def async_client(override_get_db) -> AsyncClient:
    """Create an async test client for async API testing."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def mock_redis():
    """Mock Redis for testing without external dependencies."""
    mock = Mock()
    mock.get.return_value = None
    mock.set.return_value = True
    mock.delete.return_value = True
    mock.exists.return_value = False
    mock.expire.return_value = True
    return mock


# Test data fixtures
@pytest.fixture
def test_organization_data() -> Dict[str, Any]:
    """Test organization data."""
    return {
        "name": "Test Organization",
        "email": "<EMAIL>",
        "phone": "+91-9876543210",
        "country": "India",
        "subdomain": "testorg"
    }


@pytest.fixture
def test_school_data() -> Dict[str, Any]:
    """Test school data."""
    return {
        "name": "Test School",
        "email": "<EMAIL>",
        "phone": "+91-9876543210",
        "country": "India",
        "subdomain": "testschool",
        "school_code": "TEST001",
        "board_type": "CBSE"
    }


@pytest.fixture
def test_user_data() -> Dict[str, Any]:
    """Test user data."""
    return {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "Admin",
        "phone": "+91-9876543210",
        "user_type": "admin"
    }


@pytest.fixture
def test_organization(db_session: Session, test_organization_data: Dict[str, Any]) -> Organization:
    """Create a test organization."""
    org = Organization(**test_organization_data)
    db_session.add(org)
    db_session.commit()
    db_session.refresh(org)
    return org


@pytest.fixture
def test_school(db_session: Session, test_organization: Organization, test_school_data: Dict[str, Any]) -> School:
    """Create a test school."""
    school_data = test_school_data.copy()
    school_data["organization_id"] = test_organization.id
    
    # Generate school_id for multi-tenant setup
    school_id = uuid.uuid4()
    school_data["id"] = school_id
    school_data["school_id"] = school_id
    
    school = School(**school_data)
    db_session.add(school)
    db_session.commit()
    db_session.refresh(school)
    return school


@pytest.fixture
def test_user(db_session: Session, test_school: School, test_user_data: Dict[str, Any]) -> User:
    """Create a test user."""
    user_data = test_user_data.copy()
    password = user_data.pop("password")
    user_data["school_id"] = test_school.id
    
    user = User(**user_data)
    user.set_password(password)
    user.is_verified = True
    user.is_active = True
    
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def test_admin_role(db_session: Session, test_school: School) -> Role:
    """Create a test admin role."""
    role = Role(
        school_id=test_school.id,
        name="admin",
        display_name="Administrator",
        description="Full system access"
    )
    db_session.add(role)
    db_session.commit()
    db_session.refresh(role)
    return role


@pytest.fixture
def test_permission(db_session: Session, test_school: School) -> Permission:
    """Create a test permission."""
    permission = Permission(
        school_id=test_school.id,
        code="admin.all",
        name="Admin All Permission",
        category="admin",
        resource="all",
        action="all"
    )
    db_session.add(permission)
    db_session.commit()
    db_session.refresh(permission)
    return permission


# Authentication fixtures
@pytest.fixture
def auth_headers(test_user: User, test_school: School) -> Dict[str, str]:
    """Create authentication headers for API testing."""
    from app.core.security import security_manager
    
    # Create access token
    access_token = security_manager.create_access_token(
        subject=test_user.id,
        school_id=test_school.id,
        permissions=[]
    )
    
    return {"Authorization": f"Bearer {access_token}"}


# Utility fixtures
@pytest.fixture
def clean_database(db_session: Session):
    """Clean database before test (useful for integration tests)."""
    # Delete all data in reverse order to handle foreign keys
    for table in reversed(Base.metadata.sorted_tables):
        db_session.execute(table.delete())
    db_session.commit()
    yield
    # Cleanup is handled by db_session fixture


# Performance testing fixtures
@pytest.fixture
def performance_timer():
    """Timer fixture for performance testing."""
    import time
    start_time = time.time()
    yield
    end_time = time.time()
    execution_time = end_time - start_time
    print(f"\nTest execution time: {execution_time:.4f} seconds")


# Mock external services
@pytest.fixture
def mock_email_service():
    """Mock email service for testing."""
    mock = Mock()
    mock.send_email.return_value = True
    mock.send_verification_email.return_value = True
    return mock


@pytest.fixture
def mock_sms_service():
    """Mock SMS service for testing."""
    mock = Mock()
    mock.send_sms.return_value = True
    return mock
