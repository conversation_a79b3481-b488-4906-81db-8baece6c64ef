"""
Comprehensive tests for academic structure system
Testing academic year, class, section management with full coverage
"""

import pytest
import uuid
from datetime import date, datetime, timedelta
from unittest.mock import Mock, patch
from sqlalchemy.exc import IntegrityError

from app.models.academic import AcademicYear, Class, Section, ClassSectionMapping, Subject, ClassSubject
from app.services.academic_service.academic_year import academic_year_service, AcademicYearError
from app.services.academic_service.subject import subject_service, SubjectError
from app.tests.utils import TestDataManager, APITestHelper, AssertionHelper, assert_audit_log_created
from app.tests.factories import (
    AcademicYearFactory, ActiveAcademicYearFactory, ClassFactory,
    SectionFactory, ClassSectionMappingFactory, SchoolFactory, UserFactory,
    SubjectFactory, CoreSubjectFactory, ElectiveSubjectFactory, ClassSubjectFactory
)


class TestAcademicYearModel:
    """Test AcademicYear model functionality."""
    
    def test_create_academic_year(self, db_session):
        """Test creating an academic year."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            
            academic_year = AcademicYear(
                school_id=school.id,
                year_label="2024-25",
                start_date=date(2024, 4, 1),
                end_date=date(2025, 3, 31),
                is_active=False,
                status="draft"
            )
            
            db_session.add(academic_year)
            db_session.commit()
            db_session.refresh(academic_year)
            
            assert academic_year.id is not None
            assert academic_year.school_id == school.id
            assert academic_year.year_label == "2024-25"
            assert academic_year.display_name == "Academic Year 2024-25"  # Auto-generated
            assert academic_year.start_date == date(2024, 4, 1)
            assert academic_year.end_date == date(2025, 3, 31)
            assert academic_year.is_active is False
            assert academic_year.status == "draft"
            
        finally:
            data_manager.cleanup()
    
    def test_academic_year_validation(self, db_session):
        """Test academic year validation rules."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            
            # Test invalid year label format
            with pytest.raises(ValueError, match="Year label must be in format YYYY-YY"):
                academic_year = AcademicYear(
                    school_id=school.id,
                    year_label="2024",  # Invalid format
                    start_date=date(2024, 4, 1),
                    end_date=date(2025, 3, 31)
                )
                db_session.add(academic_year)
                db_session.commit()
            
            # Test invalid status
            with pytest.raises(ValueError, match="Status must be one of"):
                academic_year = AcademicYear(
                    school_id=school.id,
                    year_label="2024-25",
                    start_date=date(2024, 4, 1),
                    end_date=date(2025, 3, 31),
                    status="invalid_status"
                )
                db_session.add(academic_year)
                db_session.commit()
                
        finally:
            data_manager.cleanup()
    
    def test_academic_year_computed_fields(self, db_session):
        """Test academic year computed fields."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            
            # Create current academic year
            academic_year = AcademicYear(
                school_id=school.id,
                year_label="2024-25",
                start_date=date(2024, 4, 1),
                end_date=date(2025, 3, 31),
                is_active=True,
                status="active"
            )
            
            db_session.add(academic_year)
            db_session.commit()
            db_session.refresh(academic_year)
            
            # Test computed fields
            assert academic_year.get_duration_days() == 365  # April 1 to March 31
            assert isinstance(academic_year.get_progress_percentage(), float)
            assert isinstance(academic_year.get_remaining_days(), int)
            
            # Test activation checks
            can_activate, reason = academic_year.can_be_activated()
            assert can_activate is False  # Already active
            assert "already active" in reason
            
            # Test completion checks
            can_complete, reason = academic_year.can_be_completed()
            assert can_complete is True  # Active year can be completed
            
        finally:
            data_manager.cleanup()
    
    def test_academic_year_unique_constraints(self, db_session):
        """Test academic year unique constraints."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            
            # Create first academic year
            academic_year1 = AcademicYear(
                school_id=school.id,
                year_label="2024-25",
                start_date=date(2024, 4, 1),
                end_date=date(2025, 3, 31),
                is_active=True,
                status="active"
            )
            
            db_session.add(academic_year1)
            db_session.commit()
            
            # Try to create another active academic year (should fail)
            with pytest.raises(IntegrityError):
                academic_year2 = AcademicYear(
                    school_id=school.id,
                    year_label="2025-26",
                    start_date=date(2025, 4, 1),
                    end_date=date(2026, 3, 31),
                    is_active=True,  # This should violate unique constraint
                    status="active"
                )
                
                db_session.add(academic_year2)
                db_session.commit()
                
        finally:
            data_manager.cleanup()


class TestAcademicYearService:
    """Test AcademicYearService functionality."""
    
    def test_create_academic_year_success(self, db_session):
        """Test successful academic year creation."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)
            
            result = academic_year_service.create_academic_year(
                school_id=school.id,
                year_label="2024-25",
                start_date=date(2024, 4, 1),
                end_date=date(2025, 3, 31),
                user_id=user.id,
                display_name="Test Academic Year",
                description="Test description"
            )
            
            assert result["success"] is True
            assert "created successfully" in result["message"]
            assert result["academic_year"]["year_label"] == "2024-25"
            assert result["academic_year"]["display_name"] == "Test Academic Year"
            assert result["academic_year"]["is_active"] is False  # Not auto-activated
            
            # Verify audit log
            assert_audit_log_created(db_session, "create_academic_year", "academic_year", user.id)
            
        finally:
            data_manager.cleanup()
    
    def test_create_academic_year_with_auto_activate(self, db_session):
        """Test academic year creation with auto-activation."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)
            
            result = academic_year_service.create_academic_year(
                school_id=school.id,
                year_label="2024-25",
                start_date=date(2024, 4, 1),
                end_date=date(2025, 3, 31),
                user_id=user.id,
                auto_activate=True  # Should activate since no active year exists
            )
            
            assert result["success"] is True
            assert result["academic_year"]["is_active"] is True
            assert result["academic_year"]["status"] == "active"
            
        finally:
            data_manager.cleanup()
    
    def test_create_academic_year_validation_errors(self, db_session):
        """Test academic year creation validation errors."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)
            
            # Test invalid date range
            with pytest.raises(AcademicYearError, match="Start date must be before end date"):
                academic_year_service.create_academic_year(
                    school_id=school.id,
                    year_label="2024-25",
                    start_date=date(2025, 3, 31),  # After end date
                    end_date=date(2024, 4, 1),
                    user_id=user.id
                )
            
            # Create first academic year
            academic_year_service.create_academic_year(
                school_id=school.id,
                year_label="2024-25",
                start_date=date(2024, 4, 1),
                end_date=date(2025, 3, 31),
                user_id=user.id
            )
            
            # Test duplicate year label
            with pytest.raises(AcademicYearError, match="already exists"):
                academic_year_service.create_academic_year(
                    school_id=school.id,
                    year_label="2024-25",  # Duplicate
                    start_date=date(2024, 4, 1),
                    end_date=date(2025, 3, 31),
                    user_id=user.id
                )
            
            # Test overlapping dates
            with pytest.raises(AcademicYearError, match="overlaps with existing year"):
                academic_year_service.create_academic_year(
                    school_id=school.id,
                    year_label="2024-26",
                    start_date=date(2024, 6, 1),  # Overlaps with existing year
                    end_date=date(2025, 5, 31),
                    user_id=user.id
                )
                
        finally:
            data_manager.cleanup()
    
    def test_get_academic_year(self, db_session):
        """Test retrieving academic year by ID."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            academic_year = AcademicYearFactory(school=school)
            
            result = academic_year_service.get_academic_year(
                school_id=school.id,
                academic_year_id=academic_year.id
            )
            
            assert result is not None
            assert result["id"] == str(academic_year.id)
            assert result["year_label"] == academic_year.year_label
            assert "is_current_year" in result  # Computed field
            assert "progress_percentage" in result  # Computed field
            
        finally:
            data_manager.cleanup()
    
    def test_get_active_academic_year(self, db_session):
        """Test retrieving active academic year."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            
            # No active year initially
            result = academic_year_service.get_active_academic_year(school.id)
            assert result is None
            
            # Create active academic year
            active_year = ActiveAcademicYearFactory(school=school)
            
            result = academic_year_service.get_active_academic_year(school.id)
            assert result is not None
            assert result["id"] == str(active_year.id)
            assert result["is_active"] is True
            
        finally:
            data_manager.cleanup()
    
    def test_list_academic_years(self, db_session):
        """Test listing academic years with pagination."""
        data_manager = TestDataManager(db_session)
        
        try:
            school = data_manager.create_school()
            
            # Create multiple academic years
            years = []
            for i in range(5):
                year = AcademicYearFactory(
                    school=school,
                    year_label=f"202{i}-2{i+1}",
                    start_date=date(2020 + i, 4, 1),
                    end_date=date(2021 + i, 3, 31)
                )
                years.append(year)
            
            # Test listing with pagination
            result = academic_year_service.list_academic_years(
                school_id=school.id,
                limit=3,
                offset=0,
                order_by="start_date",
                order_direction="desc"
            )
            
            assert result["success"] is True
            assert len(result["academic_years"]) == 3
            assert result["total_count"] == 5
            assert result["has_more"] is True
            
            # Test second page
            result = academic_year_service.list_academic_years(
                school_id=school.id,
                limit=3,
                offset=3
            )
            
            assert len(result["academic_years"]) == 2
            assert result["has_more"] is False
            
        finally:
            data_manager.cleanup()

    def test_activate_academic_year(self, db_session):
        """Test activating an academic year."""
        data_manager = TestDataManager(db_session)

        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)

            # Create current active year
            current_active = ActiveAcademicYearFactory(school=school, year_label="2023-24")

            # Create year to activate
            new_year = AcademicYearFactory(
                school=school,
                year_label="2024-25",
                status="draft"
            )

            result = academic_year_service.activate_academic_year(
                school_id=school.id,
                academic_year_id=new_year.id,
                user_id=user.id
            )

            assert result["success"] is True
            assert "activated successfully" in result["message"]
            assert result["academic_year"]["is_active"] is True
            assert result["academic_year"]["status"] == "active"
            assert result["previous_active"] == "2023-24"

            # Verify audit logs
            assert_audit_log_created(db_session, "activate_academic_year", "academic_year", user.id)
            assert_audit_log_created(db_session, "deactivate_academic_year", "academic_year", user.id)

        finally:
            data_manager.cleanup()

    def test_complete_academic_year(self, db_session):
        """Test completing an academic year."""
        data_manager = TestDataManager(db_session)

        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)

            # Create active academic year
            active_year = ActiveAcademicYearFactory(school=school)

            result = academic_year_service.complete_academic_year(
                school_id=school.id,
                academic_year_id=active_year.id,
                user_id=user.id
            )

            assert result["success"] is True
            assert "completed successfully" in result["message"]
            assert result["academic_year"]["is_active"] is False
            assert result["academic_year"]["status"] == "completed"

            # Verify audit log
            assert_audit_log_created(db_session, "complete_academic_year", "academic_year", user.id)

        finally:
            data_manager.cleanup()

    def test_delete_academic_year(self, db_session):
        """Test soft deleting an academic year."""
        data_manager = TestDataManager(db_session)

        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)

            # Create inactive academic year
            academic_year = AcademicYearFactory(school=school, is_active=False)

            result = academic_year_service.delete_academic_year(
                school_id=school.id,
                academic_year_id=academic_year.id,
                user_id=user.id,
                reason="Test deletion"
            )

            assert result["success"] is True
            assert "deleted successfully" in result["message"]

            # Verify soft deletion
            db_session.refresh(academic_year)
            assert academic_year.is_deleted is True
            assert academic_year.deletion_reason == "Test deletion"

            # Verify audit log
            assert_audit_log_created(db_session, "delete_academic_year", "academic_year", user.id)

        finally:
            data_manager.cleanup()

    def test_generate_academic_year_label(self, db_session):
        """Test academic year label generation."""
        label = academic_year_service.generate_academic_year_label(2024)
        assert label == "2024-25"

        label = academic_year_service.generate_academic_year_label(2023)
        assert label == "2023-24"

    def test_get_current_indian_academic_year(self, db_session):
        """Test getting current Indian academic year."""
        with patch('app.services.academic_service.academic_year.date') as mock_date:
            # Test April onwards (new academic year)
            mock_date.today.return_value = date(2024, 6, 15)  # June 15, 2024

            year_label, start_date, end_date = academic_year_service.get_current_indian_academic_year()

            assert year_label == "2024-25"
            assert start_date == date(2024, 4, 1)
            assert end_date == date(2025, 3, 31)

            # Test January-March (previous academic year)
            mock_date.today.return_value = date(2024, 2, 15)  # February 15, 2024

            year_label, start_date, end_date = academic_year_service.get_current_indian_academic_year()

            assert year_label == "2023-24"
            assert start_date == date(2023, 4, 1)
            assert end_date == date(2024, 3, 31)


class TestAcademicYearAPI:
    """Test Academic Year API endpoints."""

    def test_create_academic_year_api(self, client, db_session):
        """Test creating academic year via API."""
        data_manager = TestDataManager(db_session)
        api_helper = APITestHelper(client)

        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school, user_type="admin")

            # Add admin permissions
            user_permissions = [
                "academic.academic_year.create",
                "academic.academic_year.read"
            ]

            headers = api_helper.create_auth_headers(user, school)

            # Mock user permissions
            with patch.object(user, 'get_permissions', return_value=user_permissions):
                response = client.post(
                    "/api/v1/academic/years",
                    json={
                        "year_label": "2024-25",
                        "start_date": "2024-04-01",
                        "end_date": "2025-03-31",
                        "display_name": "Test Academic Year",
                        "description": "Test description",
                        "auto_activate": False
                    },
                    headers=headers
                )

            assert response.status_code == 201
            data = response.json()
            assert data["success"] is True
            assert data["academic_year"]["year_label"] == "2024-25"
            assert data["academic_year"]["display_name"] == "Test Academic Year"

        finally:
            data_manager.cleanup()

    def test_api_permission_checks(self, client, db_session):
        """Test API permission checks."""
        data_manager = TestDataManager(db_session)
        api_helper = APITestHelper(client)

        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school, user_type="teacher")  # No admin permissions

            headers = api_helper.create_auth_headers(user, school)

            # Mock user with no permissions
            with patch.object(user, 'get_permissions', return_value=[]):
                response = client.post(
                    "/api/v1/academic/years",
                    json={
                        "year_label": "2024-25",
                        "start_date": "2024-04-01",
                        "end_date": "2025-03-31"
                    },
                    headers=headers
                )

            assert response.status_code == 403
            assert "Insufficient permissions" in response.json()["detail"]

        finally:
            data_manager.cleanup()


class TestSubjectModel:
    """Test Subject model functionality."""

    def test_create_subject(self, db_session):
        """Test creating a subject."""
        data_manager = TestDataManager(db_session)

        try:
            school = data_manager.create_school()

            subject = Subject(
                school_id=school.id,
                subject_code="MATH101",
                name="Mathematics",
                description="Basic Mathematics",
                subject_type="core",
                academic_level="primary",
                default_credits=4,
                default_hours_per_week=5,
                is_active=True
            )

            db_session.add(subject)
            db_session.commit()
            db_session.refresh(subject)

            assert subject.id is not None
            assert subject.school_id == school.id
            assert subject.subject_code == "MATH101"
            assert subject.name == "Mathematics"
            assert subject.subject_type == "core"
            assert subject.academic_level == "primary"
            assert subject.default_credits == 4
            assert subject.default_hours_per_week == 5
            assert subject.is_active is True

        finally:
            data_manager.cleanup()

    def test_subject_validation(self, db_session):
        """Test subject validation rules."""
        data_manager = TestDataManager(db_session)

        try:
            school = data_manager.create_school()

            # Test empty subject code
            with pytest.raises(ValueError, match="Subject code cannot be empty"):
                subject = Subject(
                    school_id=school.id,
                    subject_code="",  # Empty code
                    name="Mathematics"
                )
                db_session.add(subject)
                db_session.commit()

            # Test empty subject name
            with pytest.raises(ValueError, match="Subject name cannot be empty"):
                subject = Subject(
                    school_id=school.id,
                    subject_code="MATH101",
                    name=""  # Empty name
                )
                db_session.add(subject)
                db_session.commit()

        finally:
            data_manager.cleanup()

    def test_subject_computed_properties(self, db_session):
        """Test subject computed properties."""
        data_manager = TestDataManager(db_session)

        try:
            school = data_manager.create_school()

            # Test core subject
            core_subject = Subject(
                school_id=school.id,
                subject_code="MATH101",
                name="Mathematics",
                subject_type="core"
            )

            db_session.add(core_subject)
            db_session.commit()

            assert core_subject.is_core_subject is True
            assert core_subject.is_elective_subject is False
            assert core_subject.is_extra_curricular is False

            # Test elective subject
            elective_subject = Subject(
                school_id=school.id,
                subject_code="ART101",
                name="Art",
                subject_type="elective"
            )

            db_session.add(elective_subject)
            db_session.commit()

            assert elective_subject.is_core_subject is False
            assert elective_subject.is_elective_subject is True
            assert elective_subject.is_extra_curricular is False

        finally:
            data_manager.cleanup()


class TestSubjectService:
    """Test SubjectService functionality."""

    def test_create_subject_success(self, db_session):
        """Test successful subject creation."""
        data_manager = TestDataManager(db_session)

        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)

            result = subject_service.create_subject(
                school_id=school.id,
                subject_code="MATH101",
                name="Mathematics",
                user_id=user.id,
                description="Basic Mathematics",
                subject_type="core",
                academic_level="primary",
                default_credits=4,
                default_hours_per_week=5
            )

            assert result["success"] is True
            assert "created successfully" in result["message"]
            assert result["subject"]["subject_code"] == "MATH101"
            assert result["subject"]["name"] == "Mathematics"
            assert result["subject"]["subject_type"] == "core"
            assert result["subject"]["is_core_subject"] is True

        finally:
            data_manager.cleanup()

    def test_create_subject_duplicate_code(self, db_session):
        """Test subject creation with duplicate code."""
        data_manager = TestDataManager(db_session)

        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)

            # Create first subject
            subject_service.create_subject(
                school_id=school.id,
                subject_code="MATH101",
                name="Mathematics",
                user_id=user.id
            )

            # Try to create subject with same code
            result = subject_service.create_subject(
                school_id=school.id,
                subject_code="MATH101",  # Duplicate code
                name="Advanced Mathematics",
                user_id=user.id
            )

            assert result["success"] is False
            assert result["error_type"] == "duplicate_code"
            assert "already exists" in result["message"]

        finally:
            data_manager.cleanup()

    def test_list_subjects(self, db_session):
        """Test listing subjects with filtering."""
        data_manager = TestDataManager(db_session)

        try:
            school = data_manager.create_school()

            # Create multiple subjects
            subjects = []
            for i in range(5):
                subject = SubjectFactory(
                    school_id=school.id,
                    subject_code=f"SUB{i:03d}",
                    name=f"Subject {i}",
                    subject_type="core" if i < 3 else "elective"
                )
                subjects.append(subject)

            # Test listing all subjects
            result = subject_service.list_subjects(
                school_id=school.id,
                limit=10
            )

            assert result["success"] is True
            assert len(result["subjects"]) == 5
            assert result["pagination"]["total"] == 5

            # Test filtering by subject type
            result = subject_service.list_subjects(
                school_id=school.id,
                subject_type="core"
            )

            assert len(result["subjects"]) == 3

        finally:
            data_manager.cleanup()

    def test_update_subject(self, db_session):
        """Test updating subject."""
        data_manager = TestDataManager(db_session)

        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)
            subject = SubjectFactory(school_id=school.id)

            result = subject_service.update_subject(
                school_id=school.id,
                subject_id=subject.id,
                user_id=user.id,
                name="Updated Subject Name",
                description="Updated description"
            )

            assert result["success"] is True
            assert result["subject"]["name"] == "Updated Subject Name"
            assert result["subject"]["description"] == "Updated description"

        finally:
            data_manager.cleanup()

    def test_delete_subject(self, db_session):
        """Test deleting subject."""
        data_manager = TestDataManager(db_session)

        try:
            school = data_manager.create_school()
            user = data_manager.create_user(school=school)
            subject = SubjectFactory(school_id=school.id)

            result = subject_service.delete_subject(
                school_id=school.id,
                subject_id=subject.id,
                user_id=user.id,
                reason="Test deletion"
            )

            assert result["success"] is True
            assert "deleted successfully" in result["message"]

            # Verify soft deletion
            db_session.refresh(subject)
            assert subject.is_deleted is True
            assert subject.deletion_reason == "Test deletion"

        finally:
            data_manager.cleanup()
