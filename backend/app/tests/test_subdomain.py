"""
Unit and integration tests for subdomain management system
Comprehensive testing of subdomain validation, availability checking, and routing
"""

import pytest
import uuid
from unittest.mock import Mock, patch

from app.services.subdomain_service.subdomain import subdomain_service
from app.models.base import School
from app.tests.utils import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, APITestHelper, Asser<PERSON>Helper
from app.tests.factories import SchoolFactory, OrganizationFactory


class TestSubdomainValidation:
    """Test subdomain validation logic."""
    
    def test_validate_subdomain_valid(self):
        """Test validation with valid subdomains."""
        valid_subdomains = [
            "testschool",
            "my-school",
            "school123",
            "a",  # Single character
            "a" * 63,  # Maximum length
            "test-school-123"
        ]
        
        for subdomain in valid_subdomains:
            result = subdomain_service.validate_subdomain(subdomain)
            assert result["is_valid"] is True, f"'{subdomain}' should be valid"
            assert result["error"] is None
    
    def test_validate_subdomain_invalid_format(self):
        """Test validation with invalid subdomain formats."""
        invalid_subdomains = [
            "",  # Empty
            "Test",  # Uppercase
            "test_school",  # Underscore
            "test school",  # Space
            "test.school",  # Dot
            "-testschool",  # Starts with hyphen
            "testschool-",  # Ends with hyphen
            "test--school",  # Double hyphen
            "a" * 64,  # Too long
            "123",  # Only numbers
            "test@school",  # Special characters
            "тест",  # Non-ASCII
        ]
        
        for subdomain in invalid_subdomains:
            result = subdomain_service.validate_subdomain(subdomain)
            assert result["is_valid"] is False, f"'{subdomain}' should be invalid"
            assert result["error"] is not None
    
    def test_validate_subdomain_reserved(self):
        """Test validation with reserved subdomains."""
        reserved_subdomains = [
            "www",
            "api",
            "admin",
            "mail",
            "ftp",
            "blog",
            "shop",
            "support"
        ]
        
        for subdomain in reserved_subdomains:
            result = subdomain_service.validate_subdomain(subdomain)
            assert result["is_valid"] is False, f"'{subdomain}' should be reserved"
            assert "reserved" in result["error"].lower()


class TestSubdomainAvailability:
    """Test subdomain availability checking."""
    
    def test_check_availability_available(self, db_session):
        """Test availability check for available subdomain."""
        data_manager = TestDataManager(db_session)
        
        try:
            result = subdomain_service.check_availability("newschool")
            
            assert result["is_available"] is True
            assert result["subdomain"] == "newschool"
            assert result["suggestions"] is None
            
        finally:
            data_manager.cleanup()
    
    def test_check_availability_taken(self, db_session):
        """Test availability check for taken subdomain."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school(subdomain="takenschool")
        
        try:
            result = subdomain_service.check_availability("takenschool")
            
            assert result["is_available"] is False
            assert result["subdomain"] == "takenschool"
            assert isinstance(result["suggestions"], list)
            assert len(result["suggestions"]) > 0
            
        finally:
            data_manager.cleanup()
    
    def test_check_availability_invalid_format(self):
        """Test availability check for invalid subdomain format."""
        result = subdomain_service.check_availability("Invalid_Subdomain")
        
        assert result["is_available"] is False
        assert "error" in result
        assert result["suggestions"] is None
    
    def test_check_availability_reserved(self):
        """Test availability check for reserved subdomain."""
        result = subdomain_service.check_availability("admin")
        
        assert result["is_available"] is False
        assert "reserved" in result.get("error", "").lower()
        assert isinstance(result["suggestions"], list)


class TestSubdomainSuggestions:
    """Test subdomain suggestion generation."""
    
    def test_generate_suggestions_basic(self, db_session):
        """Test basic suggestion generation."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school(subdomain="testschool")
        
        try:
            suggestions = subdomain_service.generate_suggestions("testschool")
            
            assert isinstance(suggestions, list)
            assert len(suggestions) >= 3
            
            # Check that suggestions are different from original
            for suggestion in suggestions:
                assert suggestion != "testschool"
                assert subdomain_service.validate_subdomain(suggestion)["is_valid"]
            
        finally:
            data_manager.cleanup()
    
    def test_generate_suggestions_with_numbers(self, db_session):
        """Test suggestion generation with numeric suffixes."""
        data_manager = TestDataManager(db_session)
        
        # Create schools with numeric suffixes
        for i in range(1, 4):
            data_manager.create_school(subdomain=f"school{i}")
        
        try:
            suggestions = subdomain_service.generate_suggestions("school")
            
            assert isinstance(suggestions, list)
            assert len(suggestions) >= 3
            
            # Should suggest available numeric suffixes
            available_suggestions = [s for s in suggestions if s.startswith("school")]
            assert len(available_suggestions) > 0
            
        finally:
            data_manager.cleanup()
    
    def test_generate_suggestions_with_variations(self):
        """Test suggestion generation with word variations."""
        suggestions = subdomain_service.generate_suggestions("myschool")
        
        assert isinstance(suggestions, list)
        assert len(suggestions) >= 3
        
        # Should include variations
        suggestion_text = " ".join(suggestions)
        assert any(word in suggestion_text for word in ["academy", "institute", "education"])


class TestSubdomainResolution:
    """Test subdomain to school resolution."""
    
    def test_resolve_subdomain_success(self, db_session):
        """Test successful subdomain resolution."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school(subdomain="testschool")
        
        try:
            result = subdomain_service.resolve_subdomain_to_school("testschool")
            
            assert result is not None
            assert result["school_id"] == str(school.id)
            assert result["school_name"] == school.name
            assert result["subdomain"] == "testschool"
            assert result["status"] == "active"
            
        finally:
            data_manager.cleanup()
    
    def test_resolve_subdomain_not_found(self, db_session):
        """Test subdomain resolution for non-existent subdomain."""
        result = subdomain_service.resolve_subdomain_to_school("nonexistent")
        assert result is None
    
    def test_resolve_subdomain_inactive_school(self, db_session):
        """Test subdomain resolution for inactive school."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school(subdomain="inactiveschool", is_active=False)
        
        try:
            result = subdomain_service.resolve_subdomain_to_school("inactiveschool")
            
            assert result is not None
            assert result["status"] == "inactive"
            
        finally:
            data_manager.cleanup()


class TestSubdomainReservation:
    """Test subdomain reservation functionality."""
    
    def test_reserve_subdomain_success(self, db_session):
        """Test successful subdomain reservation."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school(subdomain="oldsubdomain")
        user = data_manager.create_user(school=school)
        
        try:
            result = subdomain_service.reserve_subdomain(
                subdomain="newsubdomain",
                school_id=school.id,
                user_id=user.id
            )
            
            assert result["success"] is True
            assert result["subdomain"] == "newsubdomain"
            
            # Verify school subdomain was updated
            db_session.refresh(school)
            assert school.subdomain == "newsubdomain"
            
        finally:
            data_manager.cleanup()
    
    def test_reserve_subdomain_invalid(self, db_session):
        """Test subdomain reservation with invalid subdomain."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school()
        user = data_manager.create_user(school=school)
        
        try:
            with pytest.raises(ValueError, match="Invalid subdomain format"):
                subdomain_service.reserve_subdomain(
                    subdomain="Invalid_Subdomain",
                    school_id=school.id,
                    user_id=user.id
                )
        finally:
            data_manager.cleanup()
    
    def test_reserve_subdomain_taken(self, db_session):
        """Test subdomain reservation with already taken subdomain."""
        data_manager = TestDataManager(db_session)
        school1 = data_manager.create_school(subdomain="takenschool")
        school2 = data_manager.create_school(subdomain="otherschool")
        user = data_manager.create_user(school=school2)
        
        try:
            with pytest.raises(ValueError, match="Subdomain is already taken"):
                subdomain_service.reserve_subdomain(
                    subdomain="takenschool",
                    school_id=school2.id,
                    user_id=user.id
                )
        finally:
            data_manager.cleanup()


@pytest.mark.integration
class TestSubdomainAPI:
    """Integration tests for subdomain management API endpoints."""
    
    def test_validate_subdomain_api_success(self, client):
        """Test subdomain validation API with valid subdomain."""
        api_helper = APITestHelper(client)
        
        response = api_helper.post_json(
            "/api/v1/subdomain/validate",
            {"subdomain": "newschool"}
        )
        
        AssertionHelper.assert_response_success(response)
        data = response["data"]
        
        assert data["is_valid"] is True
        assert data["is_available"] is True
        assert data["subdomain"] == "newschool"
    
    def test_validate_subdomain_api_invalid(self, client):
        """Test subdomain validation API with invalid subdomain."""
        api_helper = APITestHelper(client)
        
        response = api_helper.post_json(
            "/api/v1/subdomain/validate",
            {"subdomain": "Invalid_Subdomain"}
        )
        
        AssertionHelper.assert_response_success(response)
        data = response["data"]
        
        assert data["is_valid"] is False
        assert data["is_available"] is False
        assert "error" in data
    
    def test_validate_subdomain_api_taken(self, client, db_session):
        """Test subdomain validation API with taken subdomain."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school(subdomain="takenschool")
        
        api_helper = APITestHelper(client)
        
        try:
            response = api_helper.post_json(
                "/api/v1/subdomain/validate",
                {"subdomain": "takenschool"}
            )
            
            AssertionHelper.assert_response_success(response)
            data = response["data"]
            
            assert data["is_valid"] is True
            assert data["is_available"] is False
            assert isinstance(data["suggestions"], list)
            
        finally:
            data_manager.cleanup()
    
    def test_get_reserved_subdomains_api(self, client):
        """Test getting reserved subdomains list."""
        api_helper = APITestHelper(client)
        
        response = api_helper.get_json("/api/v1/subdomain/reserved")
        
        AssertionHelper.assert_response_success(response)
        data = response["data"]
        
        assert "reserved_subdomains" in data
        assert isinstance(data["reserved_subdomains"], list)
        assert len(data["reserved_subdomains"]) > 0
        
        # Check that common reserved subdomains are included
        reserved_list = data["reserved_subdomains"]
        assert "www" in reserved_list
        assert "api" in reserved_list
        assert "admin" in reserved_list
    
    def test_get_subdomain_suggestions_api(self, client):
        """Test getting subdomain suggestions."""
        api_helper = APITestHelper(client)
        
        response = api_helper.get_json("/api/v1/subdomain/suggestions?base=myschool")
        
        AssertionHelper.assert_response_success(response)
        data = response["data"]
        
        assert "suggestions" in data
        assert isinstance(data["suggestions"], list)
        assert len(data["suggestions"]) >= 3
        
        # All suggestions should be valid
        for suggestion in data["suggestions"]:
            validation = subdomain_service.validate_subdomain(suggestion)
            assert validation["is_valid"] is True
    
    def test_get_subdomain_info_api(self, client, db_session):
        """Test getting current subdomain information."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school(subdomain="testschool")
        
        api_helper = APITestHelper(client)
        
        try:
            # Test with subdomain in host header
            headers = {"Host": "testschool.myschoolerp.local:8000"}
            response = api_helper.get_json("/api/v1/subdomain/info", headers=headers)
            
            AssertionHelper.assert_response_success(response)
            data = response["data"]
            
            assert data["subdomain"] == "testschool"
            assert data["school_name"] == school.name
            assert data["status"] == "active"
            
        finally:
            data_manager.cleanup()
    
    def test_reserve_subdomain_api_success(self, client, db_session):
        """Test subdomain reservation API."""
        data_manager = TestDataManager(db_session)
        school = data_manager.create_school(subdomain="oldsubdomain")
        user = data_manager.create_user(school=school)
        
        api_helper = APITestHelper(client)
        auth_headers = api_helper.create_auth_headers(user, school)
        
        try:
            response = api_helper.post_json(
                "/api/v1/subdomain/reserve",
                {
                    "subdomain": "newsubdomain",
                    "school_id": str(school.id)
                },
                headers=auth_headers
            )
            
            AssertionHelper.assert_response_success(response)
            data = response["data"]
            
            assert data["success"] is True
            assert data["subdomain"] == "newsubdomain"
            
        finally:
            data_manager.cleanup()
    
    def test_reserve_subdomain_api_unauthorized(self, client):
        """Test subdomain reservation API without authentication."""
        api_helper = APITestHelper(client)
        
        response = api_helper.post_json(
            "/api/v1/subdomain/reserve",
            {
                "subdomain": "newsubdomain",
                "school_id": str(uuid.uuid4())
            }
        )
        
        AssertionHelper.assert_response_error(response, 401)


@pytest.mark.performance
class TestSubdomainPerformance:
    """Performance tests for subdomain operations."""

    def test_subdomain_validation_performance(self, performance_timer):
        """Test subdomain validation performance."""
        # Test validation of 1000 subdomains
        for i in range(1000):
            subdomain_service.validate_subdomain(f"testschool{i}")

    def test_availability_check_performance(self, db_session, performance_timer):
        """Test availability check performance with large dataset."""
        data_manager = TestDataManager(db_session)

        # Create 100 schools
        for i in range(100):
            data_manager.create_school(subdomain=f"school{i}")

        try:
            # Test availability checks
            for i in range(100, 200):
                subdomain_service.check_availability(f"school{i}")
        finally:
            data_manager.cleanup()
