"""
Pydantic schemas for RBAC operations
Request and response models for role and permission management
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
import uuid


# ==================== BASE SCHEMAS ====================

class RoleBase(BaseModel):
    """Base role schema"""
    name: str = Field(..., min_length=2, max_length=100, description="Role name")
    display_name: str = Field(..., min_length=2, max_length=200, description="Human-readable role name")
    description: Optional[str] = Field(None, max_length=1000, description="Role description")
    level: int = Field(0, ge=0, le=10, description="Hierarchy level")
    scope: str = Field("school", description="Role scope")
    is_default: bool = Field(False, description="Auto-assigned to new users")

    @validator('name')
    def validate_name(cls, v):
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Role name must contain only alphanumeric characters, hyphens, and underscores')
        return v.lower().strip()

    @validator('scope')
    def validate_scope(cls, v):
        allowed_scopes = ['school', 'organization', 'system']
        if v not in allowed_scopes:
            raise ValueError(f'Scope must be one of: {allowed_scopes}')
        return v


class PermissionBase(BaseModel):
    """Base permission schema"""
    code: str = Field(..., min_length=3, max_length=100, description="Unique permission code")
    name: str = Field(..., min_length=2, max_length=200, description="Human-readable permission name")
    description: Optional[str] = Field(None, max_length=1000, description="Permission description")
    category: str = Field(..., description="Permission category")
    resource: str = Field(..., description="Resource type")
    action: str = Field(..., description="Action type")
    scope: str = Field("school", description="Permission scope")

    @validator('code')
    def validate_code(cls, v):
        if not v.replace('.', '').replace('_', '').replace('-', '').isalnum():
            raise ValueError('Permission code must contain only alphanumeric characters, dots, hyphens, and underscores')
        return v.lower().strip()

    @validator('category')
    def validate_category(cls, v):
        allowed_categories = [
            'admin', 'user', 'student', 'academic', 'fee', 'attendance',
            'report', 'communication', 'settings'
        ]
        if v not in allowed_categories:
            raise ValueError(f'Category must be one of: {allowed_categories}')
        return v.lower().strip()

    @validator('scope')
    def validate_scope(cls, v):
        allowed_scopes = ['school', 'organization', 'system']
        if v not in allowed_scopes:
            raise ValueError(f'Scope must be one of: {allowed_scopes}')
        return v


# ==================== REQUEST SCHEMAS ====================

class RoleCreateRequest(RoleBase):
    """Schema for creating a new role"""
    parent_role_id: Optional[uuid.UUID] = Field(None, description="Parent role ID for hierarchy")


class RoleUpdateRequest(BaseModel):
    """Schema for updating a role"""
    display_name: Optional[str] = Field(None, min_length=2, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    parent_role_id: Optional[uuid.UUID] = None
    level: Optional[int] = Field(None, ge=0, le=10)
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None


class PermissionCreateRequest(PermissionBase):
    """Schema for creating a new permission"""
    pass


class PermissionUpdateRequest(BaseModel):
    """Schema for updating a permission"""
    name: Optional[str] = Field(None, min_length=2, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    is_active: Optional[bool] = None


class RolePermissionAssignRequest(BaseModel):
    """Schema for assigning permission to role"""
    permission_id: uuid.UUID = Field(..., description="Permission ID to assign")


class UserRoleAssignRequest(BaseModel):
    """Schema for assigning role to user"""
    role_id: uuid.UUID = Field(..., description="Role ID to assign")
    expires_at: Optional[datetime] = Field(None, description="Optional expiration date")


class PermissionCheckRequest(BaseModel):
    """Schema for checking user permission"""
    permission_code: str = Field(..., description="Permission code to check")
    user_id: Optional[uuid.UUID] = Field(None, description="User ID (if not current user)")


# ==================== RESPONSE SCHEMAS ====================

class RoleResponse(BaseModel):
    """Schema for role response"""
    id: uuid.UUID
    name: str
    display_name: str
    description: Optional[str]
    level: int
    scope: str
    is_active: bool
    is_system_role: bool
    is_default: bool
    parent_role_id: Optional[uuid.UUID]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]
    permissions: Optional[List['PermissionResponse']] = None

    class Config:
        from_attributes = True


class PermissionResponse(BaseModel):
    """Schema for permission response"""
    id: uuid.UUID
    code: str
    name: str
    description: Optional[str]
    category: str
    resource: str
    action: str
    scope: str
    is_active: bool
    is_system_permission: bool
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class RoleListResponse(BaseModel):
    """Schema for paginated role list response"""
    roles: List[RoleResponse]
    pagination: Dict[str, Any]


class PermissionListResponse(BaseModel):
    """Schema for paginated permission list response"""
    permissions: List[PermissionResponse]
    pagination: Dict[str, Any]


class UserPermissionResponse(BaseModel):
    """Schema for user permission response"""
    user_id: uuid.UUID
    permissions: List[str]
    cached: bool = False


class PermissionCheckResponse(BaseModel):
    """Schema for permission check response"""
    has_permission: bool
    permission_code: str
    user_id: uuid.UUID


class UserWithPermissionResponse(BaseModel):
    """Schema for users with specific permission"""
    id: uuid.UUID
    email: str
    full_name: str
    user_type: str
    is_active: bool


class RBACActionResponse(BaseModel):
    """Schema for RBAC action responses"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


class RBACErrorResponse(BaseModel):
    """Schema for RBAC error responses"""
    success: bool = False
    error: str
    details: Optional[Dict[str, Any]] = None


# ==================== FILTER SCHEMAS ====================

class RoleFilterParams(BaseModel):
    """Schema for role filtering parameters"""
    include_inactive: bool = Field(False, description="Include inactive roles")
    include_system: bool = Field(True, description="Include system roles")
    parent_role_id: Optional[uuid.UUID] = Field(None, description="Filter by parent role")
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(50, ge=1, le=100, description="Items per page")


class PermissionFilterParams(BaseModel):
    """Schema for permission filtering parameters"""
    category: Optional[str] = Field(None, description="Filter by category")
    resource: Optional[str] = Field(None, description="Filter by resource")
    action: Optional[str] = Field(None, description="Filter by action")
    include_inactive: bool = Field(False, description="Include inactive permissions")
    include_system: bool = Field(True, description="Include system permissions")
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(100, ge=1, le=200, description="Items per page")


# Update forward references
RoleResponse.model_rebuild()
PermissionResponse.model_rebuild()
