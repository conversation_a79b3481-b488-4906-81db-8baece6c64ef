# Pydantic schemas for request/response validation

# Import all schemas for easy access
from app.schemas.onboarding import *
from app.schemas.academic import *

# Export commonly used schemas
__all__ = [
    # Onboarding schemas
    'SchoolRegistrationRequest',
    'SchoolRegistrationResponse',
    'SubdomainCheckRequest',
    'SubdomainCheckResponse',

    # Academic schemas
    'AcademicYearCreateRequest',
    'AcademicYearUpdateRequest',
    'AcademicYearResponse',
    'AcademicYearListResponse',
    'AcademicYearActionRequest',
    'AcademicYearActionResponse',
    'ClassCreateRequest',
    'ClassResponse',
    'SectionCreateRequest',
    'SectionResponse',
    'ErrorResponse'
]
