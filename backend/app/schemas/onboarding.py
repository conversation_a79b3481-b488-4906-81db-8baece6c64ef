"""
Pydantic schemas for onboarding API endpoints
World-class validation and serialization for school registration
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, EmailStr, field_validator
import re


class SchoolRegistrationRequest(BaseModel):
    """Schema for school registration request"""
    
    # Required fields
    school_name: str = Field(
        ...,
        min_length=2,
        max_length=255,
        description="Name of the school or organization"
    )
    admin_email: EmailStr = Field(
        ...,
        description="Email address for the admin user"
    )
    admin_password: str = Field(
        ...,
        min_length=8,
        max_length=128,
        description="Password for the admin user"
    )
    preferred_subdomain: str = Field(
        ...,
        min_length=3,
        max_length=63,
        description="Desired subdomain for the school"
    )
    country: str = Field(
        default="India",
        max_length=100,
        description="Country where the school is located"
    )
    
    # Optional fields
    phone: Optional[str] = Field(
        None,
        max_length=20,
        description="Phone number (optional)"
    )
    is_multi_branch: bool = Field(
        default=False,
        description="Whether this is a multi-branch organization"
    )
    admin_first_name: str = Field(
        default="Admin",
        min_length=1,
        max_length=100,
        description="First name of the admin user"
    )
    admin_last_name: str = Field(
        default="User",
        min_length=1,
        max_length=100,
        description="Last name of the admin user"
    )
    
    @field_validator('school_name')
    @classmethod
    def validate_school_name(cls, v: str) -> str:
        """Validate and normalize school name"""
        if not v or not v.strip():
            raise ValueError("School name cannot be empty")
        
        # Remove extra whitespace
        normalized = ' '.join(v.strip().split())
        
        # Check for valid characters (letters, numbers, spaces, basic punctuation)
        if not re.match(r'^[a-zA-Z0-9\s\.\-\&\']+$', normalized):
            raise ValueError("School name contains invalid characters")
        
        return normalized
    
    @field_validator('preferred_subdomain')
    @classmethod
    def validate_subdomain(cls, v: str) -> str:
        """Validate and normalize subdomain"""
        if not v:
            raise ValueError("Subdomain cannot be empty")
        
        # Normalize to lowercase and remove whitespace
        normalized = v.lower().strip()
        
        # Validate subdomain format (RFC 1123)
        if not re.match(r'^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$', normalized):
            raise ValueError(
                "Subdomain must contain only lowercase letters, numbers, and hyphens. "
                "Cannot start or end with hyphen."
            )
        
        # Check for reserved words (basic check - detailed check in service)
        reserved_words = ['www', 'api', 'admin', 'mail', 'ftp', 'blog', 'shop']
        if normalized in reserved_words:
            raise ValueError(f"'{normalized}' is a reserved subdomain")
        
        return normalized
    
    @field_validator('admin_password')
    @classmethod
    def validate_password(cls, v: str) -> str:
        """Validate password strength"""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        # Check for required character types
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in v)
        
        if not all([has_upper, has_lower, has_digit, has_special]):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, one number, and one special character"
            )
        
        return v
    
    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v: Optional[str]) -> Optional[str]:
        """Validate phone number format"""
        if not v:
            return None
        
        # Remove all non-digit characters except + and -
        cleaned = re.sub(r'[^\d\+\-\s\(\)]', '', v.strip())
        
        # Basic validation - should have at least 10 digits
        digits_only = re.sub(r'[^\d]', '', cleaned)
        if len(digits_only) < 10:
            raise ValueError("Phone number must have at least 10 digits")
        
        return cleaned
    
    @field_validator('country')
    @classmethod
    def validate_country(cls, v: str) -> str:
        """Validate country name"""
        if not v or not v.strip():
            raise ValueError("Country cannot be empty")
        
        # Normalize country name
        normalized = v.strip().title()
        
        # Basic validation - only letters, spaces, and basic punctuation
        if not re.match(r'^[a-zA-Z\s\.\-\']+$', normalized):
            raise ValueError("Country name contains invalid characters")
        
        return normalized


class OrganizationInfo(BaseModel):
    """Organization information in response"""
    id: str
    name: str
    subdomain: Optional[str] = None


class SchoolInfo(BaseModel):
    """School information in response"""
    id: str
    name: str
    subdomain: str
    school_code: str


class AdminUserInfo(BaseModel):
    """Admin user information in response"""
    id: str
    email: str
    first_name: str
    last_name: str


class TrialLicenseInfo(BaseModel):
    """Trial license information in response"""
    plan_type: str
    plan_name: str
    start_date: str
    end_date: str
    max_students: int
    max_staff: int
    enabled_features: List[str]


class AcademicYearInfo(BaseModel):
    """Academic year information in response"""
    label: str
    start_date: str
    end_date: str
    is_active: bool


class SchoolRegistrationResponse(BaseModel):
    """Schema for school registration response"""
    success: bool
    message: str
    organization: OrganizationInfo
    school: SchoolInfo
    admin_user: AdminUserInfo
    trial_license: TrialLicenseInfo
    academic_year: AcademicYearInfo
    next_steps: List[str]


class SubdomainCheckRequest(BaseModel):
    """Schema for subdomain availability check"""
    subdomain: str = Field(
        ...,
        min_length=3,
        max_length=63,
        description="Subdomain to check availability"
    )
    
    @field_validator('subdomain')
    @classmethod
    def validate_subdomain(cls, v: str) -> str:
        """Validate subdomain format"""
        normalized = v.lower().strip()
        
        if not re.match(r'^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$', normalized):
            raise ValueError("Invalid subdomain format")
        
        return normalized


class SubdomainCheckResponse(BaseModel):
    """Schema for subdomain availability response"""
    is_valid: bool
    is_available: bool
    subdomain: str
    error: Optional[str] = None
    suggestions: Optional[List[str]] = None


class SubdomainSuggestionsRequest(BaseModel):
    """Schema for subdomain suggestions request"""
    school_name: str = Field(
        ...,
        min_length=2,
        max_length=255,
        description="School name for generating suggestions"
    )
    limit: int = Field(
        default=5,
        ge=1,
        le=10,
        description="Number of suggestions to return"
    )


class SubdomainSuggestionsResponse(BaseModel):
    """Schema for subdomain suggestions response"""
    suggestions: List[str]
    school_name: str


class EmailVerificationRequest(BaseModel):
    """Schema for email verification request"""
    verification_token: str = Field(
        ...,
        min_length=1,
        description="Email verification token"
    )


class EmailVerificationResponse(BaseModel):
    """Schema for email verification response"""
    success: bool
    message: str


class OnboardingStatusResponse(BaseModel):
    """Schema for onboarding status response"""
    school_id: str
    organization_id: str
    admin_user_id: str
    subdomain: str
    is_email_verified: bool
    is_profile_complete: bool
    trial_days_remaining: int
    completion_percentage: int
    next_steps: List[str]


class ErrorResponse(BaseModel):
    """Schema for error responses"""
    success: bool = False
    error: str
    details: Optional[Dict[str, Any]] = None
