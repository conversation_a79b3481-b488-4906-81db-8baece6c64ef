"""
Pydantic schemas for academic structure API endpoints
Comprehensive validation and serialization for academic year, class, and section management
"""

from typing import Optional, List, Dict, Any, Tuple
from datetime import date
from pydantic import BaseModel, Field, field_validator
import re


# Academic Year Schemas

class AcademicYearCreateRequest(BaseModel):
    """Schema for creating a new academic year"""
    
    year_label: str = Field(
        ...,
        min_length=7,
        max_length=20,
        description="Academic year label (e.g., '2024-25')",
        examples=["2024-25", "2025-26"]
    )
    start_date: date = Field(
        ...,
        description="Academic year start date"
    )
    end_date: date = Field(
        ...,
        description="Academic year end date"
    )
    display_name: Optional[str] = Field(
        None,
        max_length=100,
        description="Custom display name for the academic year"
    )
    description: Optional[str] = Field(
        None,
        max_length=500,
        description="Description of the academic year"
    )
    terms_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Terms/semesters configuration"
    )
    holidays_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Holidays and breaks configuration"
    )
    settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Academic year specific settings"
    )
    total_working_days: Optional[int] = Field(
        None,
        ge=1,
        le=400,
        description="Expected total working days in the academic year"
    )
    auto_activate: bool = Field(
        False,
        description="Auto-activate if no active academic year exists"
    )
    
    @field_validator('year_label')
    @classmethod
    def validate_year_label(cls, v: str) -> str:
        """Validate academic year label format"""
        v = v.strip()
        
        # Check basic format (YYYY-YY)
        if not re.match(r'^\d{4}-\d{2}$', v):
            raise ValueError("Year label must be in format YYYY-YY (e.g., 2024-25)")
        
        # Extract years and validate
        start_year, end_year_short = v.split('-')
        start_year_int = int(start_year)
        end_year_int = int(f"20{end_year_short}")
        
        # Validate year sequence
        if end_year_int != start_year_int + 1:
            raise ValueError("End year must be exactly one year after start year")
        
        # Validate reasonable year range (not too far in past or future)
        current_year = date.today().year
        if start_year_int < current_year - 10 or start_year_int > current_year + 10:
            raise ValueError("Academic year must be within reasonable range (±10 years)")
        
        return v
    
    @field_validator('end_date')
    @classmethod
    def validate_end_date(cls, v: date, info) -> date:
        """Validate end date is after start date"""
        if 'start_date' in info.data:
            start_date = info.data['start_date']
            if v <= start_date:
                raise ValueError("End date must be after start date")
            
            # Validate reasonable duration (6 months to 18 months)
            duration_days = (v - start_date).days
            if duration_days < 180:
                raise ValueError("Academic year must be at least 6 months long")
            if duration_days > 550:
                raise ValueError("Academic year cannot be longer than 18 months")
        
        return v


class AcademicYearUpdateRequest(BaseModel):
    """Schema for updating an academic year"""
    
    year_label: Optional[str] = Field(
        None,
        min_length=7,
        max_length=20,
        description="Academic year label (e.g., '2024-25')"
    )
    start_date: Optional[date] = Field(
        None,
        description="Academic year start date"
    )
    end_date: Optional[date] = Field(
        None,
        description="Academic year end date"
    )
    display_name: Optional[str] = Field(
        None,
        max_length=100,
        description="Custom display name for the academic year"
    )
    description: Optional[str] = Field(
        None,
        max_length=500,
        description="Description of the academic year"
    )
    terms_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Terms/semesters configuration"
    )
    holidays_config: Optional[Dict[str, Any]] = Field(
        None,
        description="Holidays and breaks configuration"
    )
    settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Academic year specific settings"
    )
    total_working_days: Optional[int] = Field(
        None,
        ge=1,
        le=400,
        description="Expected total working days in the academic year"
    )
    
    @field_validator('year_label')
    @classmethod
    def validate_year_label(cls, v: Optional[str]) -> Optional[str]:
        """Validate academic year label format"""
        if v is None:
            return v
        
        v = v.strip()
        
        # Check basic format (YYYY-YY)
        if not re.match(r'^\d{4}-\d{2}$', v):
            raise ValueError("Year label must be in format YYYY-YY (e.g., 2024-25)")
        
        return v


class AcademicYearResponse(BaseModel):
    """Schema for academic year response"""
    
    id: str
    school_id: str
    year_label: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    start_date: date
    end_date: date
    is_active: bool
    status: str
    total_working_days: Optional[int] = None
    actual_working_days: int
    terms_config: Optional[Dict[str, Any]] = None
    holidays_config: Optional[Dict[str, Any]] = None
    settings: Optional[Dict[str, Any]] = None
    academic_calendar_approved: bool
    approved_by: Optional[str] = None
    approved_at: Optional[date] = None
    created_at: str
    updated_at: str
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    
    # Computed fields
    is_current_year: bool
    duration_days: int
    progress_percentage: float
    remaining_days: int
    can_be_activated: Tuple[bool, str]
    can_be_completed: Tuple[bool, str]


class AcademicYearListResponse(BaseModel):
    """Schema for academic year list response"""
    
    success: bool
    academic_years: List[AcademicYearResponse]
    total_count: int
    limit: int
    offset: int
    has_more: bool


class AcademicYearActionRequest(BaseModel):
    """Schema for academic year actions (activate, complete, etc.)"""
    
    force: bool = Field(
        False,
        description="Force the action even if validation fails"
    )
    reason: Optional[str] = Field(
        None,
        max_length=255,
        description="Reason for the action"
    )


class AcademicYearActionResponse(BaseModel):
    """Schema for academic year action response"""
    
    success: bool
    message: str
    academic_year: Optional[AcademicYearResponse] = None
    previous_active: Optional[str] = None
    changes: Optional[Dict[str, Any]] = None


class AcademicYearGenerateRequest(BaseModel):
    """Schema for generating academic year suggestions"""
    
    start_year: int = Field(
        ...,
        ge=2020,
        le=2050,
        description="Starting year for the academic year"
    )
    pattern: str = Field(
        default="indian",
        description="Academic year pattern (indian, international, custom)"
    )
    
    @field_validator('pattern')
    @classmethod
    def validate_pattern(cls, v: str) -> str:
        """Validate academic year pattern"""
        valid_patterns = ['indian', 'international', 'custom']
        if v not in valid_patterns:
            raise ValueError(f"Pattern must be one of: {', '.join(valid_patterns)}")
        return v


class AcademicYearGenerateResponse(BaseModel):
    """Schema for academic year generation response"""
    
    year_label: str
    start_date: date
    end_date: date
    pattern: str
    suggested_display_name: str


# Class Schemas

class ClassCreateRequest(BaseModel):
    """Schema for creating a new class"""
    
    name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="Class name (e.g., 'Class 1', 'Grade 10', 'Nursery')"
    )
    display_name: Optional[str] = Field(
        None,
        max_length=200,
        description="Custom display name for the class"
    )
    short_name: Optional[str] = Field(
        None,
        max_length=20,
        description="Short name or abbreviation (e.g., '1st', '10th', 'N')"
    )
    description: Optional[str] = Field(
        None,
        max_length=500,
        description="Description of the class"
    )
    class_code: Optional[str] = Field(
        None,
        max_length=20,
        description="Unique class code identifier"
    )
    display_order: int = Field(
        0,
        ge=0,
        description="Display order for sorting classes"
    )
    academic_level: Optional[str] = Field(
        None,
        max_length=50,
        description="Academic level (Primary, Secondary, Higher Secondary)"
    )
    max_students_per_section: int = Field(
        40,
        ge=1,
        le=200,
        description="Maximum students allowed per section"
    )
    settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Class-specific settings"
    )
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate class name"""
        v = v.strip()
        if not v:
            raise ValueError("Class name cannot be empty")
        return v


class ClassResponse(BaseModel):
    """Schema for class response"""
    
    id: str
    school_id: str
    name: str
    display_name: Optional[str] = None
    short_name: Optional[str] = None
    description: Optional[str] = None
    class_code: Optional[str] = None
    display_order: int
    academic_level: Optional[str] = None
    max_students_per_section: int
    is_active: bool
    settings: Optional[Dict[str, Any]] = None
    created_at: str
    updated_at: str
    created_by: Optional[str] = None
    updated_by: Optional[str] = None


# Section Schemas

class SectionCreateRequest(BaseModel):
    """Schema for creating a new section"""
    
    name: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="Section name (e.g., 'A', 'B', 'Red', 'Blue')"
    )
    display_name: Optional[str] = Field(
        None,
        max_length=100,
        description="Custom display name for the section"
    )
    description: Optional[str] = Field(
        None,
        max_length=500,
        description="Description of the section"
    )
    section_code: Optional[str] = Field(
        None,
        max_length=20,
        description="Optional unique section identifier"
    )
    max_capacity: int = Field(
        40,
        ge=1,
        le=200,
        description="Maximum students allowed in this section"
    )
    settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Section-specific settings"
    )
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate section name"""
        v = v.strip().upper()  # Store in uppercase
        if not v:
            raise ValueError("Section name cannot be empty")
        return v


class SectionResponse(BaseModel):
    """Schema for section response"""
    
    id: str
    school_id: str
    name: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    section_code: Optional[str] = None
    max_capacity: int
    current_strength: int
    is_active: bool
    settings: Optional[Dict[str, Any]] = None
    created_at: str
    updated_at: str
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    
    # Computed fields
    available_capacity: int
    is_full: bool
    occupancy_percentage: float


# Error Response Schema

class ErrorResponse(BaseModel):
    """Schema for error responses"""
    success: bool = False
    error: str
    details: Optional[Dict[str, Any]] = None


# Subject Schemas

class SubjectCreateRequest(BaseModel):
    """Schema for creating a new subject"""

    subject_code: str = Field(
        ...,
        min_length=1,
        max_length=20,
        description="Unique subject code (e.g., 'MATH101', 'ENG201')",
        examples=["MATH101", "ENG201", "SCI301"]
    )
    name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="Subject name",
        examples=["Mathematics", "English Literature", "Physics"]
    )
    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Detailed subject description"
    )
    subject_type: str = Field(
        "core",
        description="Type of subject",
        examples=["core", "elective", "extra_curricular"]
    )
    academic_level: Optional[str] = Field(
        None,
        description="Academic level",
        examples=["primary", "secondary", "higher_secondary"]
    )
    default_credits: Optional[int] = Field(
        None,
        ge=1,
        le=10,
        description="Default credit hours for this subject"
    )
    default_hours_per_week: Optional[int] = Field(
        None,
        ge=1,
        le=20,
        description="Default weekly hours for this subject"
    )
    settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Subject-specific configuration settings"
    )
    is_active: bool = Field(
        True,
        description="Whether the subject is active"
    )

    @field_validator('subject_code')
    @classmethod
    def validate_subject_code(cls, v: str) -> str:
        """Validate subject code format"""
        if not v or not v.strip():
            raise ValueError("Subject code cannot be empty")

        # Convert to uppercase and validate format
        v = v.strip().upper()
        if not re.match(r'^[A-Z0-9_-]+$', v):
            raise ValueError("Subject code must contain only alphanumeric characters, hyphens, and underscores")

        return v

    @field_validator('subject_type')
    @classmethod
    def validate_subject_type(cls, v: str) -> str:
        """Validate subject type"""
        valid_types = ['core', 'elective', 'extra_curricular']
        if v not in valid_types:
            raise ValueError(f"Subject type must be one of: {', '.join(valid_types)}")
        return v

    @field_validator('academic_level')
    @classmethod
    def validate_academic_level(cls, v: Optional[str]) -> Optional[str]:
        """Validate academic level"""
        if v is None:
            return v

        valid_levels = ['primary', 'secondary', 'higher_secondary']
        if v not in valid_levels:
            raise ValueError(f"Academic level must be one of: {', '.join(valid_levels)}")
        return v


class SubjectUpdateRequest(BaseModel):
    """Schema for updating a subject"""

    subject_code: Optional[str] = Field(
        None,
        min_length=1,
        max_length=20,
        description="Unique subject code"
    )
    name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="Subject name"
    )
    description: Optional[str] = Field(
        None,
        max_length=1000,
        description="Subject description"
    )
    subject_type: Optional[str] = Field(
        None,
        description="Type of subject"
    )
    academic_level: Optional[str] = Field(
        None,
        description="Academic level"
    )
    default_credits: Optional[int] = Field(
        None,
        ge=1,
        le=10,
        description="Default credit hours"
    )
    default_hours_per_week: Optional[int] = Field(
        None,
        ge=1,
        le=20,
        description="Default weekly hours"
    )
    settings: Optional[Dict[str, Any]] = Field(
        None,
        description="Subject-specific settings"
    )
    is_active: Optional[bool] = Field(
        None,
        description="Whether the subject is active"
    )

    @field_validator('subject_code')
    @classmethod
    def validate_subject_code(cls, v: Optional[str]) -> Optional[str]:
        """Validate subject code format"""
        if v is None:
            return v

        if not v.strip():
            raise ValueError("Subject code cannot be empty")

        v = v.strip().upper()
        if not re.match(r'^[A-Z0-9_-]+$', v):
            raise ValueError("Subject code must contain only alphanumeric characters, hyphens, and underscores")

        return v

    @field_validator('subject_type')
    @classmethod
    def validate_subject_type(cls, v: Optional[str]) -> Optional[str]:
        """Validate subject type"""
        if v is None:
            return v

        valid_types = ['core', 'elective', 'extra_curricular']
        if v not in valid_types:
            raise ValueError(f"Subject type must be one of: {', '.join(valid_types)}")
        return v

    @field_validator('academic_level')
    @classmethod
    def validate_academic_level(cls, v: Optional[str]) -> Optional[str]:
        """Validate academic level"""
        if v is None:
            return v

        valid_levels = ['primary', 'secondary', 'higher_secondary']
        if v not in valid_levels:
            raise ValueError(f"Academic level must be one of: {', '.join(valid_levels)}")
        return v


class SubjectResponse(BaseModel):
    """Schema for subject response"""

    id: str
    school_id: str
    subject_code: str
    name: str
    description: Optional[str] = None
    subject_type: str
    academic_level: Optional[str] = None
    is_active: bool
    display_order: int
    default_credits: Optional[int] = None
    default_hours_per_week: Optional[int] = None
    settings: Optional[Dict[str, Any]] = None
    approved_by: Optional[str] = None
    approved_at: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    # Computed fields
    is_core_subject: bool
    is_elective_subject: bool
    is_extra_curricular: bool
    assigned_classes_count: int


class SubjectListResponse(BaseModel):
    """Schema for subject list response"""

    success: bool = True
    subjects: List[SubjectResponse]
    pagination: Dict[str, Any]


class SubjectActionRequest(BaseModel):
    """Schema for subject action requests (delete, etc.)"""

    reason: Optional[str] = Field(
        None,
        max_length=255,
        description="Reason for the action"
    )


class SubjectActionResponse(BaseModel):
    """Schema for subject action responses"""

    success: bool
    message: str
    subject: Optional[SubjectResponse] = None
