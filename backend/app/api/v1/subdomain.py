"""
Subdomain management API endpoints for School ERP
Handles subdomain validation, availability checking, and management
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from pydantic import BaseModel, Field, validator
import uuid

from app.services.subdomain_service.subdomain import subdomain_service
from app.services.audit_service.audit import log_audit_event
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/subdomain", tags=["Subdomain Management"])


# Request/Response Models
class SubdomainValidationRequest(BaseModel):
    """Subdomain validation request"""
    subdomain: str = Field(..., min_length=3, max_length=63, description="Subdomain to validate")
    
    @validator('subdomain')
    def normalize_subdomain(cls, v):
        return v.lower().strip()


class SubdomainValidationResponse(BaseModel):
    """Subdomain validation response"""
    is_valid: bool
    is_available: Optional[bool] = None
    subdomain: Optional[str] = None
    error: Optional[str] = None
    suggestions: List[str] = []
    message: Optional[str] = None


class SubdomainReservationRequest(BaseModel):
    """Subdomain reservation request"""
    subdomain: str = Field(..., min_length=3, max_length=63, description="Subdomain to reserve")
    school_id: str = Field(..., description="School ID to associate with subdomain")
    
    @validator('subdomain')
    def normalize_subdomain(cls, v):
        return v.lower().strip()
    
    @validator('school_id')
    def validate_school_id(cls, v):
        try:
            uuid.UUID(v)
            return v
        except ValueError:
            raise ValueError('Invalid school ID format')


class SubdomainReservationResponse(BaseModel):
    """Subdomain reservation response"""
    success: bool
    subdomain: Optional[str] = None
    message: str
    error: Optional[str] = None


class SubdomainSuggestionsRequest(BaseModel):
    """Subdomain suggestions request"""
    school_name: str = Field(..., min_length=1, description="School name for generating suggestions")
    limit: int = Field(5, ge=1, le=10, description="Number of suggestions to return")


class SubdomainSuggestionsResponse(BaseModel):
    """Subdomain suggestions response"""
    suggestions: List[str]
    school_name: str


class SubdomainInfoResponse(BaseModel):
    """Subdomain information response"""
    subdomain: str
    school_id: str
    school_name: str
    organization_id: Optional[str] = None
    status: str
    settings: Dict[str, Any]


def get_current_user_id(request: Request) -> Optional[uuid.UUID]:
    """Get current user ID from request state"""
    user_id = getattr(request.state, 'user_id', None)
    if user_id:
        return uuid.UUID(user_id)
    return None


def get_current_school_id(request: Request) -> Optional[uuid.UUID]:
    """Get current school ID from request state"""
    # Try from tenant context first
    tenant_school_id = getattr(request.state, 'tenant_school_id', None)
    if tenant_school_id:
        return uuid.UUID(tenant_school_id)
    
    # Try from user context
    user_school_id = getattr(request.state, 'school_id', None)
    if user_school_id:
        return uuid.UUID(user_school_id)
    
    return None


@router.post("/validate", response_model=SubdomainValidationResponse)
async def validate_subdomain(
    validation_data: SubdomainValidationRequest,
    request: Request
):
    """
    Validate subdomain format and check availability
    """
    try:
        # Check availability (includes validation)
        result = subdomain_service.check_availability(validation_data.subdomain)
        
        # Log validation attempt for audit
        user_id = get_current_user_id(request)
        school_id = get_current_school_id(request)
        
        if school_id:
            log_audit_event(
                school_id=school_id,
                module="subdomain",
                action="subdomain_validated",
                user_id=user_id,
                event_data={
                    "subdomain": validation_data.subdomain,
                    "is_valid": result.get("is_valid", False),
                    "is_available": result.get("is_available", False)
                },
                severity="info",
                category="system"
            )
        
        return SubdomainValidationResponse(**result)
        
    except Exception as e:
        logger.error(f"Subdomain validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Subdomain validation failed. Please try again."
        )


@router.post("/reserve", response_model=SubdomainReservationResponse)
async def reserve_subdomain(
    reservation_data: SubdomainReservationRequest,
    request: Request
):
    """
    Reserve subdomain for a school (requires authentication)
    """
    try:
        # Get current user for audit
        user_id = get_current_user_id(request)
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )
        
        school_id = uuid.UUID(reservation_data.school_id)
        
        # Reserve subdomain
        result = subdomain_service.reserve_subdomain(
            subdomain=reservation_data.subdomain,
            school_id=school_id,
            user_id=user_id
        )
        
        if result.get("success"):
            return SubdomainReservationResponse(
                success=True,
                subdomain=result["subdomain"],
                message=result["message"]
            )
        else:
            return SubdomainReservationResponse(
                success=False,
                message="Failed to reserve subdomain",
                error=result.get("error", "Unknown error")
            )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Subdomain reservation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Subdomain reservation failed. Please try again."
        )


@router.post("/suggestions", response_model=SubdomainSuggestionsResponse)
async def get_subdomain_suggestions(
    suggestions_data: SubdomainSuggestionsRequest,
    request: Request
):
    """
    Generate subdomain suggestions based on school name
    """
    try:
        suggestions = subdomain_service.get_subdomain_suggestions(
            school_name=suggestions_data.school_name,
            limit=suggestions_data.limit
        )
        
        # Log suggestion request for analytics
        user_id = get_current_user_id(request)
        school_id = get_current_school_id(request)
        
        if school_id:
            log_audit_event(
                school_id=school_id,
                module="subdomain",
                action="suggestions_requested",
                user_id=user_id,
                event_data={
                    "school_name": suggestions_data.school_name,
                    "suggestions_count": len(suggestions)
                },
                severity="info",
                category="system"
            )
        
        return SubdomainSuggestionsResponse(
            suggestions=suggestions,
            school_name=suggestions_data.school_name
        )
        
    except Exception as e:
        logger.error(f"Subdomain suggestions error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate subdomain suggestions. Please try again."
        )


@router.get("/info/{subdomain}", response_model=SubdomainInfoResponse)
async def get_subdomain_info(
    subdomain: str,
    request: Request
):
    """
    Get information about a subdomain (public endpoint)
    """
    try:
        # Resolve subdomain to school information
        school_info = subdomain_service.resolve_subdomain_to_school(subdomain)
        
        if not school_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No school found for subdomain '{subdomain}'"
            )
        
        return SubdomainInfoResponse(**school_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Subdomain info error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve subdomain information"
        )


@router.get("/reserved", response_model=List[str])
async def get_reserved_subdomains():
    """
    Get list of reserved subdomains that cannot be used
    """
    try:
        return list(subdomain_service.RESERVED_SUBDOMAINS)
        
    except Exception as e:
        logger.error(f"Reserved subdomains error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve reserved subdomains"
        )


@router.get("/current", response_model=Optional[SubdomainInfoResponse])
async def get_current_subdomain_info(request: Request):
    """
    Get current subdomain information from request context
    """
    try:
        # Get subdomain from tenant context
        subdomain = getattr(request.state, 'tenant_subdomain', None)
        
        if not subdomain:
            return None
        
        # Get school information from tenant context
        school_id = getattr(request.state, 'tenant_school_id', None)
        school_name = getattr(request.state, 'tenant_school_name', None)
        organization_id = getattr(request.state, 'tenant_organization_id', None)
        settings = getattr(request.state, 'tenant_settings', {})
        
        if school_id:
            return SubdomainInfoResponse(
                subdomain=subdomain,
                school_id=school_id,
                school_name=school_name or "Unknown School",
                organization_id=organization_id,
                status="active",  # Assume active if resolved
                settings=settings
            )
        
        return None
        
    except Exception as e:
        logger.error(f"Current subdomain info error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve current subdomain information"
        )
