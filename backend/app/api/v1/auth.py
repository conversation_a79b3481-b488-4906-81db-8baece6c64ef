"""
Authentication API endpoints for School ERP
Secure authentication with comprehensive validation and audit logging
"""

from datetime import datetime
from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from fastapi.security import HTTPBearer
from pydantic import BaseModel, EmailStr, Field, validator
import uuid

from app.services.auth_service.auth import auth_service, AuthenticationError
from app.services.audit_service.audit import log_audit_event, log_security_event
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger(__name__)
router = APIRouter(prefix="/auth", tags=["Authentication"])
security = HTTPBearer()


# Request/Response Models
class LoginRequest(BaseModel):
    """Login request model with validation"""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=1, description="User password")
    school_subdomain: Optional[str] = Field(None, description="School subdomain for tenant routing")
    remember_me: bool = Field(False, description="Extended session duration")
    
    @validator('email')
    def validate_email(cls, v):
        return v.lower().strip()
    
    @validator('school_subdomain')
    def validate_subdomain(cls, v):
        if v:
            return v.lower().strip()
        return v


class LoginResponse(BaseModel):
    """Login response model"""
    success: bool
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: Dict[str, Any]
    school: Dict[str, Any]
    session: Dict[str, Any]
    localization: Dict[str, Any]


class RefreshTokenRequest(BaseModel):
    """Refresh token request model"""
    refresh_token: str = Field(..., description="Valid refresh token")
    school_subdomain: Optional[str] = Field(None, description="School subdomain")


class RefreshTokenResponse(BaseModel):
    """Refresh token response model"""
    success: bool
    access_token: str
    token_type: str = "bearer"
    expires_in: int


class ChangePasswordRequest(BaseModel):
    """Change password request model"""
    current_password: str = Field(..., min_length=1, description="Current password")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., min_length=8, description="Confirm new password")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('Passwords do not match')
        return v


class LogoutRequest(BaseModel):
    """Logout request model"""
    logout_all_sessions: bool = Field(False, description="Logout from all sessions")


class StandardResponse(BaseModel):
    """Standard API response model"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


def get_client_info(request: Request) -> Dict[str, str]:
    """Extract client information from request"""
    return {
        "ip_address": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("user-agent", "unknown"),
        "correlation_id": getattr(request.state, 'correlation_id', None)
    }


def get_school_id_from_request(request: Request) -> Optional[uuid.UUID]:
    """Get school ID from request context"""
    # Try from tenant context first (subdomain routing)
    tenant_school_id = getattr(request.state, 'tenant_school_id', None)
    if tenant_school_id:
        return uuid.UUID(tenant_school_id)
    
    # Try from authenticated user context
    user_school_id = getattr(request.state, 'school_id', None)
    if user_school_id:
        return uuid.UUID(user_school_id)
    
    return None


@router.post("/login", response_model=LoginResponse)
async def login(
    login_data: LoginRequest,
    request: Request,
    response: Response
):
    """
    Authenticate user and return JWT tokens
    """
    client_info = get_client_info(request)
    
    try:
        # Determine school ID for authentication
        school_id = None
        
        # If subdomain provided in request, use it
        if login_data.school_subdomain:
            from app.services.subdomain_service.subdomain import subdomain_service
            school_info = subdomain_service.resolve_subdomain_to_school(login_data.school_subdomain)
            if not school_info:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="School not found for the provided subdomain"
                )
            school_id = uuid.UUID(school_info["school_id"])
        
        # Try to get school ID from tenant context (subdomain routing)
        elif hasattr(request.state, 'tenant_school_id') and request.state.tenant_school_id:
            school_id = uuid.UUID(request.state.tenant_school_id)
        
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="School identification required. Please provide subdomain or access through school-specific URL."
            )
        
        # Authenticate user
        auth_result = await auth_service.authenticate_user(
            email=login_data.email,
            password=login_data.password,
            school_id=school_id,
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"]
        )
        
        # Set secure cookie for refresh token if remember_me is true
        if login_data.remember_me:
            response.set_cookie(
                key="refresh_token",
                value=auth_result["refresh_token"],
                max_age=settings.REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60,
                httponly=True,
                secure=settings.ENVIRONMENT == "production",
                samesite="lax"
            )
        
        # Log successful login
        log_security_event(
            school_id=school_id,
            action="login_success",
            user_id=uuid.UUID(auth_result["user"]["id"]),
            event_data={
                "email": login_data.email,
                "remember_me": login_data.remember_me
            },
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"],
            severity="info"
        )
        
        return LoginResponse(**auth_result)
        
    except AuthenticationError as e:
        # Log failed login attempt
        log_security_event(
            school_id=school_id or uuid.uuid4(),  # Use dummy ID if school not found
            action="login_failed",
            event_data={
                "email": login_data.email,
                "error": str(e)
            },
            ip_address=client_info["ip_address"],
            user_agent=client_info["user_agent"],
            severity="warning"
        )
        
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed. Please try again."
        )


@router.post("/refresh", response_model=RefreshTokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    request: Request
):
    """
    Refresh access token using valid refresh token
    """
    try:
        # Determine school ID
        school_id = get_school_id_from_request(request)
        
        if not school_id:
            # Try to resolve from subdomain in request
            if refresh_data.school_subdomain:
                from app.services.subdomain_service.subdomain import subdomain_service
                school_info = subdomain_service.resolve_subdomain_to_school(refresh_data.school_subdomain)
                if school_info:
                    school_id = uuid.UUID(school_info["school_id"])
        
        if not school_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="School identification required"
            )
        
        # Refresh token
        result = await auth_service.refresh_access_token(
            refresh_token=refresh_data.refresh_token,
            school_id=school_id
        )
        
        return RefreshTokenResponse(**result)
        
    except AuthenticationError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed. Please login again."
        )


@router.post("/logout", response_model=StandardResponse)
async def logout(
    logout_data: LogoutRequest,
    request: Request,
    response: Response
):
    """
    Logout user and invalidate tokens
    """
    try:
        # Get user information from request state (set by auth middleware)
        user_id = getattr(request.state, 'user_id', None)
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )
        
        user_id = uuid.UUID(user_id)
        
        # Get session and token information
        session_id = request.headers.get("X-Session-ID")
        access_token = None
        
        # Extract access token from Authorization header
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            access_token = authorization.split(" ")[1]
        
        # Logout user
        if logout_data.logout_all_sessions:
            result = await auth_service.logout_all_sessions(user_id)
        else:
            result = await auth_service.logout_user(
                user_id=user_id,
                session_id=session_id,
                access_token=access_token
            )
        
        # Clear refresh token cookie
        response.delete_cookie(key="refresh_token")
        
        # Log logout event
        school_id = get_school_id_from_request(request)
        if school_id:
            client_info = get_client_info(request)
            log_security_event(
                school_id=school_id,
                action="logout_success",
                user_id=user_id,
                event_data={
                    "logout_all_sessions": logout_data.logout_all_sessions
                },
                ip_address=client_info["ip_address"],
                user_agent=client_info["user_agent"],
                severity="info"
            )
        
        return StandardResponse(
            success=result["success"],
            message=result["message"]
        )
        
    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed. Please try again."
        )


@router.post("/change-password", response_model=StandardResponse)
async def change_password(
    password_data: ChangePasswordRequest,
    request: Request
):
    """
    Change user password with validation
    """
    try:
        # Get user information from request state
        user_id = getattr(request.state, 'user_id', None)
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )
        
        user_id = uuid.UUID(user_id)
        
        # Change password
        result = await auth_service.change_password(
            user_id=user_id,
            current_password=password_data.current_password,
            new_password=password_data.new_password
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result["message"]
            )
        
        # Log password change event
        school_id = get_school_id_from_request(request)
        if school_id:
            client_info = get_client_info(request)
            log_security_event(
                school_id=school_id,
                action="password_changed",
                user_id=user_id,
                ip_address=client_info["ip_address"],
                user_agent=client_info["user_agent"],
                severity="info"
            )
        
        return StandardResponse(
            success=True,
            message=result["message"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password change error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed. Please try again."
        )


@router.get("/me", response_model=Dict[str, Any])
async def get_current_user(request: Request):
    """
    Get current authenticated user information
    """
    try:
        # Get user from request state (set by auth middleware)
        user = getattr(request.state, 'user', None)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required"
            )
        
        # Get additional context
        school_id = getattr(request.state, 'school_id', None)
        permissions = getattr(request.state, 'permissions', [])
        tenant_settings = getattr(request.state, 'tenant_settings', {})
        
        return {
            "user": {
                "id": str(user.id),
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "full_name": user.get_full_name(),
                "user_type": user.user_type,
                "is_verified": user.is_verified,
                "avatar_url": user.avatar_url,
                "last_login": user.last_login.isoformat() if user.last_login else None
            },
            "school": {
                "id": school_id
            },
            "permissions": permissions,
            "settings": tenant_settings
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get current user error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user information"
        )
