"""
RBAC API endpoints for School ERP
Role and permission management with comprehensive validation and security
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
import uuid

from app.schemas.rbac import (
    RoleCreateRequest, RoleUpdateRequest, RoleResponse, RoleListResponse,
    PermissionCreateRequest, PermissionUpdateRequest, PermissionResponse, PermissionListResponse,
    RolePermissionAssignRequest, UserRoleAssignRequest, PermissionCheckRequest,
    UserPermissionResponse, PermissionCheckResponse, UserWithPermissionResponse,
    RBACActionResponse, RBACErrorResponse, RoleFilterParams, PermissionFilterParams
)
from app.services.auth_service.rbac import rbac_service, RBACError, PermissionDeniedError
from app.core.security import get_current_user
from app.models.auth import User

router = APIRouter(prefix="/rbac", tags=["RBAC Management"])


# ==================== ROLE MANAGEMENT ENDPOINTS ====================

@router.post("/roles", response_model=RBACActionResponse, status_code=status.HTTP_201_CREATED)
async def create_role(
    role_data: RoleCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Create a new role
    
    Requires: admin.all or user.manage permission
    """
    try:
        # Check permissions
        if not current_user.has_permission("admin.all") and not current_user.has_permission("user.manage"):
            raise PermissionDeniedError("Insufficient permissions to create roles")
        
        result = rbac_service.create_role(
            school_id=current_user.school_id,
            name=role_data.name,
            display_name=role_data.display_name,
            description=role_data.description,
            parent_role_id=role_data.parent_role_id,
            level=role_data.level,
            scope=role_data.scope,
            is_default=role_data.is_default,
            created_by=current_user.id
        )
        
        return RBACActionResponse(
            success=result["success"],
            message=result["message"],
            data=result["role"]
        )
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except RBACError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/roles", response_model=RoleListResponse)
async def list_roles(
    include_inactive: bool = Query(False, description="Include inactive roles"),
    include_system: bool = Query(True, description="Include system roles"),
    parent_role_id: Optional[uuid.UUID] = Query(None, description="Filter by parent role"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_user)
):
    """
    List roles with filtering and pagination
    
    Requires: admin.all, user.manage, or user.read permission
    """
    try:
        # Check permissions
        if not any([
            current_user.has_permission("admin.all"),
            current_user.has_permission("user.manage"),
            current_user.has_permission("user.read")
        ]):
            raise PermissionDeniedError("Insufficient permissions to view roles")
        
        result = rbac_service.list_roles(
            school_id=current_user.school_id,
            include_inactive=include_inactive,
            include_system=include_system,
            parent_role_id=parent_role_id,
            page=page,
            page_size=page_size
        )
        
        return RoleListResponse(**result)
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/roles/{role_id}", response_model=RoleResponse)
async def get_role(
    role_id: uuid.UUID,
    include_permissions: bool = Query(False, description="Include role permissions"),
    current_user: User = Depends(get_current_user)
):
    """
    Get role by ID with optional permission details
    
    Requires: admin.all, user.manage, or user.read permission
    """
    try:
        # Check permissions
        if not any([
            current_user.has_permission("admin.all"),
            current_user.has_permission("user.manage"),
            current_user.has_permission("user.read")
        ]):
            raise PermissionDeniedError("Insufficient permissions to view roles")
        
        role = rbac_service.get_role(
            school_id=current_user.school_id,
            role_id=role_id,
            include_permissions=include_permissions
        )
        
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found"
            )
        
        return RoleResponse(**role)
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put("/roles/{role_id}", response_model=RBACActionResponse)
async def update_role(
    role_id: uuid.UUID,
    role_data: RoleUpdateRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Update role
    
    Requires: admin.all or user.manage permission
    """
    try:
        # Check permissions
        if not current_user.has_permission("admin.all") and not current_user.has_permission("user.manage"):
            raise PermissionDeniedError("Insufficient permissions to update roles")
        
        result = rbac_service.update_role(
            school_id=current_user.school_id,
            role_id=role_id,
            display_name=role_data.display_name,
            description=role_data.description,
            parent_role_id=role_data.parent_role_id,
            level=role_data.level,
            is_active=role_data.is_active,
            is_default=role_data.is_default,
            updated_by=current_user.id
        )
        
        return RBACActionResponse(
            success=result["success"],
            message=result["message"],
            data=result["role"]
        )
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except RBACError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete("/roles/{role_id}", response_model=RBACActionResponse)
async def delete_role(
    role_id: uuid.UUID,
    force: bool = Query(False, description="Force deletion (reassign users)"),
    current_user: User = Depends(get_current_user)
):
    """
    Delete role
    
    Requires: admin.all permission
    """
    try:
        # Check permissions
        if not current_user.has_permission("admin.all"):
            raise PermissionDeniedError("Insufficient permissions to delete roles")
        
        result = rbac_service.delete_role(
            school_id=current_user.school_id,
            role_id=role_id,
            deleted_by=current_user.id,
            force=force
        )
        
        return RBACActionResponse(
            success=result["success"],
            message=result["message"]
        )
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except RBACError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# ==================== PERMISSION MANAGEMENT ENDPOINTS ====================

@router.post("/permissions", response_model=RBACActionResponse, status_code=status.HTTP_201_CREATED)
async def create_permission(
    permission_data: PermissionCreateRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Create a new permission

    Requires: admin.all permission
    """
    try:
        # Check permissions
        if not current_user.has_permission("admin.all"):
            raise PermissionDeniedError("Insufficient permissions to create permissions")

        result = rbac_service.create_permission(
            school_id=current_user.school_id,
            code=permission_data.code,
            name=permission_data.name,
            category=permission_data.category,
            resource=permission_data.resource,
            action=permission_data.action,
            description=permission_data.description,
            scope=permission_data.scope,
            created_by=current_user.id
        )

        return RBACActionResponse(
            success=result["success"],
            message=result["message"],
            data=result["permission"]
        )

    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except RBACError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/permissions", response_model=PermissionListResponse)
async def list_permissions(
    category: Optional[str] = Query(None, description="Filter by category"),
    resource: Optional[str] = Query(None, description="Filter by resource"),
    action: Optional[str] = Query(None, description="Filter by action"),
    include_inactive: bool = Query(False, description="Include inactive permissions"),
    include_system: bool = Query(True, description="Include system permissions"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(100, ge=1, le=200, description="Items per page"),
    current_user: User = Depends(get_current_user)
):
    """
    List permissions with filtering and pagination

    Requires: admin.all, user.manage, or user.read permission
    """
    try:
        # Check permissions
        if not any([
            current_user.has_permission("admin.all"),
            current_user.has_permission("user.manage"),
            current_user.has_permission("user.read")
        ]):
            raise PermissionDeniedError("Insufficient permissions to view permissions")

        result = rbac_service.list_permissions(
            school_id=current_user.school_id,
            category=category,
            resource=resource,
            action=action,
            include_inactive=include_inactive,
            include_system=include_system,
            page=page,
            page_size=page_size
        )

        return PermissionListResponse(**result)

    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/permissions/{permission_id}", response_model=PermissionResponse)
async def get_permission(
    permission_id: uuid.UUID,
    current_user: User = Depends(get_current_user)
):
    """
    Get permission by ID

    Requires: admin.all, user.manage, or user.read permission
    """
    try:
        # Check permissions
        if not any([
            current_user.has_permission("admin.all"),
            current_user.has_permission("user.manage"),
            current_user.has_permission("user.read")
        ]):
            raise PermissionDeniedError("Insufficient permissions to view permissions")

        permission = rbac_service.get_permission(
            school_id=current_user.school_id,
            permission_id=permission_id
        )

        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Permission not found"
            )

        return PermissionResponse(**permission)

    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# ==================== ROLE-PERMISSION ASSIGNMENT ENDPOINTS ====================

@router.post("/roles/{role_id}/permissions", response_model=RBACActionResponse)
async def assign_permission_to_role(
    role_id: uuid.UUID,
    assignment_data: RolePermissionAssignRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Assign permission to role

    Requires: admin.all or user.manage permission
    """
    try:
        # Check permissions
        if not current_user.has_permission("admin.all") and not current_user.has_permission("user.manage"):
            raise PermissionDeniedError("Insufficient permissions to assign permissions to roles")

        result = rbac_service.assign_permission_to_role(
            school_id=current_user.school_id,
            role_id=role_id,
            permission_id=assignment_data.permission_id,
            assigned_by=current_user.id
        )

        return RBACActionResponse(
            success=result["success"],
            message=result["message"]
        )

    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except RBACError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete("/roles/{role_id}/permissions/{permission_id}", response_model=RBACActionResponse)
async def remove_permission_from_role(
    role_id: uuid.UUID,
    permission_id: uuid.UUID,
    current_user: User = Depends(get_current_user)
):
    """
    Remove permission from role

    Requires: admin.all or user.manage permission
    """
    try:
        # Check permissions
        if not current_user.has_permission("admin.all") and not current_user.has_permission("user.manage"):
            raise PermissionDeniedError("Insufficient permissions to remove permissions from roles")

        result = rbac_service.remove_permission_from_role(
            school_id=current_user.school_id,
            role_id=role_id,
            permission_id=permission_id,
            removed_by=current_user.id
        )

        return RBACActionResponse(
            success=result["success"],
            message=result["message"]
        )

    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except RBACError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# ==================== USER-ROLE ASSIGNMENT ENDPOINTS ====================

@router.post("/users/{user_id}/roles", response_model=RBACActionResponse)
async def assign_role_to_user(
    user_id: uuid.UUID,
    assignment_data: UserRoleAssignRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Assign role to user

    Requires: admin.all or user.manage permission
    """
    try:
        # Check permissions
        if not current_user.has_permission("admin.all") and not current_user.has_permission("user.manage"):
            raise PermissionDeniedError("Insufficient permissions to assign roles to users")

        result = rbac_service.assign_role_to_user(
            school_id=current_user.school_id,
            user_id=user_id,
            role_id=assignment_data.role_id,
            assigned_by=current_user.id,
            expires_at=assignment_data.expires_at
        )

        return RBACActionResponse(
            success=result["success"],
            message=result["message"]
        )

    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except RBACError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete("/users/{user_id}/roles/{role_id}", response_model=RBACActionResponse)
async def remove_role_from_user(
    user_id: uuid.UUID,
    role_id: uuid.UUID,
    current_user: User = Depends(get_current_user)
):
    """
    Remove role from user

    Requires: admin.all or user.manage permission
    """
    try:
        # Check permissions
        if not current_user.has_permission("admin.all") and not current_user.has_permission("user.manage"):
            raise PermissionDeniedError("Insufficient permissions to remove roles from users")

        result = rbac_service.remove_role_from_user(
            school_id=current_user.school_id,
            user_id=user_id,
            role_id=role_id,
            removed_by=current_user.id
        )

        return RBACActionResponse(
            success=result["success"],
            message=result["message"]
        )

    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except RBACError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# ==================== PERMISSION CHECKING ENDPOINTS ====================

@router.post("/check-permission", response_model=PermissionCheckResponse)
async def check_permission(
    check_data: PermissionCheckRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Check if user has specific permission

    Can check current user or another user (requires admin.all or user.read permission)
    """
    try:
        target_user_id = check_data.user_id or current_user.id

        # If checking another user's permissions, require additional permissions
        if target_user_id != current_user.id:
            if not current_user.has_permission("admin.all") and not current_user.has_permission("user.read"):
                raise PermissionDeniedError("Insufficient permissions to check other users' permissions")

        has_permission = rbac_service.check_user_permission(
            school_id=current_user.school_id,
            user_id=target_user_id,
            permission_code=check_data.permission_code
        )

        return PermissionCheckResponse(
            has_permission=has_permission,
            permission_code=check_data.permission_code,
            user_id=target_user_id
        )

    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/users/{user_id}/permissions", response_model=UserPermissionResponse)
async def get_user_permissions(
    user_id: uuid.UUID,
    current_user: User = Depends(get_current_user)
):
    """
    Get all permissions for a user

    Can get current user permissions or another user (requires admin.all or user.read permission)
    """
    try:
        # If checking another user's permissions, require additional permissions
        if user_id != current_user.id:
            if not current_user.has_permission("admin.all") and not current_user.has_permission("user.read"):
                raise PermissionDeniedError("Insufficient permissions to view other users' permissions")

        permissions = rbac_service.get_user_permissions(
            school_id=current_user.school_id,
            user_id=user_id
        )

        return UserPermissionResponse(
            user_id=user_id,
            permissions=permissions,
            cached=True
        )

    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
