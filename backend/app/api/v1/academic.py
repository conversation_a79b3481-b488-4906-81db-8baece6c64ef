"""
Academic structure API endpoints for School ERP
Handles academic year, class, and section management
"""

from typing import Optional, List
from datetime import date
from fastapi import APIRouter, Depends, HTTPException, Request, Query, Path
from sqlalchemy.orm import Session
import uuid

from app.core.database import get_db
from app.core.logging import get_logger
from app.models.auth import User
from app.services.academic_service.academic_year import academic_year_service, AcademicYearError
from app.services.academic_service.subject import subject_service, SubjectError
from app.schemas.academic import (
    AcademicYearCreateRequest,
    AcademicYearUpdateRequest,
    AcademicYearResponse,
    AcademicYearListResponse,
    AcademicYearActionRequest,
    AcademicYearActionResponse,
    AcademicYearGenerateRequest,
    AcademicYearGenerateResponse,
    SubjectCreateRequest,
    SubjectUpdateRequest,
    SubjectResponse,
    SubjectListResponse,
    SubjectActionRequest,
    SubjectActionResponse,
    ErrorResponse
)

logger = get_logger(__name__)

# Create router with prefix and tags
router = APIRouter(prefix="/academic", tags=["academic"])


# Dependency to get current user from request state (set by AuthenticationMiddleware)
def get_current_user(request: Request) -> User:
    """Get current authenticated user from request state"""
    user = getattr(request.state, 'user', None)
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Authentication required"
        )
    return user


# Academic Year Endpoints

@router.post(
    "/years",
    response_model=AcademicYearActionResponse,
    status_code=201,
    summary="Create Academic Year",
    description="""
    Create a new academic year for the school.
    
    **Features:**
    - Validates year label format (YYYY-YY)
    - Prevents overlapping academic years
    - Supports auto-activation if no active year exists
    - Comprehensive audit logging
    - Multi-tenant isolation
    
    **Permissions Required:**
    - `academic.academic_year.create`
    
    **Business Rules:**
    - Year label must be unique per school
    - Start date must be before end date
    - Academic year duration must be 6-18 months
    - Only one active academic year per school
    """
)
async def create_academic_year(
    request_data: AcademicYearCreateRequest,
    request: Request,
    db: Session = Depends(get_db)
) -> AcademicYearActionResponse:
    """
    Create a new academic year
    
    Creates a new academic year with validation and business rule enforcement.
    Supports automatic activation if no active year exists.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.academic_year.create" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to create academic year"
            )

        # Create academic year
        result = academic_year_service.create_academic_year(
            school_id=current_user.school_id,
            year_label=request_data.year_label,
            start_date=request_data.start_date,
            end_date=request_data.end_date,
            user_id=current_user.id,
            display_name=request_data.display_name,
            description=request_data.description,
            terms_config=request_data.terms_config,
            holidays_config=request_data.holidays_config,
            settings=request_data.settings,
            auto_activate=request_data.auto_activate
        )
        
        return AcademicYearActionResponse(
            success=result["success"],
            message=result["message"],
            academic_year=AcademicYearResponse(**result["academic_year"])
        )
        
    except AcademicYearError as e:
        logger.warning(f"Academic year creation failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error creating academic year: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/years",
    response_model=AcademicYearListResponse,
    summary="List Academic Years",
    description="""
    Retrieve a list of academic years for the school.
    
    **Features:**
    - Pagination support
    - Filtering by status
    - Sorting options
    - Multi-tenant isolation
    
    **Permissions Required:**
    - `academic.academic_year.read`
    
    **Query Parameters:**
    - `include_inactive`: Include completed/archived years
    - `limit`: Maximum results per page (1-100)
    - `offset`: Number of results to skip
    - `order_by`: Field to sort by
    - `order_direction`: Sort direction (asc/desc)
    """
)
async def list_academic_years(
    request: Request,
    include_inactive: bool = Query(True, description="Include inactive academic years"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    order_by: str = Query("start_date", description="Field to order by"),
    order_direction: str = Query("desc", regex="^(asc|desc)$", description="Order direction"),
    db: Session = Depends(get_db)
) -> AcademicYearListResponse:
    """
    List academic years for the school
    
    Returns paginated list of academic years with filtering and sorting options.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.academic_year.read" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to read academic years"
            )
        
        # Get academic years
        result = academic_year_service.list_academic_years(
            school_id=current_user.school_id,
            include_inactive=include_inactive,
            limit=limit,
            offset=offset,
            order_by=order_by,
            order_direction=order_direction
        )
        
        if not result["success"]:
            raise HTTPException(status_code=500, detail=result.get("error", "Failed to retrieve academic years"))
        
        return AcademicYearListResponse(
            success=True,
            academic_years=[AcademicYearResponse(**year) for year in result["academic_years"]],
            total_count=result["total_count"],
            limit=result["limit"],
            offset=result["offset"],
            has_more=result["has_more"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error listing academic years: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/years/{academic_year_id}",
    response_model=AcademicYearResponse,
    summary="Get Academic Year",
    description="""
    Retrieve a specific academic year by ID.
    
    **Features:**
    - Detailed academic year information
    - Computed fields (progress, remaining days, etc.)
    - Multi-tenant isolation
    
    **Permissions Required:**
    - `academic.academic_year.read`
    """
)
async def get_academic_year(
    request: Request,
    academic_year_id: str = Path(..., description="Academic year UUID"),
    db: Session = Depends(get_db)
) -> AcademicYearResponse:
    """
    Get academic year by ID
    
    Returns detailed information about a specific academic year including computed fields.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.academic_year.read" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to read academic year"
            )
        
        # Validate UUID
        try:
            year_uuid = uuid.UUID(academic_year_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid academic year ID format")
        
        # Get academic year
        academic_year = academic_year_service.get_academic_year(
            school_id=current_user.school_id,
            academic_year_id=year_uuid
        )
        
        if not academic_year:
            raise HTTPException(status_code=404, detail="Academic year not found")
        
        return AcademicYearResponse(**academic_year)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving academic year: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/years/active",
    response_model=AcademicYearResponse,
    summary="Get Active Academic Year",
    description="""
    Retrieve the currently active academic year for the school.
    
    **Features:**
    - Returns the single active academic year
    - Computed fields included
    - Multi-tenant isolation
    
    **Permissions Required:**
    - `academic.academic_year.read`
    """
)
async def get_active_academic_year(
    request: Request,
    db: Session = Depends(get_db)
) -> AcademicYearResponse:
    """
    Get the currently active academic year
    
    Returns the active academic year for the school, or 404 if no active year exists.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.academic_year.read" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to read academic year"
            )
        
        # Get active academic year
        active_year = academic_year_service.get_active_academic_year(current_user.school_id)
        
        if not active_year:
            raise HTTPException(status_code=404, detail="No active academic year found")
        
        return AcademicYearResponse(**active_year)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving active academic year: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put(
    "/years/{academic_year_id}",
    response_model=AcademicYearActionResponse,
    summary="Update Academic Year",
    description="""
    Update an existing academic year.
    
    **Features:**
    - Partial updates supported
    - Validation and business rule enforcement
    - Comprehensive audit logging
    - Multi-tenant isolation
    
    **Permissions Required:**
    - `academic.academic_year.update`
    
    **Business Rules:**
    - Cannot modify active academic year dates if students are enrolled
    - Year label must remain unique per school
    """
)
async def update_academic_year(
    request: Request,
    academic_year_id: str = Path(..., description="Academic year UUID"),
    request_data: AcademicYearUpdateRequest = ...,
    db: Session = Depends(get_db)
) -> AcademicYearActionResponse:
    """
    Update academic year details
    
    Updates the specified academic year with the provided data.
    Only non-null fields in the request will be updated.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.academic_year.update" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to update academic year"
            )

        # Validate UUID
        try:
            year_uuid = uuid.UUID(academic_year_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid academic year ID format")

        # Prepare update data (exclude None values)
        update_data = {k: v for k, v in request_data.model_dump().items() if v is not None}
        
        if not update_data:
            raise HTTPException(status_code=400, detail="No fields to update")
        
        # Update academic year
        result = academic_year_service.update_academic_year(
            school_id=current_user.school_id,
            academic_year_id=year_uuid,
            user_id=current_user.id,
            **update_data
        )
        
        return AcademicYearActionResponse(
            success=result["success"],
            message=result["message"],
            academic_year=AcademicYearResponse(**result["academic_year"]),
            changes=result.get("changes")
        )
        
    except AcademicYearError as e:
        logger.warning(f"Academic year update failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating academic year: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/years/{academic_year_id}/activate",
    response_model=AcademicYearActionResponse,
    summary="Activate Academic Year",
    description="""
    Activate an academic year (deactivates current active year).

    **Features:**
    - Automatic deactivation of current active year
    - Validation checks before activation
    - Force activation option for edge cases
    - Comprehensive audit logging

    **Permissions Required:**
    - `academic.academic_year.activate`

    **Business Rules:**
    - Only one active academic year per school
    - Cannot activate expired years (unless forced)
    - Previous active year is automatically completed/drafted
    """
)
async def activate_academic_year(
    request: Request,
    academic_year_id: str = Path(..., description="Academic year UUID to activate"),
    request_data: AcademicYearActionRequest = ...,
    db: Session = Depends(get_db)
) -> AcademicYearActionResponse:
    """
    Activate an academic year

    Activates the specified academic year and deactivates the current active year.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.academic_year.activate" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to activate academic year"
            )

        # Validate UUID
        try:
            year_uuid = uuid.UUID(academic_year_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid academic year ID format")

        # Activate academic year
        result = academic_year_service.activate_academic_year(
            school_id=current_user.school_id,
            academic_year_id=year_uuid,
            user_id=current_user.id,
            force=request_data.force
        )

        return AcademicYearActionResponse(
            success=result["success"],
            message=result["message"],
            academic_year=AcademicYearResponse(**result["academic_year"]),
            previous_active=result.get("previous_active")
        )

    except AcademicYearError as e:
        logger.warning(f"Academic year activation failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error activating academic year: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/years/{academic_year_id}/complete",
    response_model=AcademicYearActionResponse,
    summary="Complete Academic Year",
    description="""
    Mark an academic year as completed.

    **Features:**
    - Validates completion eligibility
    - Deactivates the academic year
    - Comprehensive audit logging

    **Permissions Required:**
    - `academic.academic_year.complete`

    **Business Rules:**
    - Only active academic years can be completed
    - Completion triggers status change to 'completed'
    """
)
async def complete_academic_year(
    request: Request,
    academic_year_id: str = Path(..., description="Academic year UUID to complete"),
    request_data: AcademicYearActionRequest = ...,
    db: Session = Depends(get_db)
) -> AcademicYearActionResponse:
    """
    Complete an academic year

    Marks the specified academic year as completed and deactivates it.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.academic_year.complete" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to complete academic year"
            )

        # Validate UUID
        try:
            year_uuid = uuid.UUID(academic_year_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid academic year ID format")

        # Complete academic year
        result = academic_year_service.complete_academic_year(
            school_id=current_user.school_id,
            academic_year_id=year_uuid,
            user_id=current_user.id
        )

        return AcademicYearActionResponse(
            success=result["success"],
            message=result["message"],
            academic_year=AcademicYearResponse(**result["academic_year"])
        )

    except AcademicYearError as e:
        logger.warning(f"Academic year completion failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error completing academic year: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete(
    "/years/{academic_year_id}",
    response_model=AcademicYearActionResponse,
    summary="Delete Academic Year",
    description="""
    Soft delete an academic year.

    **Features:**
    - Soft deletion with audit trail
    - Dependency checking
    - Reason tracking

    **Permissions Required:**
    - `academic.academic_year.delete`

    **Business Rules:**
    - Cannot delete active academic years
    - Cannot delete years with enrolled students
    - Deletion is reversible (soft delete)
    """
)
async def delete_academic_year(
    request: Request,
    academic_year_id: str = Path(..., description="Academic year UUID to delete"),
    request_data: AcademicYearActionRequest = ...,
    db: Session = Depends(get_db)
) -> AcademicYearActionResponse:
    """
    Delete an academic year

    Soft deletes the specified academic year with dependency checking.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.academic_year.delete" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to delete academic year"
            )

        # Validate UUID
        try:
            year_uuid = uuid.UUID(academic_year_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid academic year ID format")

        # Delete academic year
        result = academic_year_service.delete_academic_year(
            school_id=current_user.school_id,
            academic_year_id=year_uuid,
            user_id=current_user.id,
            reason=request_data.reason
        )

        return AcademicYearActionResponse(
            success=result["success"],
            message=result["message"]
        )

    except AcademicYearError as e:
        logger.warning(f"Academic year deletion failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error deleting academic year: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post(
    "/years/generate",
    response_model=AcademicYearGenerateResponse,
    summary="Generate Academic Year",
    description="""
    Generate academic year suggestions based on patterns.

    **Features:**
    - Indian academic year pattern (April-March)
    - International pattern (September-June)
    - Custom pattern support
    - Automatic date calculation

    **Permissions Required:**
    - `academic.academic_year.read`

    **Patterns:**
    - `indian`: April 1st to March 31st
    - `international`: September 1st to June 30th
    - `custom`: User-defined pattern
    """
)
async def generate_academic_year(
    http_request: Request,
    request_data: AcademicYearGenerateRequest,
    db: Session = Depends(get_db)
) -> AcademicYearGenerateResponse:
    """
    Generate academic year suggestions

    Generates academic year label and dates based on the specified pattern.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(http_request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.academic_year.read" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to generate academic year"
            )

        # Generate academic year based on pattern
        if request_data.pattern == "indian":
            year_label, start_date, end_date = academic_year_service.get_current_indian_academic_year()
            if request_data.start_year:
                year_label = academic_year_service.generate_academic_year_label(request_data.start_year)
                start_date = date(request_data.start_year, 4, 1)
                end_date = date(request_data.start_year + 1, 3, 31)
        else:
            # For now, default to Indian pattern
            # TODO: Implement other patterns when needed
            year_label = academic_year_service.generate_academic_year_label(request.start_year)
            start_date = date(request.start_year, 4, 1)
            end_date = date(request.start_year + 1, 3, 31)

        return AcademicYearGenerateResponse(
            year_label=year_label,
            start_date=start_date,
            end_date=end_date,
            pattern=request.pattern,
            suggested_display_name=f"Academic Year {year_label}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error generating academic year: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Subject Endpoints

@router.post(
    "/subjects",
    response_model=SubjectActionResponse,
    status_code=201,
    summary="Create Subject",
    description="""
    Create a new subject with comprehensive validation.

    **Features:**
    - Unique subject code validation per school
    - Subject type categorization (core, elective, extra_curricular)
    - Academic level assignment (primary, secondary, higher_secondary)
    - Default credit and hour configuration
    - Multi-tenant isolation

    **Permissions Required:**
    - `academic.subject.create`
    """
)
async def create_subject(
    http_request: Request,
    request_data: SubjectCreateRequest,
    db: Session = Depends(get_db)
) -> SubjectActionResponse:
    """
    Create a new subject

    Creates a new subject with validation and business rule enforcement.
    Subject codes must be unique per school.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(http_request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.subject.create" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to create subject"
            )

        # Create subject
        result = subject_service.create_subject(
            school_id=current_user.school_id,
            subject_code=request_data.subject_code,
            name=request_data.name,
            user_id=current_user.id,
            description=request_data.description,
            subject_type=request_data.subject_type,
            academic_level=request_data.academic_level,
            default_credits=request_data.default_credits,
            default_hours_per_week=request_data.default_hours_per_week,
            settings=request_data.settings,
            is_active=request_data.is_active
        )

        if not result["success"]:
            if result.get("error_type") == "duplicate_code":
                raise HTTPException(status_code=409, detail=result["message"])
            elif result.get("error_type") == "duplicate_name":
                raise HTTPException(status_code=409, detail=result["message"])
            else:
                raise HTTPException(status_code=400, detail=result["message"])

        return SubjectActionResponse(
            success=True,
            message="Subject created successfully",
            subject=SubjectResponse(**result["subject"])
        )

    except HTTPException:
        raise
    except SubjectError as e:
        logger.error(f"Subject service error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error creating subject: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/subjects",
    response_model=SubjectListResponse,
    summary="List Subjects",
    description="""
    List subjects with filtering and pagination.

    **Features:**
    - Filter by subject type and academic level
    - Include/exclude inactive subjects
    - Pagination support
    - Sorting by display order or name
    - Multi-tenant isolation

    **Permissions Required:**
    - `academic.subject.read`
    """
)
async def list_subjects(
    http_request: Request,
    include_inactive: bool = Query(True, description="Include inactive subjects"),
    subject_type: Optional[str] = Query(None, description="Filter by subject type"),
    academic_level: Optional[str] = Query(None, description="Filter by academic level"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    order_by: str = Query("display_order", description="Field to order by"),
    order_direction: str = Query("asc", regex="^(asc|desc)$", description="Order direction"),
    db: Session = Depends(get_db)
) -> SubjectListResponse:
    """
    List subjects for the school

    Returns paginated list of subjects with filtering options.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(http_request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.subject.read" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to read subjects"
            )

        # List subjects
        result = subject_service.list_subjects(
            school_id=current_user.school_id,
            include_inactive=include_inactive,
            subject_type=subject_type,
            academic_level=academic_level,
            limit=limit,
            offset=offset,
            order_by=order_by,
            order_direction=order_direction
        )

        if not result["success"]:
            raise HTTPException(status_code=400, detail="Failed to list subjects")

        return SubjectListResponse(
            success=True,
            subjects=[SubjectResponse(**subject) for subject in result["subjects"]],
            pagination=result["pagination"]
        )

    except HTTPException:
        raise
    except SubjectError as e:
        logger.error(f"Subject service error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error listing subjects: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/subjects/{subject_id}",
    response_model=SubjectResponse,
    summary="Get Subject",
    description="""
    Get subject by ID with multi-tenant isolation.

    **Permissions Required:**
    - `academic.subject.read`
    """
)
async def get_subject(
    http_request: Request,
    subject_id: str = Path(..., description="Subject UUID"),
    db: Session = Depends(get_db)
) -> SubjectResponse:
    """
    Get subject by ID

    Returns detailed subject information with computed fields.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(http_request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.subject.read" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to read subject"
            )

        # Validate UUID
        try:
            subject_uuid = uuid.UUID(subject_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid subject ID format")

        # Get subject
        subject = subject_service.get_subject(
            school_id=current_user.school_id,
            subject_id=subject_uuid
        )

        if not subject:
            raise HTTPException(status_code=404, detail="Subject not found")

        return SubjectResponse(**subject)

    except HTTPException:
        raise
    except SubjectError as e:
        logger.error(f"Subject service error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error getting subject: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put(
    "/subjects/{subject_id}",
    response_model=SubjectActionResponse,
    summary="Update Subject",
    description="""
    Update subject details with validation.

    **Features:**
    - Partial updates supported
    - Unique constraint validation
    - Multi-tenant isolation

    **Permissions Required:**
    - `academic.subject.update`
    """
)
async def update_subject(
    http_request: Request,
    subject_id: str = Path(..., description="Subject UUID"),
    request_data: SubjectUpdateRequest = ...,
    db: Session = Depends(get_db)
) -> SubjectActionResponse:
    """
    Update subject details

    Updates subject with validation and business rule enforcement.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(http_request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.subject.update" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to update subject"
            )

        # Validate UUID
        try:
            subject_uuid = uuid.UUID(subject_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid subject ID format")

        # Prepare update data (exclude None values)
        update_data = {k: v for k, v in request_data.model_dump().items() if v is not None}

        if not update_data:
            raise HTTPException(status_code=400, detail="No fields provided for update")

        # Update subject
        result = subject_service.update_subject(
            school_id=current_user.school_id,
            subject_id=subject_uuid,
            user_id=current_user.id,
            **update_data
        )

        if not result["success"]:
            if result.get("error_type") == "not_found":
                raise HTTPException(status_code=404, detail=result["message"])
            elif result.get("error_type") in ["duplicate_code", "duplicate_name"]:
                raise HTTPException(status_code=409, detail=result["message"])
            else:
                raise HTTPException(status_code=400, detail=result["message"])

        return SubjectActionResponse(
            success=True,
            message="Subject updated successfully",
            subject=SubjectResponse(**result["subject"])
        )

    except HTTPException:
        raise
    except SubjectError as e:
        logger.error(f"Subject service error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error updating subject: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete(
    "/subjects/{subject_id}",
    response_model=SubjectActionResponse,
    summary="Delete Subject",
    description="""
    Soft delete subject with dependency checking.

    **Features:**
    - Dependency validation (checks for active class assignments)
    - Soft delete with audit trail
    - Multi-tenant isolation

    **Permissions Required:**
    - `academic.subject.delete`
    """
)
async def delete_subject(
    http_request: Request,
    subject_id: str = Path(..., description="Subject UUID to delete"),
    request_data: SubjectActionRequest = ...,
    db: Session = Depends(get_db)
) -> SubjectActionResponse:
    """
    Delete a subject

    Performs soft delete with dependency checking.
    Cannot delete subjects that are assigned to active classes.
    """
    try:
        # Get current user from request state
        current_user = get_current_user(http_request)

        # Check permissions
        user_permissions = current_user.get_permissions()
        if "academic.subject.delete" not in user_permissions:
            raise HTTPException(
                status_code=403,
                detail="Insufficient permissions to delete subject"
            )

        # Validate UUID
        try:
            subject_uuid = uuid.UUID(subject_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid subject ID format")

        # Delete subject
        result = subject_service.delete_subject(
            school_id=current_user.school_id,
            subject_id=subject_uuid,
            user_id=current_user.id,
            reason=request_data.reason
        )

        if not result["success"]:
            if result.get("error_type") == "not_found":
                raise HTTPException(status_code=404, detail=result["message"])
            elif result.get("error_type") == "has_dependencies":
                raise HTTPException(status_code=409, detail=result["message"])
            else:
                raise HTTPException(status_code=400, detail=result["message"])

        return SubjectActionResponse(
            success=True,
            message="Subject deleted successfully"
        )

    except HTTPException:
        raise
    except SubjectError as e:
        logger.error(f"Subject service error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error deleting subject: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
