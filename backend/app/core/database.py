"""
Database configuration and session management for School ERP
Includes multi-tenant query scoping and connection pooling
"""

from typing import Generator, Optional
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
import uuid

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


# Database engine with connection pooling
engine = create_engine(
    settings.DATABASE_URL,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,  # Recycle connections every hour
    echo=settings.DEBUG,  # Log SQL queries in debug mode
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


class MultiTenantSession:
    """Session wrapper that automatically scopes queries by school_id"""
    
    def __init__(self, session: Session, school_id: Optional[uuid.UUID] = None):
        self.session = session
        self.school_id = school_id
        self._original_query = session.query
        
        # Override query method to add school_id filter
        if school_id:
            session.query = self._scoped_query
    
    def _scoped_query(self, *args, **kwargs):
        """Override query to automatically add school_id filter"""
        query = self._original_query(*args, **kwargs)
        
        # Check if the model has school_id attribute
        if hasattr(query.column_descriptions[0]['type'], 'school_id'):
            query = query.filter_by(school_id=self.school_id)
        
        return query
    
    def __getattr__(self, name):
        """Delegate all other attributes to the original session"""
        return getattr(self.session, name)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.session.rollback()
        else:
            self.session.commit()
        self.session.close()


def get_db() -> Generator[Session, None, None]:
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_multi_tenant_db(school_id: uuid.UUID) -> Generator[MultiTenantSession, None, None]:
    """Dependency to get multi-tenant database session"""
    db = SessionLocal()
    try:
        yield MultiTenantSession(db, school_id)
    finally:
        db.close()


@contextmanager
def get_db_context():
    """Context manager for database session"""
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()


@contextmanager
def get_multi_tenant_db_context(school_id: uuid.UUID):
    """Context manager for multi-tenant database session"""
    db = SessionLocal()
    try:
        scoped_session = MultiTenantSession(db, school_id)
        yield scoped_session
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()


# Database event listeners for audit logging
@event.listens_for(engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Log SQL queries in debug mode"""
    if settings.DEBUG:
        logger.debug(f"SQL Query: {statement}")
        if parameters:
            logger.debug(f"Parameters: {parameters}")


@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """Set database-specific configurations"""
    # This is for PostgreSQL, but we can add specific configurations here
    pass


def init_db():
    """Initialize database tables"""
    from app.models.base import Base
    Base.metadata.create_all(bind=engine)
    logger.info("Database tables created successfully")


def check_db_connection() -> bool:
    """Check if database connection is working"""
    try:
        from sqlalchemy import text
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        logger.info("Database connection successful")
        return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False


# Database health check
def get_db_health() -> dict:
    """Get database health status"""
    try:
        from sqlalchemy import text
        with engine.connect() as connection:
            result = connection.execute(text("SELECT version()"))
            version = result.fetchone()[0]

        return {
            "status": "healthy",
            "database": "postgresql",
            "version": version,
            "pool_size": engine.pool.size(),
            "checked_out_connections": engine.pool.checkedout(),
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }
