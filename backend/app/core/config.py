"""
Core configuration settings for School ERP Backend
Uses Pydantic Settings for environment-based configuration
"""

from typing import List, Optional
from pydantic import validator, model_validator
from pydantic_settings import BaseSettings
import secrets


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Project Information
    PROJECT_NAME: str = "School ERP Backend"
    VERSION: str = "0.1.0"
    DESCRIPTION: str = "AI-Driven Modular School ERP Backend API"
    
    # Environment
    ENVIRONMENT: str = "development"  # development, staging, production
    DEBUG: bool = True
    
    # Security
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    ALGORITHM: str = "HS256"
    
    # Database
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "school_erp_user"
    POSTGRES_PASSWORD: str = "school_erp_password"
    POSTGRES_DB: str = "school_erp_dev"
    POSTGRES_PORT: int = 5432
    DATABASE_URL: Optional[str] = None
    
    # Redis
    REDIS_URL: Optional[str] = None
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    # CORS
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",  # Next.js dev server
        "http://localhost:3001",  # Alternative frontend port
        "https://*.vercel.app",   # Vercel deployments
    ]
    
    # Domain Configuration
    PRIMARY_DOMAIN: str = "myschoolerp.local"  # For development
    SUBDOMAIN_PATTERN: str = "{subdomain}.myschoolerp.local"
    
    # File Storage
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = [
        "image/jpeg", "image/png", "image/gif",
        "application/pdf", "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ]
    
    # Rate Limiting Configuration (configurable via admin interface)
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_PER_HOUR: int = 1000
    RATE_LIMIT_USER_PER_HOUR: int = 500
    RATE_LIMIT_SCHOOL_PER_HOUR: int = 10000
    RATE_LIMIT_AUTH_PER_HOUR: int = 10
    RATE_LIMIT_BURST_PER_MINUTE: int = 30
    
    # Email (Placeholder)
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: Optional[str] = None
    
    # Licensing
    DEFAULT_TRIAL_DAYS: int = 14

    # Internationalization & Localization
    DEFAULT_TIMEZONE: str = "Asia/Kolkata"  # Indian timezone as default
    DEFAULT_LANGUAGE: str = "en"  # English as default
    DEFAULT_CURRENCY: str = "INR"  # Indian Rupee as default
    DEFAULT_DATE_FORMAT: str = "DD/MM/YYYY"  # Indian date format
    DEFAULT_TIME_FORMAT: str = "HH:mm"  # 24-hour format
    SUPPORTED_LANGUAGES: List[str] = ["en", "hi", "ta", "te", "bn", "gu", "mr", "kn"]  # Indian languages
    SUPPORTED_TIMEZONES: List[str] = [
        "Asia/Kolkata", "Asia/Mumbai", "Asia/Delhi", "Asia/Calcutta",
        "UTC", "America/New_York", "Europe/London"  # For future international expansion
    ]
    SUPPORTED_CURRENCIES: List[str] = ["INR", "USD", "EUR", "GBP"]  # For future expansion
    
    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict) -> str:
        if isinstance(v, str) and v:
            return v

        # Get values with defaults
        user = values.get('POSTGRES_USER', 'school_erp_user')
        password = values.get('POSTGRES_PASSWORD', 'school_erp_password')
        server = values.get('POSTGRES_SERVER', 'localhost')
        port = values.get('POSTGRES_PORT', 5432)
        db = values.get('POSTGRES_DB', 'school_erp_dev')

        return f"postgresql://{user}:{password}@{server}:{port}/{db}"
    
    @model_validator(mode='after')
    def assemble_redis_connection(self) -> 'Settings':
        if isinstance(self.REDIS_URL, str) and self.REDIS_URL:
            return self

        # Get values with defaults
        host = self.REDIS_HOST
        port = self.REDIS_PORT
        db = self.REDIS_DB
        password = self.REDIS_PASSWORD

        password_part = f":{password}@" if password else ""
        self.REDIS_URL = f"redis://{password_part}{host}:{port}/{db}"
        return self
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
