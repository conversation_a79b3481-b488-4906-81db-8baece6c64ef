"""
Security utilities for School ERP
JWT token management, password hashing, and security middleware
"""

from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional, Union
import secrets
import hashlib
import hmac
from jose import JWTError, jwt
from passlib.context import CryptContext
import redis
import uuid

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

# Password hashing context with multiple algorithms for security
pwd_context = CryptContext(
    schemes=["bcrypt", "pbkdf2_sha256"],
    default="bcrypt",
    bcrypt__rounds=12,  # Higher rounds for better security
    pbkdf2_sha256__rounds=100000,
    deprecated="auto"
)

# Redis connection for token management
redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)


class SecurityManager:
    """Centralized security management for authentication and authorization"""
    
    # Token types
    ACCESS_TOKEN = "access"
    REFRESH_TOKEN = "refresh"
    RESET_TOKEN = "reset"
    VERIFICATION_TOKEN = "verification"
    
    # Redis key patterns
    BLACKLIST_KEY = "auth:blacklist:{token_id}"
    REFRESH_KEY = "auth:refresh:{user_id}:{token_id}"
    RATE_LIMIT_KEY = "auth:rate_limit:{identifier}"
    SESSION_KEY = "auth:session:{user_id}:{session_id}"
    
    def __init__(self):
        self.algorithm = settings.ALGORITHM
        self.secret_key = settings.SECRET_KEY
        
    def create_access_token(
        self, 
        subject: Union[str, uuid.UUID], 
        school_id: Optional[uuid.UUID] = None,
        permissions: Optional[list] = None,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT access token with security claims"""
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )
        
        # Generate unique token ID for tracking
        token_id = str(uuid.uuid4())
        
        # Create comprehensive token payload
        to_encode = {
            "sub": str(subject),  # Subject (user ID)
            "exp": expire,        # Expiration time
            "iat": datetime.now(timezone.utc),  # Issued at
            "jti": token_id,      # JWT ID for blacklisting
            "type": self.ACCESS_TOKEN,
            "school_id": str(school_id) if school_id else None,
            "permissions": permissions or [],
            "iss": "school-erp",  # Issuer
            "aud": "school-erp-api"  # Audience
        }
        
        # Sign the token
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        
        # Log token creation (without sensitive data)
        logger.info(
            "Access token created",
            extra={
                "user_id": str(subject),
                "school_id": str(school_id) if school_id else None,
                "token_id": token_id,
                "expires_at": expire.isoformat()
            }
        )
        
        return encoded_jwt
    
    def create_refresh_token(
        self, 
        user_id: uuid.UUID, 
        school_id: Optional[uuid.UUID] = None
    ) -> str:
        """Create refresh token and store in Redis"""
        expire = datetime.now(timezone.utc) + timedelta(
            days=settings.REFRESH_TOKEN_EXPIRE_DAYS
        )
        
        token_id = str(uuid.uuid4())
        
        to_encode = {
            "sub": str(user_id),
            "exp": expire,
            "iat": datetime.now(timezone.utc),
            "jti": token_id,
            "type": self.REFRESH_TOKEN,
            "school_id": str(school_id) if school_id else None,
            "iss": "school-erp",
            "aud": "school-erp-api"
        }
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        
        # Store refresh token in Redis with expiration
        redis_key = self.REFRESH_KEY.format(user_id=user_id, token_id=token_id)
        redis_client.setex(
            redis_key,
            int(timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS).total_seconds()),
            encoded_jwt
        )
        
        logger.info(
            "Refresh token created",
            extra={
                "user_id": str(user_id),
                "school_id": str(school_id) if school_id else None,
                "token_id": token_id
            }
        )
        
        return encoded_jwt
    
    def verify_token(self, token: str, token_type: str = None) -> Optional[Dict[str, Any]]:
        """Verify JWT token and return payload"""
        try:
            # Decode token
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=[self.algorithm],
                audience="school-erp-api",
                issuer="school-erp"
            )
            
            # Verify token type if specified
            if token_type and payload.get("type") != token_type:
                logger.warning(f"Invalid token type. Expected: {token_type}, Got: {payload.get('type')}")
                return None
            
            # Check if token is blacklisted
            token_id = payload.get("jti")
            if token_id and self.is_token_blacklisted(token_id):
                logger.warning(f"Blacklisted token used: {token_id}")
                return None
            
            # Verify expiration
            exp = payload.get("exp")
            if exp and datetime.fromtimestamp(exp, timezone.utc) < datetime.now(timezone.utc):
                logger.warning("Expired token used")
                return None
            
            return payload
            
        except JWTError as e:
            logger.warning(f"JWT verification failed: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Token verification error: {str(e)}")
            return None
    
    def blacklist_token(self, token_id: str, expires_in: int = None) -> bool:
        """Add token to blacklist"""
        try:
            if not expires_in:
                expires_in = settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
            
            redis_key = self.BLACKLIST_KEY.format(token_id=token_id)
            redis_client.setex(redis_key, expires_in, "blacklisted")
            
            logger.info(f"Token blacklisted: {token_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to blacklist token: {str(e)}")
            return False
    
    def is_token_blacklisted(self, token_id: str) -> bool:
        """Check if token is blacklisted"""
        try:
            redis_key = self.BLACKLIST_KEY.format(token_id=token_id)
            return redis_client.exists(redis_key) > 0
        except Exception as e:
            logger.error(f"Failed to check blacklist: {str(e)}")
            return False
    
    def revoke_refresh_token(self, user_id: uuid.UUID, token_id: str) -> bool:
        """Revoke specific refresh token"""
        try:
            redis_key = self.REFRESH_KEY.format(user_id=user_id, token_id=token_id)
            result = redis_client.delete(redis_key)
            
            if result:
                logger.info(f"Refresh token revoked: {token_id}")
            
            return result > 0
            
        except Exception as e:
            logger.error(f"Failed to revoke refresh token: {str(e)}")
            return False
    
    def revoke_all_user_tokens(self, user_id: uuid.UUID) -> bool:
        """Revoke all refresh tokens for a user"""
        try:
            pattern = self.REFRESH_KEY.format(user_id=user_id, token_id="*")
            keys = redis_client.keys(pattern)
            
            if keys:
                redis_client.delete(*keys)
                logger.info(f"All refresh tokens revoked for user: {user_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to revoke all user tokens: {str(e)}")
            return False


class PasswordManager:
    """Secure password management utilities"""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password using bcrypt"""
        return pwd_context.hash(password)
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def generate_secure_password(length: int = 12) -> str:
        """Generate cryptographically secure password"""
        alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """Validate password strength and return detailed feedback"""
        issues = []
        score = 0
        
        # Length check
        if len(password) < 8:
            issues.append("Password must be at least 8 characters long")
        else:
            score += 1
        
        # Character variety checks
        if not any(c.islower() for c in password):
            issues.append("Password must contain at least one lowercase letter")
        else:
            score += 1
            
        if not any(c.isupper() for c in password):
            issues.append("Password must contain at least one uppercase letter")
        else:
            score += 1
            
        if not any(c.isdigit() for c in password):
            issues.append("Password must contain at least one number")
        else:
            score += 1
            
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            issues.append("Password must contain at least one special character")
        else:
            score += 1
        
        # Common password check (basic)
        common_passwords = ["password", "123456", "qwerty", "admin", "welcome"]
        if password.lower() in common_passwords:
            issues.append("Password is too common")
            score = max(0, score - 2)
        
        # Determine strength
        if score >= 5:
            strength = "strong"
        elif score >= 3:
            strength = "medium"
        else:
            strength = "weak"
        
        return {
            "is_valid": len(issues) == 0,
            "strength": strength,
            "score": score,
            "issues": issues
        }


# Global security manager instance
security_manager = SecurityManager()
password_manager = PasswordManager()


def create_session_id() -> str:
    """Create secure session ID"""
    return secrets.token_urlsafe(32)


def generate_csrf_token() -> str:
    """Generate CSRF token"""
    return secrets.token_urlsafe(32)


def verify_csrf_token(token: str, expected: str) -> bool:
    """Verify CSRF token using constant-time comparison"""
    return hmac.compare_digest(token, expected)


def generate_api_key() -> str:
    """Generate API key for external integrations"""
    return f"sk_{''.join(secrets.choice('abcdefghijklmnopqrstuvwxyz0123456789') for _ in range(32))}"


def hash_api_key(api_key: str) -> str:
    """Hash API key for storage"""
    return hashlib.sha256(api_key.encode()).hexdigest()
