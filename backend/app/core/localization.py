"""
Internationalization and Localization utilities for School ERP
Handles timezone, currency, date formatting, and multi-language support
"""

from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from decimal import Decimal
import pytz
from babel import Locale, dates, numbers
from babel.core import UnknownLocaleError

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class LocalizationManager:
    """Centralized localization management for multi-tenant application"""
    
    # Language to locale mapping for Indian languages
    LANGUAGE_LOCALE_MAP = {
        "en": "en_IN",  # English (India)
        "hi": "hi_IN",  # Hindi (India)
        "ta": "ta_IN",  # Tamil (India)
        "te": "te_IN",  # Telugu (India)
        "bn": "bn_IN",  # Bengali (India)
        "gu": "gu_IN",  # Gujarati (India)
        "mr": "mr_IN",  # Marathi (India)
        "kn": "kn_IN",  # Kannada (India)
    }
    
    # Currency symbols and formatting
    CURRENCY_INFO = {
        "INR": {"symbol": "₹", "decimal_places": 2, "locale": "en_IN"},
        "USD": {"symbol": "$", "decimal_places": 2, "locale": "en_US"},
        "EUR": {"symbol": "€", "decimal_places": 2, "locale": "en_EU"},
        "GBP": {"symbol": "£", "decimal_places": 2, "locale": "en_GB"},
    }
    
    # Date format patterns
    DATE_FORMATS = {
        "DD/MM/YYYY": "%d/%m/%Y",
        "MM/DD/YYYY": "%m/%d/%Y",
        "YYYY-MM-DD": "%Y-%m-%d",
        "DD-MM-YYYY": "%d-%m-%Y",
    }
    
    # Time format patterns
    TIME_FORMATS = {
        "HH:mm": "%H:%M",
        "HH:mm:ss": "%H:%M:%S",
        "hh:mm AM/PM": "%I:%M %p",
        "hh:mm:ss AM/PM": "%I:%M:%S %p",
    }
    
    def __init__(self, 
                 language: str = None, 
                 timezone_name: str = None, 
                 currency: str = None,
                 date_format: str = None,
                 time_format: str = None):
        """Initialize localization manager with school/user preferences"""
        self.language = language or settings.DEFAULT_LANGUAGE
        self.timezone_name = timezone_name or settings.DEFAULT_TIMEZONE
        self.currency = currency or settings.DEFAULT_CURRENCY
        self.date_format = date_format or settings.DEFAULT_DATE_FORMAT
        self.time_format = time_format or settings.DEFAULT_TIME_FORMAT
        
        # Initialize timezone and locale objects
        self._timezone = None
        self._locale = None
        self._initialize()
    
    def _initialize(self):
        """Initialize timezone and locale objects with error handling"""
        try:
            self._timezone = pytz.timezone(self.timezone_name)
        except pytz.UnknownTimeZoneError:
            logger.warning(f"Unknown timezone: {self.timezone_name}, using default")
            self._timezone = pytz.timezone(settings.DEFAULT_TIMEZONE)
            self.timezone_name = settings.DEFAULT_TIMEZONE
        
        try:
            locale_code = self.LANGUAGE_LOCALE_MAP.get(self.language, "en_IN")
            self._locale = Locale.parse(locale_code)
        except UnknownLocaleError:
            logger.warning(f"Unknown locale for language: {self.language}, using default")
            self._locale = Locale.parse("en_IN")
            self.language = "en"
    
    def format_datetime(self, dt: datetime, include_time: bool = True) -> str:
        """Format datetime according to user preferences"""
        if dt.tzinfo is None:
            # Assume UTC if no timezone info
            dt = dt.replace(tzinfo=timezone.utc)
        
        # Convert to user's timezone
        local_dt = dt.astimezone(self._timezone)
        
        # Format date
        date_pattern = self.DATE_FORMATS.get(self.date_format, "%d/%m/%Y")
        formatted_date = local_dt.strftime(date_pattern)
        
        if include_time:
            # Format time
            time_pattern = self.TIME_FORMATS.get(self.time_format, "%H:%M")
            formatted_time = local_dt.strftime(time_pattern)
            return f"{formatted_date} {formatted_time}"
        
        return formatted_date
    
    def format_date(self, dt: datetime) -> str:
        """Format date only according to user preferences"""
        return self.format_datetime(dt, include_time=False)
    
    def format_time(self, dt: datetime) -> str:
        """Format time only according to user preferences"""
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        
        local_dt = dt.astimezone(self._timezone)
        time_pattern = self.TIME_FORMATS.get(self.time_format, "%H:%M")
        return local_dt.strftime(time_pattern)
    
    def format_currency(self, amount: Decimal, include_symbol: bool = True) -> str:
        """Format currency according to user preferences"""
        currency_info = self.CURRENCY_INFO.get(self.currency, self.CURRENCY_INFO["INR"])
        
        try:
            # Use Babel for proper currency formatting
            formatted = numbers.format_currency(
                amount, 
                self.currency, 
                locale=self._locale
            )
            return formatted
        except Exception as e:
            logger.warning(f"Currency formatting error: {e}, using fallback")
            # Fallback formatting
            symbol = currency_info["symbol"] if include_symbol else ""
            decimal_places = currency_info["decimal_places"]
            return f"{symbol}{amount:,.{decimal_places}f}"
    
    def format_number(self, number: float, decimal_places: int = 2) -> str:
        """Format number according to locale preferences"""
        try:
            return numbers.format_decimal(number, locale=self._locale)
        except Exception as e:
            logger.warning(f"Number formatting error: {e}, using fallback")
            return f"{number:,.{decimal_places}f}"
    
    def get_timezone_offset(self) -> str:
        """Get timezone offset string (e.g., '+05:30')"""
        now = datetime.now(self._timezone)
        offset = now.strftime('%z')
        # Format as +05:30
        return f"{offset[:3]}:{offset[3:]}"
    
    def convert_to_utc(self, dt: datetime) -> datetime:
        """Convert local datetime to UTC"""
        if dt.tzinfo is None:
            # Assume it's in user's timezone
            dt = self._timezone.localize(dt)
        return dt.astimezone(timezone.utc)
    
    def convert_from_utc(self, dt: datetime) -> datetime:
        """Convert UTC datetime to user's timezone"""
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        return dt.astimezone(self._timezone)
    
    def get_localization_context(self) -> Dict[str, Any]:
        """Get complete localization context for frontend"""
        return {
            "language": self.language,
            "timezone": self.timezone_name,
            "timezone_offset": self.get_timezone_offset(),
            "currency": self.currency,
            "currency_symbol": self.CURRENCY_INFO.get(self.currency, {}).get("symbol", "₹"),
            "date_format": self.date_format,
            "time_format": self.time_format,
            "locale": str(self._locale),
        }
    
    @classmethod
    def get_supported_languages(cls) -> List[Dict[str, str]]:
        """Get list of supported languages with names"""
        return [
            {"code": "en", "name": "English", "native_name": "English"},
            {"code": "hi", "name": "Hindi", "native_name": "हिन्दी"},
            {"code": "ta", "name": "Tamil", "native_name": "தமிழ்"},
            {"code": "te", "name": "Telugu", "native_name": "తెలుగు"},
            {"code": "bn", "name": "Bengali", "native_name": "বাংলা"},
            {"code": "gu", "name": "Gujarati", "native_name": "ગુજરાતી"},
            {"code": "mr", "name": "Marathi", "native_name": "मराठी"},
            {"code": "kn", "name": "Kannada", "native_name": "ಕನ್ನಡ"},
        ]
    
    @classmethod
    def get_supported_timezones(cls) -> List[Dict[str, str]]:
        """Get list of supported timezones with display names"""
        return [
            {"code": "Asia/Kolkata", "name": "India Standard Time (IST)", "offset": "+05:30"},
            {"code": "UTC", "name": "Coordinated Universal Time (UTC)", "offset": "+00:00"},
            {"code": "America/New_York", "name": "Eastern Time (ET)", "offset": "-05:00"},
            {"code": "Europe/London", "name": "Greenwich Mean Time (GMT)", "offset": "+00:00"},
        ]
    
    @classmethod
    def get_supported_currencies(cls) -> List[Dict[str, str]]:
        """Get list of supported currencies with symbols"""
        return [
            {"code": "INR", "name": "Indian Rupee", "symbol": "₹"},
            {"code": "USD", "name": "US Dollar", "symbol": "$"},
            {"code": "EUR", "name": "Euro", "symbol": "€"},
            {"code": "GBP", "name": "British Pound", "symbol": "£"},
        ]


# Global localization manager instance
def get_localization_manager(
    language: str = None,
    timezone_name: str = None,
    currency: str = None,
    date_format: str = None,
    time_format: str = None
) -> LocalizationManager:
    """Factory function to create localization manager"""
    return LocalizationManager(
        language=language,
        timezone_name=timezone_name,
        currency=currency,
        date_format=date_format,
        time_format=time_format
    )
