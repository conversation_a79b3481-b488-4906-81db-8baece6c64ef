"""
Security middleware for School ERP
Authentication, authorization, rate limiting, and security headers
"""

from typing import Optional, Dict, Any, Callable
from fastapi import Request, Response, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import time
import uuid
import redis
from urllib.parse import urlparse

from app.core.config import settings
from app.core.security import security_manager
from app.core.logging import get_logger
from app.models.auth import User
from app.core.database import get_db_context
from app.services.subdomain_service.subdomain import subdomain_service

logger = get_logger(__name__)

# Redis client for rate limiting
redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)

# HTTP Bearer token scheme
security_scheme = HTTPBearer(auto_error=False)


class SecurityMiddleware(BaseHTTPMiddleware):
    """Comprehensive security middleware"""
    
    def __init__(self, app, **kwargs):
        super().__init__(app, **kwargs)
        self.excluded_paths = {
            "/docs", "/redoc", "/openapi.json", "/health", "/",
            "/auth/login", "/auth/register", "/auth/refresh"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request through security layers"""
        
        # Generate correlation ID for request tracking
        correlation_id = str(uuid.uuid4())
        request.state.correlation_id = correlation_id
        
        # Add correlation ID to response headers
        response = await call_next(request)
        response.headers["X-Correlation-ID"] = correlation_id
        
        # Add security headers
        self._add_security_headers(response)
        
        return response
    
    def _add_security_headers(self, response: Response) -> None:
        """Add comprehensive security headers"""
        
        # Prevent clickjacking
        response.headers["X-Frame-Options"] = "DENY"
        
        # Prevent MIME type sniffing
        response.headers["X-Content-Type-Options"] = "nosniff"
        
        # XSS protection
        response.headers["X-XSS-Protection"] = "1; mode=block"
        
        # Referrer policy
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Content Security Policy (relaxed for development to allow Swagger UI)
        if settings.ENVIRONMENT == "development":
            csp = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; "
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
                "img-src 'self' data: https:; "
                "font-src 'self' data: https://cdn.jsdelivr.net; "
                "connect-src 'self'; "
                "frame-ancestors 'none';"
            )
        else:
            # Stricter CSP for production
            csp = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self' data:; "
                "connect-src 'self'; "
                "frame-ancestors 'none';"
            )
        response.headers["Content-Security-Policy"] = csp
        
        # HSTS (only in production with HTTPS)
        if settings.ENVIRONMENT == "production":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        # Remove server information
        if "Server" in response.headers:
            del response.headers["Server"]


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Advanced rate limiting middleware with multiple strategies"""
    
    def __init__(self, app, **kwargs):
        super().__init__(app, **kwargs)
        self.excluded_paths = {"/health", "/"}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Apply rate limiting based on multiple factors"""
        
        # Skip rate limiting for excluded paths
        if request.url.path in self.excluded_paths:
            return await call_next(request)
        
        # Get client identifier
        client_ip = self._get_client_ip(request)
        user_id = getattr(request.state, 'user_id', None)
        school_id = getattr(request.state, 'school_id', None)
        
        # Check rate limits
        rate_limit_result = await self._check_rate_limits(
            client_ip=client_ip,
            user_id=user_id,
            school_id=school_id,
            endpoint=request.url.path,
            method=request.method
        )
        
        if not rate_limit_result["allowed"]:
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "error": "Rate limit exceeded",
                    "message": rate_limit_result["message"],
                    "retry_after": rate_limit_result["retry_after"]
                },
                headers={
                    "Retry-After": str(rate_limit_result["retry_after"]),
                    "X-RateLimit-Limit": str(rate_limit_result["limit"]),
                    "X-RateLimit-Remaining": str(rate_limit_result["remaining"]),
                    "X-RateLimit-Reset": str(rate_limit_result["reset_time"])
                }
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers to response
        response.headers["X-RateLimit-Limit"] = str(rate_limit_result["limit"])
        response.headers["X-RateLimit-Remaining"] = str(rate_limit_result["remaining"])
        response.headers["X-RateLimit-Reset"] = str(rate_limit_result["reset_time"])
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address considering proxies"""
        
        # Check for forwarded headers (in order of preference)
        forwarded_headers = [
            "X-Forwarded-For",
            "X-Real-IP",
            "CF-Connecting-IP",  # Cloudflare
            "X-Client-IP"
        ]
        
        for header in forwarded_headers:
            if header in request.headers:
                # Take the first IP in case of multiple
                ip = request.headers[header].split(",")[0].strip()
                if ip:
                    return ip
        
        # Fallback to direct client IP
        return request.client.host if request.client else "unknown"
    
    async def _check_rate_limits(
        self,
        client_ip: str,
        user_id: Optional[str] = None,
        school_id: Optional[str] = None,
        endpoint: str = None,
        method: str = None
    ) -> Dict[str, Any]:
        """Check multiple rate limit strategies"""
        
        current_time = int(time.time())
        
        # Define rate limit strategies using configurable values
        strategies = [
            # Per IP limits
            {
                "key": f"rate_limit:ip:{client_ip}",
                "limit": settings.RATE_LIMIT_PER_HOUR,
                "window": 3600,  # 1 hour
                "name": "IP-based"
            },
            # Per IP burst protection
            {
                "key": f"rate_limit:ip_burst:{client_ip}",
                "limit": settings.RATE_LIMIT_BURST_PER_MINUTE,
                "window": 60,  # 1 minute
                "name": "IP burst protection"
            }
        ]
        
        # Add user-based limits if authenticated
        if user_id:
            strategies.extend([
                {
                    "key": f"rate_limit:user:{user_id}",
                    "limit": settings.RATE_LIMIT_USER_PER_HOUR,
                    "window": 3600,
                    "name": "User-based"
                },
                {
                    "key": f"rate_limit:user_burst:{user_id}",
                    "limit": settings.RATE_LIMIT_BURST_PER_MINUTE,
                    "window": 60,
                    "name": "User burst protection"
                }
            ])
        
        # Add school-based limits if available
        if school_id:
            strategies.append({
                "key": f"rate_limit:school:{school_id}",
                "limit": settings.RATE_LIMIT_SCHOOL_PER_HOUR,
                "window": 3600,
                "name": "School-based"
            })
        
        # Add endpoint-specific limits for sensitive operations
        if endpoint and method:
            if endpoint.startswith("/auth/"):
                strategies.append({
                    "key": f"rate_limit:auth:{client_ip}",
                    "limit": settings.RATE_LIMIT_AUTH_PER_HOUR,
                    "window": 3600,
                    "name": "Authentication"
                })
        
        # Check each strategy
        for strategy in strategies:
            result = await self._check_sliding_window_rate_limit(
                key=strategy["key"],
                limit=strategy["limit"],
                window=strategy["window"],
                current_time=current_time
            )
            
            if not result["allowed"]:
                logger.warning(
                    f"Rate limit exceeded: {strategy['name']}",
                    extra={
                        "client_ip": client_ip,
                        "user_id": user_id,
                        "school_id": school_id,
                        "endpoint": endpoint,
                        "strategy": strategy["name"]
                    }
                )
                
                return {
                    "allowed": False,
                    "message": f"Rate limit exceeded: {strategy['name']}",
                    "retry_after": result["retry_after"],
                    "limit": strategy["limit"],
                    "remaining": result["remaining"],
                    "reset_time": current_time + strategy["window"]
                }
        
        # All checks passed
        return {
            "allowed": True,
            "limit": strategies[0]["limit"],  # Return primary limit info
            "remaining": strategies[0]["limit"] - 1,  # Approximate
            "reset_time": current_time + strategies[0]["window"]
        }
    
    async def _check_sliding_window_rate_limit(
        self,
        key: str,
        limit: int,
        window: int,
        current_time: int
    ) -> Dict[str, Any]:
        """Implement sliding window rate limiting using Redis"""
        
        try:
            # Use Redis sorted set for sliding window
            pipe = redis_client.pipeline()
            
            # Remove expired entries
            pipe.zremrangebyscore(key, 0, current_time - window)
            
            # Count current requests
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(uuid.uuid4()): current_time})
            
            # Set expiration
            pipe.expire(key, window)
            
            results = pipe.execute()
            current_count = results[1]
            
            if current_count >= limit:
                return {
                    "allowed": False,
                    "remaining": 0,
                    "retry_after": window
                }
            
            return {
                "allowed": True,
                "remaining": limit - current_count - 1,
                "retry_after": 0
            }
            
        except Exception as e:
            logger.error(f"Rate limiting error: {str(e)}")
            # Allow request if rate limiting fails
            return {
                "allowed": True,
                "remaining": limit,
                "retry_after": 0
            }


class AuthenticationMiddleware(BaseHTTPMiddleware):
    """JWT authentication middleware"""
    
    def __init__(self, app, **kwargs):
        super().__init__(app, **kwargs)
        self.public_paths = {
            "/docs", "/redoc", "/openapi.json", "/health", "/",
            "/api/v1/auth/login", "/api/v1/auth/refresh",
            "/api/v1/subdomain/validate", "/api/v1/subdomain/suggestions",
            "/api/v1/subdomain/reserved", "/api/v1/subdomain/info"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Authenticate requests and set user context"""
        
        # Skip authentication for public paths
        path = request.url.path
        if path in self.public_paths or any(path.startswith(public_path) for public_path in ["/api/v1/subdomain/info/"]):
            return await call_next(request)
        
        # Extract token from Authorization header
        authorization = request.headers.get("Authorization")
        if not authorization or not authorization.startswith("Bearer "):
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"error": "Missing or invalid authorization header"}
            )
        
        token = authorization.split(" ")[1]
        
        # Verify token
        payload = security_manager.verify_token(token, security_manager.ACCESS_TOKEN)
        if not payload:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"error": "Invalid or expired token"}
            )
        
        # Extract user information
        user_id = uuid.UUID(payload["sub"])
        school_id = uuid.UUID(payload["school_id"]) if payload.get("school_id") else None
        permissions = payload.get("permissions", [])
        
        # Verify user still exists and is active
        with get_db_context() as db:
            user = db.query(User).filter(
                User.id == user_id,
                User.is_active == True,
                User.is_deleted == False
            ).first()
            
            if not user:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"error": "User not found or inactive"}
                )
        
        # Set user context in request state
        request.state.user_id = str(user_id)
        request.state.school_id = str(school_id) if school_id else None
        request.state.permissions = permissions
        request.state.user = user
        
        return await call_next(request)


class SubdomainRoutingMiddleware(BaseHTTPMiddleware):
    """Middleware for subdomain-based tenant routing"""

    def __init__(self, app, **kwargs):
        super().__init__(app, **kwargs)
        self.excluded_paths = {"/health", "/docs", "/redoc", "/openapi.json"}

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Route requests based on subdomain"""

        # Skip subdomain routing for excluded paths
        if request.url.path in self.excluded_paths:
            return await call_next(request)

        # Extract subdomain from host header
        host = request.headers.get("host", "")
        subdomain = self._extract_subdomain(host)

        if subdomain:
            # Resolve subdomain to school information
            school_info = subdomain_service.resolve_subdomain_to_school(subdomain)

            if school_info:
                # Set tenant context in request state
                request.state.tenant_subdomain = subdomain
                request.state.tenant_school_id = school_info["school_id"]
                request.state.tenant_school_name = school_info["school_name"]
                request.state.tenant_organization_id = school_info.get("organization_id")
                request.state.tenant_settings = school_info["settings"]

                logger.info(
                    f"Tenant resolved from subdomain",
                    extra={
                        "subdomain": subdomain,
                        "school_id": school_info["school_id"],
                        "school_name": school_info["school_name"]
                    }
                )
            else:
                # Subdomain not found - return 404
                logger.warning(f"Unknown subdomain: {subdomain}")
                return JSONResponse(
                    status_code=status.HTTP_404_NOT_FOUND,
                    content={
                        "error": "School not found",
                        "message": f"No school found for subdomain '{subdomain}'"
                    }
                )
        else:
            # No subdomain - could be main domain or localhost
            if self._is_main_domain(host):
                # Main domain access - set default context
                request.state.tenant_subdomain = None
                request.state.tenant_school_id = None
                request.state.tenant_school_name = None
                request.state.tenant_organization_id = None
                request.state.tenant_settings = None
            else:
                # Invalid domain
                return JSONResponse(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    content={
                        "error": "Invalid domain",
                        "message": "Please access the application through a valid subdomain"
                    }
                )

        return await call_next(request)

    def _extract_subdomain(self, host: str) -> Optional[str]:
        """Extract subdomain from host header"""
        if not host:
            return None

        # Remove port if present
        host = host.split(':')[0]

        # Handle localhost and IP addresses
        if host in ['localhost', '127.0.0.1'] or host.startswith('192.168.'):
            return None

        # Split by dots
        parts = host.split('.')

        # Need at least 3 parts for subdomain (subdomain.domain.tld)
        if len(parts) < 3:
            return None

        # First part is the subdomain
        subdomain = parts[0]

        # Validate subdomain format
        if subdomain and subdomain != 'www':
            return subdomain.lower()

        return None

    def _is_main_domain(self, host: str) -> bool:
        """Check if host is the main domain"""
        if not host:
            return False

        # Remove port if present
        host = host.split(':')[0]

        # Main domain patterns
        main_domains = [
            'localhost',
            '127.0.0.1',
            settings.DOMAIN_NAME if hasattr(settings, 'DOMAIN_NAME') else None,
            f"www.{settings.DOMAIN_NAME}" if hasattr(settings, 'DOMAIN_NAME') else None
        ]

        # Add local development domains
        main_domains.extend([
            'myschoolerp.local',
            'www.myschoolerp.local'
        ])

        return host in [domain for domain in main_domains if domain]
