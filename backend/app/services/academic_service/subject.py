"""
Subject Service for School ERP
Handles CRUD operations, class-subject mapping, teacher assignment, and business logic for subjects
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from sqlalchemy.exc import IntegrityError
import uuid

from app.core.database import get_db_context
from app.core.logging import get_logger
from app.models.academic import Subject, ClassSubject, AcademicYear, Class
from app.models.auth import User

logger = get_logger(__name__)


class SubjectError(Exception):
    """Custom exception for subject-related errors"""
    pass


class SubjectService:
    """
    Service class for managing subjects and class-subject mappings
    Provides comprehensive CRUD operations with business logic validation
    """
    
    def __init__(self):
        self.logger = logger
    
    def create_subject(
        self,
        school_id: uuid.UUID,
        subject_code: str,
        name: str,
        user_id: Optional[uuid.UUID] = None,
        description: Optional[str] = None,
        subject_type: str = "core",
        academic_level: Optional[str] = None,
        default_credits: Optional[int] = None,
        default_hours_per_week: Optional[int] = None,
        settings: Optional[Dict[str, Any]] = None,
        is_active: bool = True
    ) -> Dict[str, Any]:
        """
        Create a new subject with comprehensive validation
        
        Args:
            school_id: School UUID
            subject_code: Unique subject code
            name: Subject name
            user_id: User creating the subject
            description: Subject description
            subject_type: Type of subject (core, elective, extra_curricular)
            academic_level: Academic level (primary, secondary, higher_secondary)
            default_credits: Default credit hours
            default_hours_per_week: Default weekly hours
            settings: Subject-specific settings
            is_active: Subject active status
            
        Returns:
            Dict with success status and subject data
        """
        try:
            with get_db_context() as db:
                # Check for duplicate subject code
                existing_code = db.query(Subject).filter(
                    and_(
                        Subject.school_id == school_id,
                        Subject.subject_code == subject_code.upper(),
                        Subject.is_deleted == False
                    )
                ).first()
                
                if existing_code:
                    return {
                        "success": False,
                        "message": f"Subject with code '{subject_code}' already exists",
                        "error_type": "duplicate_code"
                    }
                
                # Check for duplicate subject name
                existing_name = db.query(Subject).filter(
                    and_(
                        Subject.school_id == school_id,
                        Subject.name.ilike(name.strip()),
                        Subject.is_deleted == False
                    )
                ).first()
                
                if existing_name:
                    return {
                        "success": False,
                        "message": f"Subject with name '{name}' already exists",
                        "error_type": "duplicate_name"
                    }
                
                # Create new subject
                subject = Subject(
                    school_id=school_id,
                    subject_code=subject_code,
                    name=name,
                    description=description,
                    subject_type=subject_type,
                    academic_level=academic_level,
                    default_credits=default_credits,
                    default_hours_per_week=default_hours_per_week,
                    settings=settings,
                    is_active=is_active,
                    created_by=user_id,
                    updated_by=user_id
                )
                
                db.add(subject)
                db.commit()
                db.refresh(subject)
                
                self.logger.info(f"Subject created successfully: {subject.subject_code} - {subject.name}")
                
                return {
                    "success": True,
                    "message": "Subject created successfully",
                    "subject": self._subject_to_dict(subject)
                }
                
        except IntegrityError as e:
            self.logger.error(f"Database integrity error creating subject: {str(e)}")
            return {
                "success": False,
                "message": "Failed to create subject due to data conflict",
                "error_type": "integrity_error"
            }
        except Exception as e:
            self.logger.error(f"Unexpected error creating subject: {str(e)}")
            raise SubjectError("Failed to create subject due to system error")
    
    def get_subject(self, school_id: uuid.UUID, subject_id: uuid.UUID) -> Optional[Dict[str, Any]]:
        """
        Get subject by ID with school isolation
        
        Args:
            school_id: School UUID
            subject_id: Subject UUID
            
        Returns:
            Subject dictionary or None if not found
        """
        try:
            with get_db_context() as db:
                subject = db.query(Subject).filter(
                    and_(
                        Subject.id == subject_id,
                        Subject.school_id == school_id,
                        Subject.is_deleted == False
                    )
                ).first()
                
                if not subject:
                    return None
                
                return self._subject_to_dict(subject)
                
        except Exception as e:
            self.logger.error(f"Error retrieving subject {subject_id}: {str(e)}")
            raise SubjectError("Failed to retrieve subject")
    
    def list_subjects(
        self,
        school_id: uuid.UUID,
        include_inactive: bool = True,
        subject_type: Optional[str] = None,
        academic_level: Optional[str] = None,
        limit: int = 50,
        offset: int = 0,
        order_by: str = "display_order",
        order_direction: str = "asc"
    ) -> Dict[str, Any]:
        """
        List subjects with filtering and pagination
        
        Args:
            school_id: School UUID
            include_inactive: Include inactive subjects
            subject_type: Filter by subject type
            academic_level: Filter by academic level
            limit: Maximum results to return
            offset: Number of results to skip
            order_by: Field to order by
            order_direction: Order direction (asc/desc)
            
        Returns:
            Dict with subjects list and pagination info
        """
        try:
            with get_db_context() as db:
                # Build base query
                query = db.query(Subject).filter(
                    and_(
                        Subject.school_id == school_id,
                        Subject.is_deleted == False
                    )
                )
                
                # Apply filters
                if not include_inactive:
                    query = query.filter(Subject.is_active == True)
                
                if subject_type:
                    query = query.filter(Subject.subject_type == subject_type)
                
                if academic_level:
                    query = query.filter(Subject.academic_level == academic_level)
                
                # Apply ordering
                order_field = getattr(Subject, order_by, Subject.display_order)
                if order_direction.lower() == "desc":
                    query = query.order_by(desc(order_field))
                else:
                    query = query.order_by(asc(order_field))
                
                # Get total count
                total_count = query.count()
                
                # Apply pagination
                subjects = query.offset(offset).limit(limit).all()
                
                return {
                    "success": True,
                    "subjects": [self._subject_to_dict(subject) for subject in subjects],
                    "pagination": {
                        "total": total_count,
                        "limit": limit,
                        "offset": offset,
                        "has_more": offset + limit < total_count
                    }
                }
                
        except Exception as e:
            self.logger.error(f"Error listing subjects: {str(e)}")
            raise SubjectError("Failed to list subjects")
    
    def _subject_to_dict(self, subject: Subject) -> Dict[str, Any]:
        """Convert Subject model to dictionary with computed fields"""
        return {
            "id": str(subject.id),
            "school_id": str(subject.school_id),
            "subject_code": subject.subject_code,
            "name": subject.name,
            "description": subject.description,
            "subject_type": subject.subject_type,
            "academic_level": subject.academic_level,
            "is_active": subject.is_active,
            "display_order": subject.display_order,
            "default_credits": subject.default_credits,
            "default_hours_per_week": subject.default_hours_per_week,
            "settings": subject.settings,
            "approved_by": str(subject.approved_by) if subject.approved_by else None,
            "approved_at": subject.approved_at.isoformat() if subject.approved_at else None,
            "created_at": subject.created_at.isoformat() if subject.created_at else None,
            "updated_at": subject.updated_at.isoformat() if subject.updated_at else None,
            "created_by": str(subject.created_by) if subject.created_by else None,
            "updated_by": str(subject.updated_by) if subject.updated_by else None,
            # Computed fields
            "is_core_subject": subject.is_core_subject,
            "is_elective_subject": subject.is_elective_subject,
            "is_extra_curricular": subject.is_extra_curricular,
            "assigned_classes_count": subject.get_assigned_classes_count()
        }


    def update_subject(
        self,
        school_id: uuid.UUID,
        subject_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        **update_data
    ) -> Dict[str, Any]:
        """
        Update subject with validation

        Args:
            school_id: School UUID
            subject_id: Subject UUID
            user_id: User updating the subject
            **update_data: Fields to update

        Returns:
            Dict with success status and updated subject data
        """
        try:
            with get_db_context() as db:
                # Get existing subject
                subject = db.query(Subject).filter(
                    and_(
                        Subject.id == subject_id,
                        Subject.school_id == school_id,
                        Subject.is_deleted == False
                    )
                ).first()

                if not subject:
                    return {
                        "success": False,
                        "message": "Subject not found",
                        "error_type": "not_found"
                    }

                # Check for duplicate subject code if being updated
                if 'subject_code' in update_data:
                    new_code = update_data['subject_code'].upper()
                    if new_code != subject.subject_code:
                        existing_code = db.query(Subject).filter(
                            and_(
                                Subject.school_id == school_id,
                                Subject.subject_code == new_code,
                                Subject.id != subject_id,
                                Subject.is_deleted == False
                            )
                        ).first()

                        if existing_code:
                            return {
                                "success": False,
                                "message": f"Subject with code '{new_code}' already exists",
                                "error_type": "duplicate_code"
                            }

                # Check for duplicate subject name if being updated
                if 'name' in update_data:
                    new_name = update_data['name'].strip()
                    if new_name.lower() != subject.name.lower():
                        existing_name = db.query(Subject).filter(
                            and_(
                                Subject.school_id == school_id,
                                Subject.name.ilike(new_name),
                                Subject.id != subject_id,
                                Subject.is_deleted == False
                            )
                        ).first()

                        if existing_name:
                            return {
                                "success": False,
                                "message": f"Subject with name '{new_name}' already exists",
                                "error_type": "duplicate_name"
                            }

                # Update fields
                for field, value in update_data.items():
                    if hasattr(subject, field):
                        setattr(subject, field, value)

                # Update metadata
                subject.updated_by = user_id
                subject.updated_at = datetime.utcnow()

                db.commit()
                db.refresh(subject)

                self.logger.info(f"Subject updated successfully: {subject.subject_code} - {subject.name}")

                return {
                    "success": True,
                    "message": "Subject updated successfully",
                    "subject": self._subject_to_dict(subject)
                }

        except IntegrityError as e:
            self.logger.error(f"Database integrity error updating subject: {str(e)}")
            return {
                "success": False,
                "message": "Failed to update subject due to data conflict",
                "error_type": "integrity_error"
            }
        except Exception as e:
            self.logger.error(f"Unexpected error updating subject: {str(e)}")
            raise SubjectError("Failed to update subject due to system error")

    def delete_subject(
        self,
        school_id: uuid.UUID,
        subject_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Soft delete subject with dependency checking

        Args:
            school_id: School UUID
            subject_id: Subject UUID
            user_id: User deleting the subject
            reason: Reason for deletion

        Returns:
            Dict with success status and message
        """
        try:
            with get_db_context() as db:
                # Get existing subject
                subject = db.query(Subject).filter(
                    and_(
                        Subject.id == subject_id,
                        Subject.school_id == school_id,
                        Subject.is_deleted == False
                    )
                ).first()

                if not subject:
                    return {
                        "success": False,
                        "message": "Subject not found",
                        "error_type": "not_found"
                    }

                # Check for active class assignments
                active_assignments = db.query(ClassSubject).filter(
                    and_(
                        ClassSubject.subject_id == subject_id,
                        ClassSubject.school_id == school_id,
                        ClassSubject.is_active == True,
                        ClassSubject.is_deleted == False
                    )
                ).count()

                if active_assignments > 0:
                    return {
                        "success": False,
                        "message": f"Cannot delete subject. It is assigned to {active_assignments} active class(es)",
                        "error_type": "has_dependencies"
                    }

                # Perform soft delete
                subject.is_deleted = True
                subject.deleted_at = datetime.utcnow()
                subject.deleted_by = user_id
                subject.deletion_reason = reason

                db.commit()

                self.logger.info(f"Subject deleted successfully: {subject.subject_code} - {subject.name}")

                return {
                    "success": True,
                    "message": "Subject deleted successfully"
                }

        except Exception as e:
            self.logger.error(f"Error deleting subject: {str(e)}")
            raise SubjectError("Failed to delete subject due to system error")

    def assign_subject_to_class(
        self,
        school_id: uuid.UUID,
        subject_id: uuid.UUID,
        class_id: uuid.UUID,
        academic_year_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        teacher_id: Optional[uuid.UUID] = None,
        is_mandatory: bool = True,
        credits: Optional[int] = None,
        hours_per_week: Optional[int] = None,
        max_marks: Optional[int] = None,
        pass_marks: Optional[int] = None,
        settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Assign subject to class for specific academic year

        Args:
            school_id: School UUID
            subject_id: Subject UUID
            class_id: Class UUID
            academic_year_id: Academic year UUID
            user_id: User making the assignment
            teacher_id: Teacher assigned to subject
            is_mandatory: Whether subject is mandatory
            credits: Credit hours for this class
            hours_per_week: Weekly hours for this class
            max_marks: Maximum marks for assessments
            pass_marks: Minimum marks to pass
            settings: Class-specific settings

        Returns:
            Dict with success status and assignment data
        """
        try:
            with get_db_context() as db:
                # Validate subject exists
                subject = db.query(Subject).filter(
                    and_(
                        Subject.id == subject_id,
                        Subject.school_id == school_id,
                        Subject.is_deleted == False,
                        Subject.is_active == True
                    )
                ).first()

                if not subject:
                    return {
                        "success": False,
                        "message": "Subject not found or inactive",
                        "error_type": "subject_not_found"
                    }

                # Validate class exists
                class_obj = db.query(Class).filter(
                    and_(
                        Class.id == class_id,
                        Class.school_id == school_id,
                        Class.is_deleted == False,
                        Class.is_active == True
                    )
                ).first()

                if not class_obj:
                    return {
                        "success": False,
                        "message": "Class not found or inactive",
                        "error_type": "class_not_found"
                    }

                # Validate academic year exists
                academic_year = db.query(AcademicYear).filter(
                    and_(
                        AcademicYear.id == academic_year_id,
                        AcademicYear.school_id == school_id,
                        AcademicYear.is_deleted == False
                    )
                ).first()

                if not academic_year:
                    return {
                        "success": False,
                        "message": "Academic year not found",
                        "error_type": "academic_year_not_found"
                    }

                # Check if assignment already exists
                existing_assignment = db.query(ClassSubject).filter(
                    and_(
                        ClassSubject.school_id == school_id,
                        ClassSubject.subject_id == subject_id,
                        ClassSubject.class_id == class_id,
                        ClassSubject.academic_year_id == academic_year_id,
                        ClassSubject.is_deleted == False
                    )
                ).first()

                if existing_assignment:
                    return {
                        "success": False,
                        "message": "Subject is already assigned to this class for the academic year",
                        "error_type": "already_assigned"
                    }

                # Create class-subject assignment
                class_subject = ClassSubject(
                    school_id=school_id,
                    subject_id=subject_id,
                    class_id=class_id,
                    academic_year_id=academic_year_id,
                    teacher_id=teacher_id,
                    is_mandatory=is_mandatory,
                    credits=credits,
                    hours_per_week=hours_per_week,
                    max_marks=max_marks,
                    pass_marks=pass_marks,
                    settings=settings,
                    assigned_at=date.today(),
                    assigned_by=user_id,
                    created_by=user_id,
                    updated_by=user_id
                )

                db.add(class_subject)
                db.commit()
                db.refresh(class_subject)

                self.logger.info(f"Subject assigned to class: {subject.name} -> {class_obj.name}")

                return {
                    "success": True,
                    "message": "Subject assigned to class successfully",
                    "assignment": self._class_subject_to_dict(class_subject)
                }

        except IntegrityError as e:
            self.logger.error(f"Database integrity error assigning subject: {str(e)}")
            return {
                "success": False,
                "message": "Failed to assign subject due to data conflict",
                "error_type": "integrity_error"
            }
        except Exception as e:
            self.logger.error(f"Unexpected error assigning subject: {str(e)}")
            raise SubjectError("Failed to assign subject due to system error")

    def _class_subject_to_dict(self, class_subject: ClassSubject) -> Dict[str, Any]:
        """Convert ClassSubject model to dictionary with computed fields"""
        return {
            "id": str(class_subject.id),
            "school_id": str(class_subject.school_id),
            "subject_id": str(class_subject.subject_id),
            "class_id": str(class_subject.class_id),
            "academic_year_id": str(class_subject.academic_year_id),
            "teacher_id": str(class_subject.teacher_id) if class_subject.teacher_id else None,
            "is_mandatory": class_subject.is_mandatory,
            "is_active": class_subject.is_active,
            "credits": class_subject.credits,
            "hours_per_week": class_subject.hours_per_week,
            "max_marks": class_subject.max_marks,
            "pass_marks": class_subject.pass_marks,
            "display_order": class_subject.display_order,
            "settings": class_subject.settings,
            "assigned_at": class_subject.assigned_at.isoformat() if class_subject.assigned_at else None,
            "assigned_by": str(class_subject.assigned_by) if class_subject.assigned_by else None,
            "created_at": class_subject.created_at.isoformat() if class_subject.created_at else None,
            "updated_at": class_subject.updated_at.isoformat() if class_subject.updated_at else None,
            # Computed fields
            "pass_percentage": class_subject.pass_percentage,
            "is_assigned_to_teacher": class_subject.is_assigned_to_teacher,
            "effective_credits": class_subject.get_effective_credits(),
            "effective_hours_per_week": class_subject.get_effective_hours_per_week(),
            "full_name": class_subject.get_full_name()
        }


# Global service instance
subject_service = SubjectService()
