"""
Academic Year service for School ERP
Handles academic year management, transitions, and business logic
"""

from datetime import datetime, date, timedelta
from typing import Optional, Dict, Any, List, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from sqlalchemy.exc import IntegrityError
import uuid

from app.models.academic import Academic<PERSON>ear
from app.core.database import get_db_context
from app.core.logging import get_logger
from app.services.audit_service.audit import log_audit_event

logger = get_logger(__name__)


class AcademicYearError(Exception):
    """Custom exception for academic year operations"""
    pass


class AcademicYearService:
    """
    Comprehensive academic year management service
    Handles CRUD operations, year transitions, and business logic
    """
    
    def __init__(self):
        self.logger = logger
    
    def create_academic_year(
        self,
        school_id: uuid.UUID,
        year_label: str,
        start_date: date,
        end_date: date,
        user_id: Optional[uuid.UUID] = None,
        display_name: Optional[str] = None,
        description: Optional[str] = None,
        terms_config: Optional[Dict[str, Any]] = None,
        holidays_config: Optional[Dict[str, Any]] = None,
        settings: Optional[Dict[str, Any]] = None,
        auto_activate: bool = False
    ) -> Dict[str, Any]:
        """
        Create a new academic year for a school
        
        Args:
            school_id: School UUID
            year_label: Academic year label (e.g., "2024-25")
            start_date: Academic year start date
            end_date: Academic year end date
            user_id: User creating the academic year
            display_name: Optional display name
            description: Optional description
            terms_config: Terms/semesters configuration
            holidays_config: Holidays configuration
            settings: Academic year settings
            auto_activate: Whether to auto-activate if no active year exists
        
        Returns:
            Dict with success status and academic year data
        """
        try:
            with get_db_context() as db:
                # Validate dates
                if start_date >= end_date:
                    raise AcademicYearError("Start date must be before end date")
                
                # Check for overlapping academic years
                overlapping = db.query(AcademicYear).filter(
                    and_(
                        AcademicYear.school_id == school_id,
                        AcademicYear.is_deleted == False,
                        or_(
                            and_(AcademicYear.start_date <= start_date, AcademicYear.end_date >= start_date),
                            and_(AcademicYear.start_date <= end_date, AcademicYear.end_date >= end_date),
                            and_(AcademicYear.start_date >= start_date, AcademicYear.end_date <= end_date)
                        )
                    )
                ).first()
                
                if overlapping:
                    raise AcademicYearError(
                        f"Academic year overlaps with existing year: {overlapping.year_label}"
                    )
                
                # Check if year label already exists
                existing_label = db.query(AcademicYear).filter(
                    and_(
                        AcademicYear.school_id == school_id,
                        AcademicYear.year_label == year_label,
                        AcademicYear.is_deleted == False
                    )
                ).first()
                
                if existing_label:
                    raise AcademicYearError(f"Academic year with label '{year_label}' already exists")
                
                # Check if we should auto-activate
                should_activate = auto_activate
                if auto_activate:
                    active_year = self._get_active_academic_year(school_id, db)
                    if active_year:
                        should_activate = False  # Don't auto-activate if there's already an active year
                
                # Create academic year
                academic_year = AcademicYear(
                    school_id=school_id,
                    year_label=year_label,
                    display_name=display_name,
                    description=description,
                    start_date=start_date,
                    end_date=end_date,
                    is_active=should_activate,
                    status='active' if should_activate else 'draft',
                    terms_config=terms_config,
                    holidays_config=holidays_config,
                    settings=settings,
                    created_by=user_id,
                    updated_by=user_id
                )
                
                db.add(academic_year)
                db.commit()
                db.refresh(academic_year)
                
                # Log audit event
                log_audit_event(
                    school_id=school_id,
                    module="academic",
                    action="create_academic_year",
                    user_id=user_id,
                    resource_type="academic_year",
                    resource_id=academic_year.id,
                    event_data={
                        "year_label": year_label,
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat(),
                        "is_active": should_activate,
                        "auto_activated": should_activate and auto_activate
                    }
                )
                
                self.logger.info(
                    f"Academic year created successfully: {year_label}",
                    extra={
                        "school_id": str(school_id),
                        "academic_year_id": str(academic_year.id),
                        "user_id": str(user_id) if user_id else None,
                        "is_active": should_activate
                    }
                )
                
                return {
                    "success": True,
                    "message": f"Academic year '{year_label}' created successfully",
                    "academic_year": academic_year.to_dict()
                }
                
        except AcademicYearError:
            raise
        except IntegrityError as e:
            self.logger.error(f"Database integrity error creating academic year: {str(e)}")
            raise AcademicYearError("Failed to create academic year due to data conflict")
        except Exception as e:
            self.logger.error(f"Unexpected error creating academic year: {str(e)}")
            raise AcademicYearError("Failed to create academic year due to system error")
    
    def get_academic_year(
        self,
        school_id: uuid.UUID,
        academic_year_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """
        Get academic year by ID
        
        Args:
            school_id: School UUID
            academic_year_id: Academic year UUID
        
        Returns:
            Academic year data or None if not found
        """
        try:
            with get_db_context() as db:
                academic_year = db.query(AcademicYear).filter(
                    and_(
                        AcademicYear.id == academic_year_id,
                        AcademicYear.school_id == school_id,
                        AcademicYear.is_deleted == False
                    )
                ).first()
                
                if not academic_year:
                    return None
                
                return academic_year.to_dict()
                
        except Exception as e:
            self.logger.error(f"Error retrieving academic year: {str(e)}")
            return None
    
    def get_active_academic_year(self, school_id: uuid.UUID) -> Optional[Dict[str, Any]]:
        """
        Get the currently active academic year for a school
        
        Args:
            school_id: School UUID
        
        Returns:
            Active academic year data or None if no active year
        """
        try:
            with get_db_context() as db:
                active_year = self._get_active_academic_year(school_id, db)
                return active_year.to_dict() if active_year else None
                
        except Exception as e:
            self.logger.error(f"Error retrieving active academic year: {str(e)}")
            return None
    
    def list_academic_years(
        self,
        school_id: uuid.UUID,
        include_inactive: bool = True,
        limit: int = 50,
        offset: int = 0,
        order_by: str = "start_date",
        order_direction: str = "desc"
    ) -> Dict[str, Any]:
        """
        List academic years for a school
        
        Args:
            school_id: School UUID
            include_inactive: Whether to include inactive years
            limit: Maximum number of results
            offset: Number of results to skip
            order_by: Field to order by
            order_direction: Order direction (asc/desc)
        
        Returns:
            Dict with academic years list and metadata
        """
        try:
            with get_db_context() as db:
                query = db.query(AcademicYear).filter(
                    and_(
                        AcademicYear.school_id == school_id,
                        AcademicYear.is_deleted == False
                    )
                )
                
                if not include_inactive:
                    query = query.filter(AcademicYear.status.in_(['active', 'draft']))
                
                # Apply ordering
                order_field = getattr(AcademicYear, order_by, AcademicYear.start_date)
                if order_direction.lower() == 'asc':
                    query = query.order_by(asc(order_field))
                else:
                    query = query.order_by(desc(order_field))
                
                # Get total count
                total_count = query.count()
                
                # Apply pagination
                academic_years = query.offset(offset).limit(limit).all()
                
                return {
                    "success": True,
                    "academic_years": [year.to_dict() for year in academic_years],
                    "total_count": total_count,
                    "limit": limit,
                    "offset": offset,
                    "has_more": (offset + limit) < total_count
                }
                
        except Exception as e:
            self.logger.error(f"Error listing academic years: {str(e)}")
            return {
                "success": False,
                "error": "Failed to retrieve academic years",
                "academic_years": [],
                "total_count": 0
            }
    
    def _get_active_academic_year(self, school_id: uuid.UUID, db: Session) -> Optional[AcademicYear]:
        """Get active academic year (internal helper)"""
        return db.query(AcademicYear).filter(
            and_(
                AcademicYear.school_id == school_id,
                AcademicYear.is_active == True,
                AcademicYear.is_deleted == False
            )
        ).first()

    def update_academic_year(
        self,
        school_id: uuid.UUID,
        academic_year_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        **updates
    ) -> Dict[str, Any]:
        """
        Update academic year details

        Args:
            school_id: School UUID
            academic_year_id: Academic year UUID
            user_id: User making the update
            **updates: Fields to update

        Returns:
            Dict with success status and updated academic year data
        """
        try:
            with get_db_context() as db:
                academic_year = db.query(AcademicYear).filter(
                    and_(
                        AcademicYear.id == academic_year_id,
                        AcademicYear.school_id == school_id,
                        AcademicYear.is_deleted == False
                    )
                ).first()

                if not academic_year:
                    raise AcademicYearError("Academic year not found")

                # Store original values for audit
                original_values = {}
                changes = {}

                # Update allowed fields
                allowed_fields = [
                    'year_label', 'display_name', 'description', 'start_date', 'end_date',
                    'terms_config', 'holidays_config', 'settings', 'total_working_days'
                ]

                for field, value in updates.items():
                    if field in allowed_fields and hasattr(academic_year, field):
                        original_values[field] = getattr(academic_year, field)
                        if original_values[field] != value:
                            setattr(academic_year, field, value)
                            changes[field] = {"from": original_values[field], "to": value}

                if not changes:
                    return {
                        "success": True,
                        "message": "No changes to update",
                        "academic_year": academic_year.to_dict()
                    }

                # Update audit fields
                academic_year.updated_by = user_id

                db.commit()
                db.refresh(academic_year)

                # Log audit event
                log_audit_event(
                    school_id=school_id,
                    module="academic",
                    action="update_academic_year",
                    user_id=user_id,
                    resource_type="academic_year",
                    resource_id=academic_year.id,
                    changes=changes,
                    event_data={"year_label": academic_year.year_label}
                )

                self.logger.info(
                    f"Academic year updated successfully: {academic_year.year_label}",
                    extra={
                        "school_id": str(school_id),
                        "academic_year_id": str(academic_year.id),
                        "user_id": str(user_id) if user_id else None,
                        "changes": list(changes.keys())
                    }
                )

                return {
                    "success": True,
                    "message": f"Academic year '{academic_year.year_label}' updated successfully",
                    "academic_year": academic_year.to_dict(),
                    "changes": changes
                }

        except AcademicYearError:
            raise
        except IntegrityError as e:
            self.logger.error(f"Database integrity error updating academic year: {str(e)}")
            raise AcademicYearError("Failed to update academic year due to data conflict")
        except Exception as e:
            self.logger.error(f"Unexpected error updating academic year: {str(e)}")
            raise AcademicYearError("Failed to update academic year due to system error")

    def activate_academic_year(
        self,
        school_id: uuid.UUID,
        academic_year_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        force: bool = False
    ) -> Dict[str, Any]:
        """
        Activate an academic year (deactivates current active year)

        Args:
            school_id: School UUID
            academic_year_id: Academic year UUID to activate
            user_id: User performing the activation
            force: Force activation even if year has ended

        Returns:
            Dict with success status and activation details
        """
        try:
            with get_db_context() as db:
                # Get the academic year to activate
                academic_year = db.query(AcademicYear).filter(
                    and_(
                        AcademicYear.id == academic_year_id,
                        AcademicYear.school_id == school_id,
                        AcademicYear.is_deleted == False
                    )
                ).first()

                if not academic_year:
                    raise AcademicYearError("Academic year not found")

                # Check if it can be activated
                can_activate, reason = academic_year.can_be_activated()
                if not can_activate and not force:
                    raise AcademicYearError(reason)

                # Get currently active year
                current_active = self._get_active_academic_year(school_id, db)

                # Deactivate current active year
                deactivated_year = None
                if current_active and current_active.id != academic_year.id:
                    current_active.is_active = False
                    current_active.status = 'completed' if current_active.end_date < date.today() else 'draft'
                    current_active.updated_by = user_id
                    deactivated_year = current_active.year_label

                # Activate the new year
                academic_year.is_active = True
                academic_year.status = 'active'
                academic_year.updated_by = user_id

                db.commit()

                # Log audit events
                if deactivated_year:
                    log_audit_event(
                        school_id=school_id,
                        module="academic",
                        action="deactivate_academic_year",
                        user_id=user_id,
                        resource_type="academic_year",
                        resource_id=current_active.id,
                        event_data={"year_label": deactivated_year, "reason": "new_year_activated"}
                    )

                log_audit_event(
                    school_id=school_id,
                    module="academic",
                    action="activate_academic_year",
                    user_id=user_id,
                    resource_type="academic_year",
                    resource_id=academic_year.id,
                    event_data={
                        "year_label": academic_year.year_label,
                        "previous_active": deactivated_year,
                        "forced": force
                    }
                )

                self.logger.info(
                    f"Academic year activated: {academic_year.year_label}",
                    extra={
                        "school_id": str(school_id),
                        "academic_year_id": str(academic_year.id),
                        "user_id": str(user_id) if user_id else None,
                        "previous_active": deactivated_year
                    }
                )

                return {
                    "success": True,
                    "message": f"Academic year '{academic_year.year_label}' activated successfully",
                    "academic_year": academic_year.to_dict(),
                    "previous_active": deactivated_year
                }

        except AcademicYearError:
            raise
        except Exception as e:
            self.logger.error(f"Error activating academic year: {str(e)}")
            raise AcademicYearError("Failed to activate academic year due to system error")

    def complete_academic_year(
        self,
        school_id: uuid.UUID,
        academic_year_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """
        Mark an academic year as completed

        Args:
            school_id: School UUID
            academic_year_id: Academic year UUID to complete
            user_id: User performing the completion

        Returns:
            Dict with success status and completion details
        """
        try:
            with get_db_context() as db:
                academic_year = db.query(AcademicYear).filter(
                    and_(
                        AcademicYear.id == academic_year_id,
                        AcademicYear.school_id == school_id,
                        AcademicYear.is_deleted == False
                    )
                ).first()

                if not academic_year:
                    raise AcademicYearError("Academic year not found")

                # Check if it can be completed
                can_complete, reason = academic_year.can_be_completed()
                if not can_complete:
                    raise AcademicYearError(reason)

                # Complete the academic year
                academic_year.is_active = False
                academic_year.status = 'completed'
                academic_year.updated_by = user_id

                db.commit()

                # Log audit event
                log_audit_event(
                    school_id=school_id,
                    module="academic",
                    action="complete_academic_year",
                    user_id=user_id,
                    resource_type="academic_year",
                    resource_id=academic_year.id,
                    event_data={"year_label": academic_year.year_label}
                )

                self.logger.info(
                    f"Academic year completed: {academic_year.year_label}",
                    extra={
                        "school_id": str(school_id),
                        "academic_year_id": str(academic_year.id),
                        "user_id": str(user_id) if user_id else None
                    }
                )

                return {
                    "success": True,
                    "message": f"Academic year '{academic_year.year_label}' completed successfully",
                    "academic_year": academic_year.to_dict()
                }

        except AcademicYearError:
            raise
        except Exception as e:
            self.logger.error(f"Error completing academic year: {str(e)}")
            raise AcademicYearError("Failed to complete academic year due to system error")

    def delete_academic_year(
        self,
        school_id: uuid.UUID,
        academic_year_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Soft delete an academic year

        Args:
            school_id: School UUID
            academic_year_id: Academic year UUID to delete
            user_id: User performing the deletion
            reason: Reason for deletion

        Returns:
            Dict with success status and deletion details
        """
        try:
            with get_db_context() as db:
                academic_year = db.query(AcademicYear).filter(
                    and_(
                        AcademicYear.id == academic_year_id,
                        AcademicYear.school_id == school_id,
                        AcademicYear.is_deleted == False
                    )
                ).first()

                if not academic_year:
                    raise AcademicYearError("Academic year not found")

                # Check if it's safe to delete (no active dependencies)
                if academic_year.is_active:
                    raise AcademicYearError("Cannot delete active academic year. Deactivate it first.")

                # TODO: Add checks for dependencies (students, fees, etc.) when those modules are implemented

                # Soft delete the academic year
                academic_year.soft_delete(deleted_by=user_id, reason=reason)

                db.commit()

                # Log audit event
                log_audit_event(
                    school_id=school_id,
                    module="academic",
                    action="delete_academic_year",
                    user_id=user_id,
                    resource_type="academic_year",
                    resource_id=academic_year.id,
                    event_data={
                        "year_label": academic_year.year_label,
                        "reason": reason
                    }
                )

                self.logger.info(
                    f"Academic year deleted: {academic_year.year_label}",
                    extra={
                        "school_id": str(school_id),
                        "academic_year_id": str(academic_year.id),
                        "user_id": str(user_id) if user_id else None,
                        "reason": reason
                    }
                )

                return {
                    "success": True,
                    "message": f"Academic year '{academic_year.year_label}' deleted successfully"
                }

        except AcademicYearError:
            raise
        except Exception as e:
            self.logger.error(f"Error deleting academic year: {str(e)}")
            raise AcademicYearError("Failed to delete academic year due to system error")

    def generate_academic_year_label(self, start_year: int) -> str:
        """
        Generate academic year label in Indian format (e.g., 2024-25)

        Args:
            start_year: Starting year of the academic year

        Returns:
            Academic year label string
        """
        end_year = start_year + 1
        return f"{start_year}-{str(end_year)[2:]}"

    def get_current_indian_academic_year(self) -> Tuple[str, date, date]:
        """
        Get current Indian academic year details (April-March cycle)

        Returns:
            Tuple of (year_label, start_date, end_date)
        """
        today = date.today()

        # Indian academic year runs from April to March
        if today.month >= 4:  # April onwards - current year to next year
            start_year = today.year
            end_year = today.year + 1
        else:  # January to March - previous year to current year
            start_year = today.year - 1
            end_year = today.year

        year_label = self.generate_academic_year_label(start_year)
        start_date = date(start_year, 4, 1)  # April 1st
        end_date = date(end_year, 3, 31)    # March 31st

        return year_label, start_date, end_date


# Global academic year service instance
academic_year_service = AcademicYearService()
