"""
Subdomain management service for School ERP
Handles subdomain validation, availability, and routing
"""

import re
from typing import Optional, Dict, Any, List, Set
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import uuid

from app.models.base import School, Organization
from app.core.database import get_db_context
from app.core.logging import get_logger
from app.core.config import settings
from app.services.audit_service.audit import log_audit_event

logger = get_logger(__name__)


class SubdomainService:
    """Comprehensive subdomain management service"""
    
    # Reserved subdomains that cannot be used by schools
    RESERVED_SUBDOMAINS: Set[str] = {
        # System subdomains
        "admin", "api", "www", "mail", "email", "smtp", "pop", "imap",
        "ftp", "sftp", "ssh", "vpn", "cdn", "static", "assets", "media",
        "files", "uploads", "downloads", "backup", "archive",
        
        # Application subdomains
        "app", "dashboard", "portal", "login", "auth", "oauth", "sso",
        "support", "help", "docs", "documentation", "wiki", "blog",
        "news", "status", "health", "monitoring", "metrics", "logs",
        
        # Marketing/Business subdomains
        "marketing", "sales", "billing", "payment", "invoice", "finance",
        "hr", "careers", "jobs", "about", "contact", "privacy", "terms",
        "legal", "compliance", "security", "trust",
        
        # Technical subdomains
        "test", "testing", "staging", "dev", "development", "demo",
        "sandbox", "preview", "beta", "alpha", "canary", "edge",
        "git", "gitlab", "github", "ci", "cd", "jenkins", "docker",
        
        # Common words that might cause confusion
        "school", "erp", "system", "platform", "service", "server",
        "database", "db", "cache", "redis", "postgres", "mysql",
        "elastic", "search", "queue", "worker", "job", "task",
        
        # Indian education specific
        "cbse", "icse", "ncert", "neet", "jee", "upsc", "ssc",
        "education", "learning", "teaching", "student", "teacher",
        "principal", "management", "board", "exam", "result"
    }
    
    # Subdomain validation regex
    SUBDOMAIN_PATTERN = re.compile(r'^[a-z0-9]([a-z0-9-]*[a-z0-9])?$')
    
    def __init__(self):
        self.logger = logger
    
    def validate_subdomain(self, subdomain: str) -> Dict[str, Any]:
        """
        Comprehensive subdomain validation
        Returns validation result with detailed feedback
        """
        if not subdomain:
            return {
                "is_valid": False,
                "error": "Subdomain cannot be empty",
                "suggestions": []
            }
        
        # Convert to lowercase for consistency
        subdomain = subdomain.lower().strip()
        
        # Length validation
        if len(subdomain) < 3:
            return {
                "is_valid": False,
                "error": "Subdomain must be at least 3 characters long",
                "suggestions": [f"{subdomain}school", f"{subdomain}edu", f"my{subdomain}"]
            }
        
        if len(subdomain) > 63:  # DNS label limit
            return {
                "is_valid": False,
                "error": "Subdomain cannot exceed 63 characters",
                "suggestions": [subdomain[:60]]
            }
        
        # Pattern validation (alphanumeric and hyphens, no consecutive hyphens)
        if not self.SUBDOMAIN_PATTERN.match(subdomain):
            return {
                "is_valid": False,
                "error": "Subdomain can only contain lowercase letters, numbers, and hyphens. Cannot start or end with hyphen.",
                "suggestions": [
                    re.sub(r'[^a-z0-9-]', '', subdomain),
                    re.sub(r'[^a-z0-9]', '', subdomain)
                ]
            }
        
        # Check for consecutive hyphens
        if '--' in subdomain:
            return {
                "is_valid": False,
                "error": "Subdomain cannot contain consecutive hyphens",
                "suggestions": [subdomain.replace('--', '-')]
            }
        
        # Reserved subdomain check
        if subdomain in self.RESERVED_SUBDOMAINS:
            return {
                "is_valid": False,
                "error": f"'{subdomain}' is a reserved subdomain and cannot be used",
                "suggestions": [
                    f"{subdomain}school",
                    f"{subdomain}edu",
                    f"my{subdomain}",
                    f"{subdomain}academy"
                ]
            }
        
        # Check for potentially confusing subdomains
        confusing_patterns = ['admin', 'api', 'www', 'mail', 'test']
        for pattern in confusing_patterns:
            if pattern in subdomain and subdomain != pattern:
                logger.warning(f"Potentially confusing subdomain: {subdomain}")
        
        return {
            "is_valid": True,
            "normalized": subdomain,
            "suggestions": []
        }
    
    def check_availability(self, subdomain: str) -> Dict[str, Any]:
        """
        Check if subdomain is available for use
        """
        # First validate the subdomain
        validation = self.validate_subdomain(subdomain)
        if not validation["is_valid"]:
            return validation
        
        normalized_subdomain = validation["normalized"]
        
        with get_db_context() as db:
            try:
                # Check if subdomain is already taken by any school
                existing_school = db.query(School).filter(
                    and_(
                        School.subdomain == normalized_subdomain,
                        School.is_deleted == False
                    )
                ).first()
                
                if existing_school:
                    return {
                        "is_valid": False,
                        "is_available": False,
                        "error": f"Subdomain '{normalized_subdomain}' is already taken",
                        "suggestions": self._generate_alternative_subdomains(normalized_subdomain, db)
                    }
                
                # Check if subdomain is in use by any organization
                existing_org = db.query(Organization).filter(
                    and_(
                        Organization.subdomain == normalized_subdomain,
                        Organization.is_deleted == False
                    )
                ).first()
                
                if existing_org:
                    return {
                        "is_valid": False,
                        "is_available": False,
                        "error": f"Subdomain '{normalized_subdomain}' is already taken by an organization",
                        "suggestions": self._generate_alternative_subdomains(normalized_subdomain, db)
                    }
                
                return {
                    "is_valid": True,
                    "is_available": True,
                    "subdomain": normalized_subdomain,
                    "message": f"Subdomain '{normalized_subdomain}' is available"
                }
                
            except Exception as e:
                self.logger.error(f"Error checking subdomain availability: {str(e)}")
                return {
                    "is_valid": False,
                    "is_available": False,
                    "error": "Unable to check subdomain availability. Please try again.",
                    "suggestions": []
                }
    
    def _generate_alternative_subdomains(self, base_subdomain: str, db: Session, limit: int = 5) -> List[str]:
        """Generate alternative available subdomains"""
        alternatives = []
        
        # Common suffixes for schools
        suffixes = ["school", "edu", "academy", "institute", "college", "learning"]
        
        # Try with suffixes
        for suffix in suffixes:
            candidate = f"{base_subdomain}{suffix}"
            if self._is_subdomain_available_in_db(candidate, db):
                alternatives.append(candidate)
                if len(alternatives) >= limit:
                    break
        
        # Try with numbers
        if len(alternatives) < limit:
            for i in range(1, 100):
                candidate = f"{base_subdomain}{i}"
                if self._is_subdomain_available_in_db(candidate, db):
                    alternatives.append(candidate)
                    if len(alternatives) >= limit:
                        break
        
        # Try with prefixes
        if len(alternatives) < limit:
            prefixes = ["my", "the", "new", "best", "top"]
            for prefix in prefixes:
                candidate = f"{prefix}{base_subdomain}"
                if self._is_subdomain_available_in_db(candidate, db):
                    alternatives.append(candidate)
                    if len(alternatives) >= limit:
                        break
        
        return alternatives[:limit]
    
    def _is_subdomain_available_in_db(self, subdomain: str, db: Session) -> bool:
        """Check if subdomain is available in database"""
        if subdomain in self.RESERVED_SUBDOMAINS:
            return False
        
        validation = self.validate_subdomain(subdomain)
        if not validation["is_valid"]:
            return False
        
        # Check schools
        school_exists = db.query(School).filter(
            and_(
                School.subdomain == subdomain,
                School.is_deleted == False
            )
        ).first() is not None
        
        # Check organizations
        org_exists = db.query(Organization).filter(
            and_(
                Organization.subdomain == subdomain,
                Organization.is_deleted == False
            )
        ).first() is not None
        
        return not (school_exists or org_exists)
    
    def resolve_subdomain_to_school(self, subdomain: str) -> Optional[Dict[str, Any]]:
        """
        Resolve subdomain to school information
        Used for routing requests to correct tenant
        """
        if not subdomain:
            return None
        
        subdomain = subdomain.lower().strip()
        
        with get_db_context() as db:
            try:
                # Find school by subdomain
                school = db.query(School).filter(
                    and_(
                        School.subdomain == subdomain,
                        School.is_active == True,
                        School.is_deleted == False
                    )
                ).first()
                
                if school:
                    return {
                        "school_id": str(school.id),
                        "school_name": school.name,
                        "organization_id": str(school.organization_id) if school.organization_id else None,
                        "status": "active" if school.is_active else "inactive",
                        "subdomain": school.subdomain,
                        "settings": {
                            "language": school.language,
                            "timezone": school.timezone,
                            "currency": school.currency,
                            "date_format": school.date_format,
                            "time_format": school.time_format
                        }
                    }
                
                return None
                
            except Exception as e:
                self.logger.error(f"Error resolving subdomain {subdomain}: {str(e)}")
                return None
    
    def reserve_subdomain(
        self, 
        subdomain: str, 
        school_id: uuid.UUID, 
        user_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """
        Reserve subdomain for a school
        """
        # Check availability first
        availability = self.check_availability(subdomain)
        if not availability["is_available"]:
            return availability
        
        normalized_subdomain = availability["subdomain"]
        
        with get_db_context() as db:
            try:
                # Update school with subdomain
                school = db.query(School).filter(School.id == school_id).first()
                if not school:
                    return {
                        "success": False,
                        "error": "School not found"
                    }
                
                # Store old subdomain for audit
                old_subdomain = school.subdomain
                
                # Update subdomain
                school.subdomain = normalized_subdomain
                school.updated_by = user_id
                
                # Add light audit entry
                if hasattr(school, 'add_light_audit_entry'):
                    school.add_light_audit_entry(
                        action="subdomain_updated",
                        changes={"subdomain": [old_subdomain, normalized_subdomain]},
                        user_id=user_id
                    )
                
                db.commit()
                
                # Log audit event
                log_audit_event(
                    school_id=school_id,
                    module="subdomain",
                    action="subdomain_reserved",
                    user_id=user_id,
                    resource_type="school",
                    resource_id=school_id,
                    event_data={
                        "old_subdomain": old_subdomain,
                        "new_subdomain": normalized_subdomain
                    },
                    severity="info",
                    category="system"
                )
                
                self.logger.info(
                    f"Subdomain reserved successfully",
                    extra={
                        "school_id": str(school_id),
                        "subdomain": normalized_subdomain,
                        "user_id": str(user_id) if user_id else None
                    }
                )
                
                return {
                    "success": True,
                    "subdomain": normalized_subdomain,
                    "message": f"Subdomain '{normalized_subdomain}' reserved successfully"
                }
                
            except Exception as e:
                db.rollback()
                self.logger.error(f"Error reserving subdomain: {str(e)}")
                return {
                    "success": False,
                    "error": "Failed to reserve subdomain. Please try again."
                }
    
    def get_subdomain_suggestions(self, school_name: str, limit: int = 5) -> List[str]:
        """
        Generate subdomain suggestions based on school name
        """
        if not school_name:
            return []
        
        # Clean and normalize school name
        base_name = re.sub(r'[^a-zA-Z0-9\s]', '', school_name.lower())
        words = base_name.split()
        
        suggestions = []
        
        with get_db_context() as db:
            # Try full name (first word + last word if multiple words)
            if len(words) >= 2:
                candidate = f"{words[0]}{words[-1]}"
                if self._is_subdomain_available_in_db(candidate, db):
                    suggestions.append(candidate)
            
            # Try first word only
            if words:
                candidate = words[0]
                if len(candidate) >= 3 and self._is_subdomain_available_in_db(candidate, db):
                    suggestions.append(candidate)
            
            # Try acronym
            if len(words) >= 2:
                acronym = ''.join(word[0] for word in words if word)
                if len(acronym) >= 3 and self._is_subdomain_available_in_db(acronym, db):
                    suggestions.append(acronym)
            
            # Try with common school suffixes
            if words:
                base = words[0]
                for suffix in ["school", "edu", "academy"]:
                    candidate = f"{base}{suffix}"
                    if self._is_subdomain_available_in_db(candidate, db):
                        suggestions.append(candidate)
                        if len(suggestions) >= limit:
                            break
        
        return suggestions[:limit]

    def generate_suggestions(self, base_subdomain: str, limit: int = 5) -> List[str]:
        """
        Generate subdomain suggestions (alias for get_subdomain_suggestions)
        This method exists for backward compatibility with tests
        """
        return self.get_subdomain_suggestions(base_subdomain, limit)


# Global subdomain service instance
subdomain_service = SubdomainService()
