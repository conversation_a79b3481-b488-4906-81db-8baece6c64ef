"""
Authentication service for School ERP
Handles login, logout, token management, and user authentication
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_
import uuid
import secrets

from app.models.auth import User, UserSession, APIKey
from app.core.security import security_manager, password_manager, create_session_id
from app.core.database import get_db_context
from app.core.logging import get_logger
from app.core.localization import get_localization_manager
from app.services.audit_service.audit import log_audit_event, log_security_event

logger = get_logger(__name__)


class AuthenticationError(Exception):
    """Custom exception for authentication errors"""
    pass


class AuthService:
    """Comprehensive authentication service"""
    
    def __init__(self):
        self.security_manager = security_manager
        self.password_manager = password_manager
    
    async def authenticate_user(
        self, 
        email: str, 
        password: str, 
        school_id: uuid.UUID,
        ip_address: str = None,
        user_agent: str = None
    ) -> Dict[str, Any]:
        """
        Authenticate user with comprehensive security checks
        Returns authentication result with tokens and user info
        """
        with get_db_context() as db:
            try:
                # Find user by email and school
                user = db.query(User).filter(
                    and_(
                        User.email == email.lower().strip(),
                        User.school_id == school_id,
                        User.is_deleted == False
                    )
                ).first()
                
                if not user:
                    # Log security event for failed login attempt
                    log_security_event(
                        school_id=school_id,
                        action="login_failed_user_not_found",
                        event_data={"email": email},
                        ip_address=ip_address,
                        user_agent=user_agent,
                        severity="warning"
                    )

                    logger.warning(
                        "Authentication failed - user not found",
                        extra={"email": email, "school_id": str(school_id), "ip_address": ip_address}
                    )
                    raise AuthenticationError("Invalid credentials")
                
                # Check if account is locked
                if user.is_account_locked():
                    logger.warning(
                        "Authentication failed - account locked",
                        extra={"user_id": str(user.id), "ip_address": ip_address}
                    )
                    raise AuthenticationError("Account is temporarily locked due to multiple failed attempts")
                
                # Check if account is active
                if not user.is_active:
                    logger.warning(
                        "Authentication failed - account inactive",
                        extra={"user_id": str(user.id), "ip_address": ip_address}
                    )
                    raise AuthenticationError("Account is inactive")
                
                # Verify password
                if not user.verify_password(password):
                    user.increment_failed_login()
                    db.commit()
                    
                    logger.warning(
                        "Authentication failed - invalid password",
                        extra={
                            "user_id": str(user.id), 
                            "failed_attempts": user.failed_login_attempts,
                            "ip_address": ip_address
                        }
                    )
                    raise AuthenticationError("Invalid credentials")
                
                # Reset failed login attempts on successful authentication
                user.reset_failed_login()
                
                # Create user session
                session = self._create_user_session(
                    user=user,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    db=db
                )
                
                # Get user permissions
                permissions = user.get_permissions()
                
                # Create tokens
                access_token = self.security_manager.create_access_token(
                    subject=user.id,
                    school_id=school_id,
                    permissions=permissions
                )
                
                refresh_token = self.security_manager.create_refresh_token(
                    user_id=user.id,
                    school_id=school_id
                )
                
                # Get localization context
                localization = get_localization_manager(
                    language=user.language,
                    timezone_name=user.timezone,
                    currency=user.currency,
                    date_format=user.date_format,
                    time_format=user.time_format
                )
                
                db.commit()
                
                logger.info(
                    "User authenticated successfully",
                    extra={
                        "user_id": str(user.id),
                        "session_id": session.session_id,
                        "ip_address": ip_address
                    }
                )
                
                return {
                    "success": True,
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "token_type": "bearer",
                    "expires_in": 1800,  # 30 minutes
                    "user": {
                        "id": str(user.id),
                        "email": user.email,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "full_name": user.get_full_name(),
                        "user_type": user.user_type,
                        "is_verified": user.is_verified,
                        "avatar_url": user.avatar_url,
                        "permissions": permissions,
                        "roles": [role.name for role in user.roles if role.is_active]
                    },
                    "school": {
                        "id": str(school_id)
                    },
                    "session": {
                        "id": session.session_id,
                        "expires_at": (datetime.utcnow() + timedelta(hours=24)).isoformat()
                    },
                    "localization": localization.get_localization_context()
                }
                
            except AuthenticationError:
                raise
            except Exception as e:
                logger.error(f"Authentication error: {str(e)}")
                raise AuthenticationError("Authentication failed")
    
    def _create_user_session(
        self, 
        user: User, 
        ip_address: str = None, 
        user_agent: str = None,
        db: Session = None
    ) -> UserSession:
        """Create new user session with security tracking"""
        
        # Check concurrent session limit
        active_sessions = db.query(UserSession).filter(
            and_(
                UserSession.user_id == user.id,
                UserSession.is_active == True,
                UserSession.school_id == user.school_id
            )
        ).count()
        
        if active_sessions >= user.max_concurrent_sessions:
            # Deactivate oldest session
            oldest_session = db.query(UserSession).filter(
                and_(
                    UserSession.user_id == user.id,
                    UserSession.is_active == True,
                    UserSession.school_id == user.school_id
                )
            ).order_by(UserSession.last_activity.asc()).first()
            
            if oldest_session:
                oldest_session.is_active = False
                oldest_session.logout_time = datetime.utcnow()
        
        # Create new session
        session = UserSession(
            user_id=user.id,
            school_id=user.school_id,
            session_id=create_session_id(),
            ip_address=ip_address,
            user_agent=user_agent,
            login_method="password"
        )
        
        db.add(session)
        return session
    
    async def refresh_access_token(
        self, 
        refresh_token: str,
        school_id: uuid.UUID
    ) -> Dict[str, Any]:
        """Refresh access token using valid refresh token"""
        
        # Verify refresh token
        payload = self.security_manager.verify_token(
            refresh_token, 
            token_type=self.security_manager.REFRESH_TOKEN
        )
        
        if not payload:
            raise AuthenticationError("Invalid refresh token")
        
        user_id = uuid.UUID(payload["sub"])
        token_school_id = uuid.UUID(payload["school_id"]) if payload.get("school_id") else None
        
        # Verify school ID matches
        if token_school_id != school_id:
            raise AuthenticationError("Invalid refresh token")
        
        with get_db_context() as db:
            # Get user and verify status
            user = db.query(User).filter(
                and_(
                    User.id == user_id,
                    User.school_id == school_id,
                    User.is_active == True,
                    User.is_deleted == False
                )
            ).first()
            
            if not user:
                raise AuthenticationError("User not found or inactive")
            
            # Get current permissions
            permissions = user.get_permissions()
            
            # Create new access token
            access_token = self.security_manager.create_access_token(
                subject=user.id,
                school_id=school_id,
                permissions=permissions
            )
            
            logger.info(
                "Access token refreshed",
                extra={"user_id": str(user.id), "school_id": str(school_id)}
            )
            
            return {
                "success": True,
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": 1800
            }
    
    async def logout_user(
        self, 
        user_id: uuid.UUID, 
        session_id: str = None,
        access_token: str = None
    ) -> Dict[str, Any]:
        """Logout user and invalidate tokens"""
        
        with get_db_context() as db:
            try:
                # Deactivate user session
                if session_id:
                    session = db.query(UserSession).filter(
                        and_(
                            UserSession.user_id == user_id,
                            UserSession.session_id == session_id,
                            UserSession.is_active == True
                        )
                    ).first()
                    
                    if session:
                        session.is_active = False
                        session.logout_time = datetime.utcnow()
                
                # Blacklist access token if provided
                if access_token:
                    payload = self.security_manager.verify_token(access_token)
                    if payload and payload.get("jti"):
                        self.security_manager.blacklist_token(payload["jti"])
                
                db.commit()
                
                logger.info(
                    "User logged out",
                    extra={"user_id": str(user_id), "session_id": session_id}
                )
                
                return {"success": True, "message": "Logged out successfully"}
                
            except Exception as e:
                logger.error(f"Logout error: {str(e)}")
                return {"success": False, "message": "Logout failed"}
    
    async def logout_all_sessions(self, user_id: uuid.UUID) -> Dict[str, Any]:
        """Logout user from all sessions"""
        
        with get_db_context() as db:
            try:
                # Deactivate all user sessions
                db.query(UserSession).filter(
                    and_(
                        UserSession.user_id == user_id,
                        UserSession.is_active == True
                    )
                ).update({
                    "is_active": False,
                    "logout_time": datetime.utcnow()
                })
                
                # Revoke all refresh tokens
                self.security_manager.revoke_all_user_tokens(user_id)
                
                db.commit()
                
                logger.info(
                    "All user sessions logged out",
                    extra={"user_id": str(user_id)}
                )
                
                return {"success": True, "message": "Logged out from all sessions"}
                
            except Exception as e:
                logger.error(f"Logout all sessions error: {str(e)}")
                return {"success": False, "message": "Logout failed"}
    
    async def change_password(
        self, 
        user_id: uuid.UUID, 
        current_password: str, 
        new_password: str
    ) -> Dict[str, Any]:
        """Change user password with validation"""
        
        with get_db_context() as db:
            try:
                user = db.query(User).filter(User.id == user_id).first()
                
                if not user:
                    raise AuthenticationError("User not found")
                
                # Verify current password
                if not user.verify_password(current_password):
                    raise AuthenticationError("Current password is incorrect")
                
                # Validate new password strength
                validation = self.password_manager.validate_password_strength(new_password)
                if not validation["is_valid"]:
                    return {
                        "success": False,
                        "message": "Password does not meet requirements",
                        "issues": validation["issues"]
                    }
                
                # Set new password
                user.set_password(new_password)
                user.force_password_change = False
                
                # Logout from all other sessions for security
                await self.logout_all_sessions(user_id)
                
                db.commit()
                
                logger.info(
                    "Password changed successfully",
                    extra={"user_id": str(user_id)}
                )
                
                return {"success": True, "message": "Password changed successfully"}
                
            except AuthenticationError as e:
                return {"success": False, "message": str(e)}
            except Exception as e:
                logger.error(f"Password change error: {str(e)}")
                return {"success": False, "message": "Password change failed"}


# Global auth service instance
auth_service = AuthService()
