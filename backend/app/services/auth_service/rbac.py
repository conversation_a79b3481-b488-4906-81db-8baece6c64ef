"""
RBAC Service for School ERP
Comprehensive Role-Based Access Control management
Handles roles, permissions, assignments, and permission checking
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Set, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, text
import uuid
import json

from app.models.auth import User, Role, Permission, UserRole, RolePermission
from app.models.base import School
from app.core.database import get_db_context
from app.core.logging import get_logger
from app.core.config import settings
from app.services.audit_service.audit import log_audit_event, log_security_event
import redis

logger = get_logger(__name__)

# Redis client for caching
redis_client = redis.from_url(settings.redis_url)


class RBACError(Exception):
    """Custom exception for RBAC operations"""
    pass


class PermissionDeniedError(RBACError):
    """Exception for permission denied scenarios"""
    pass


class RBACService:
    """
    Comprehensive RBAC service for role and permission management
    Provides caching, validation, and audit logging for all RBAC operations
    """
    
    # Cache TTL settings
    PERMISSION_CACHE_TTL = 3600  # 1 hour
    ROLE_CACHE_TTL = 1800       # 30 minutes
    USER_PERMISSION_CACHE_TTL = 900  # 15 minutes
    
    # Permission categories and their allowed actions
    PERMISSION_CATEGORIES = {
        "admin": ["all", "manage", "view"],
        "user": ["create", "read", "update", "delete", "list", "manage"],
        "student": ["create", "read", "update", "delete", "list", "manage", "admit", "promote"],
        "academic": ["create", "read", "update", "delete", "list", "manage"],
        "fee": ["create", "read", "update", "delete", "list", "manage", "collect", "refund"],
        "attendance": ["create", "read", "update", "delete", "list", "manage", "mark"],
        "report": ["generate", "view", "export", "share"],
        "communication": ["send", "read", "manage"],
        "settings": ["read", "update", "manage"]
    }
    
    def __init__(self):
        self.logger = logger
    
    # ==================== ROLE MANAGEMENT ====================
    
    def create_role(
        self,
        school_id: uuid.UUID,
        name: str,
        display_name: str,
        description: Optional[str] = None,
        parent_role_id: Optional[uuid.UUID] = None,
        level: int = 0,
        scope: str = "school",
        is_default: bool = False,
        created_by: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """
        Create a new role with comprehensive validation
        
        Args:
            school_id: School identifier
            name: Role name (unique within school)
            display_name: Human-readable role name
            description: Role description
            parent_role_id: Parent role for hierarchy
            level: Hierarchy level
            scope: Role scope (school, organization, system)
            is_default: Whether role is auto-assigned to new users
            created_by: User creating the role
            
        Returns:
            Dict containing success status and role data
        """
        with get_db_context() as db:
            try:
                # Validate role name uniqueness within school
                existing_role = db.query(Role).filter(
                    and_(
                        Role.school_id == school_id,
                        Role.name == name.lower().strip(),
                        Role.is_deleted == False
                    )
                ).first()
                
                if existing_role:
                    raise RBACError(f"Role with name '{name}' already exists in this school")
                
                # Validate parent role if specified
                parent_role = None
                if parent_role_id:
                    parent_role = db.query(Role).filter(
                        and_(
                            Role.id == parent_role_id,
                            Role.school_id == school_id,
                            Role.is_active == True,
                            Role.is_deleted == False
                        )
                    ).first()
                    
                    if not parent_role:
                        raise RBACError("Parent role not found or inactive")
                    
                    # Prevent circular hierarchy
                    if self._would_create_circular_hierarchy(parent_role, school_id, db):
                        raise RBACError("Cannot create circular role hierarchy")
                
                # Create new role
                role = Role(
                    school_id=school_id,
                    name=name.lower().strip(),
                    display_name=display_name.strip(),
                    description=description,
                    parent_role_id=parent_role_id,
                    level=level,
                    scope=scope,
                    is_default=is_default,
                    is_active=True,
                    is_system_role=False,
                    created_by=created_by,
                    updated_by=created_by
                )
                
                db.add(role)
                db.commit()
                db.refresh(role)
                
                # Clear role cache
                self._clear_role_cache(school_id)
                
                # Log audit event
                log_audit_event(
                    school_id=school_id,
                    user_id=created_by,
                    action="role_created",
                    resource_type="role",
                    resource_id=role.id,
                    details={
                        "role_name": role.name,
                        "display_name": role.display_name,
                        "parent_role_id": str(parent_role_id) if parent_role_id else None,
                        "level": level
                    }
                )
                
                self.logger.info(f"Role created successfully: {role.name} in school {school_id}")
                
                return {
                    "success": True,
                    "message": "Role created successfully",
                    "role": self._role_to_dict(role)
                }
                
            except Exception as e:
                db.rollback()
                self.logger.error(f"Error creating role: {str(e)}")
                raise RBACError(f"Failed to create role: {str(e)}")
    
    def get_role(
        self,
        school_id: uuid.UUID,
        role_id: uuid.UUID,
        include_permissions: bool = False
    ) -> Optional[Dict[str, Any]]:
        """
        Get role by ID with optional permission details
        
        Args:
            school_id: School identifier
            role_id: Role identifier
            include_permissions: Whether to include role permissions
            
        Returns:
            Role data dictionary or None if not found
        """
        with get_db_context() as db:
            query = db.query(Role).filter(
                and_(
                    Role.id == role_id,
                    Role.school_id == school_id,
                    Role.is_deleted == False
                )
            )
            
            if include_permissions:
                query = query.options(
                    joinedload(Role.role_permissions).joinedload(RolePermission.permission)
                )
            
            role = query.first()
            
            if not role:
                return None
            
            role_data = self._role_to_dict(role)
            
            if include_permissions:
                role_data["permissions"] = [
                    self._permission_to_dict(rp.permission)
                    for rp in role.role_permissions
                    if rp.is_active and rp.permission.is_active
                ]
            
            return role_data
    
    def list_roles(
        self,
        school_id: uuid.UUID,
        include_inactive: bool = False,
        include_system: bool = True,
        parent_role_id: Optional[uuid.UUID] = None,
        page: int = 1,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """
        List roles with filtering and pagination
        
        Args:
            school_id: School identifier
            include_inactive: Whether to include inactive roles
            include_system: Whether to include system roles
            parent_role_id: Filter by parent role
            page: Page number (1-based)
            page_size: Number of roles per page
            
        Returns:
            Paginated list of roles
        """
        with get_db_context() as db:
            query = db.query(Role).filter(
                and_(
                    Role.school_id == school_id,
                    Role.is_deleted == False
                )
            )
            
            if not include_inactive:
                query = query.filter(Role.is_active == True)
            
            if not include_system:
                query = query.filter(Role.is_system_role == False)
            
            if parent_role_id:
                query = query.filter(Role.parent_role_id == parent_role_id)
            
            # Get total count
            total_count = query.count()
            
            # Apply pagination
            offset = (page - 1) * page_size
            roles = query.order_by(Role.level, Role.name).offset(offset).limit(page_size).all()
            
            return {
                "roles": [self._role_to_dict(role) for role in roles],
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": (total_count + page_size - 1) // page_size
                }
            }

    def update_role(
        self,
        school_id: uuid.UUID,
        role_id: uuid.UUID,
        display_name: Optional[str] = None,
        description: Optional[str] = None,
        parent_role_id: Optional[uuid.UUID] = None,
        level: Optional[int] = None,
        is_active: Optional[bool] = None,
        is_default: Optional[bool] = None,
        updated_by: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """
        Update role with validation and audit logging

        Args:
            school_id: School identifier
            role_id: Role identifier
            display_name: New display name
            description: New description
            parent_role_id: New parent role
            level: New hierarchy level
            is_active: New active status
            is_default: New default status
            updated_by: User making the update

        Returns:
            Dict containing success status and updated role data
        """
        with get_db_context() as db:
            try:
                # Get existing role
                role = db.query(Role).filter(
                    and_(
                        Role.id == role_id,
                        Role.school_id == school_id,
                        Role.is_deleted == False
                    )
                ).first()

                if not role:
                    raise RBACError("Role not found")

                # Prevent modification of system roles (except activation)
                if role.is_system_role and (display_name or description or parent_role_id or level):
                    raise RBACError("Cannot modify system role properties")

                # Store original values for audit
                original_values = {
                    "display_name": role.display_name,
                    "description": role.description,
                    "parent_role_id": str(role.parent_role_id) if role.parent_role_id else None,
                    "level": role.level,
                    "is_active": role.is_active,
                    "is_default": role.is_default
                }

                # Validate parent role if changing
                if parent_role_id is not None and parent_role_id != role.parent_role_id:
                    if parent_role_id:
                        parent_role = db.query(Role).filter(
                            and_(
                                Role.id == parent_role_id,
                                Role.school_id == school_id,
                                Role.is_active == True,
                                Role.is_deleted == False
                            )
                        ).first()

                        if not parent_role:
                            raise RBACError("Parent role not found or inactive")

                        # Prevent circular hierarchy
                        if self._would_create_circular_hierarchy(parent_role, school_id, db, exclude_role_id=role_id):
                            raise RBACError("Cannot create circular role hierarchy")

                # Update role fields
                if display_name is not None:
                    role.display_name = display_name.strip()
                if description is not None:
                    role.description = description
                if parent_role_id is not None:
                    role.parent_role_id = parent_role_id
                if level is not None:
                    role.level = level
                if is_active is not None:
                    role.is_active = is_active
                if is_default is not None:
                    role.is_default = is_default

                role.updated_by = updated_by
                role.updated_at = datetime.utcnow()

                db.commit()
                db.refresh(role)

                # Clear caches
                self._clear_role_cache(school_id)
                self._clear_user_permission_cache(school_id)

                # Log audit event
                changes = {}
                for key, old_value in original_values.items():
                    new_value = getattr(role, key)
                    if key == "parent_role_id":
                        new_value = str(new_value) if new_value else None
                    if old_value != new_value:
                        changes[key] = {"old": old_value, "new": new_value}

                if changes:
                    log_audit_event(
                        school_id=school_id,
                        user_id=updated_by,
                        action="role_updated",
                        resource_type="role",
                        resource_id=role.id,
                        details={
                            "role_name": role.name,
                            "changes": changes
                        }
                    )

                self.logger.info(f"Role updated successfully: {role.name} in school {school_id}")

                return {
                    "success": True,
                    "message": "Role updated successfully",
                    "role": self._role_to_dict(role)
                }

            except Exception as e:
                db.rollback()
                self.logger.error(f"Error updating role: {str(e)}")
                raise RBACError(f"Failed to update role: {str(e)}")

    def delete_role(
        self,
        school_id: uuid.UUID,
        role_id: uuid.UUID,
        deleted_by: Optional[uuid.UUID] = None,
        force: bool = False
    ) -> Dict[str, Any]:
        """
        Delete role with dependency checking

        Args:
            school_id: School identifier
            role_id: Role identifier
            deleted_by: User performing deletion
            force: Whether to force deletion (reassign users)

        Returns:
            Dict containing success status and message
        """
        with get_db_context() as db:
            try:
                # Get role
                role = db.query(Role).filter(
                    and_(
                        Role.id == role_id,
                        Role.school_id == school_id,
                        Role.is_deleted == False
                    )
                ).first()

                if not role:
                    raise RBACError("Role not found")

                # Prevent deletion of system roles
                if role.is_system_role:
                    raise RBACError("Cannot delete system role")

                # Check for active user assignments
                active_users = db.query(UserRole).filter(
                    and_(
                        UserRole.role_id == role_id,
                        UserRole.is_active == True
                    )
                ).count()

                if active_users > 0 and not force:
                    raise RBACError(f"Cannot delete role: {active_users} users are assigned to this role")

                # Check for child roles
                child_roles = db.query(Role).filter(
                    and_(
                        Role.parent_role_id == role_id,
                        Role.is_active == True,
                        Role.is_deleted == False
                    )
                ).count()

                if child_roles > 0:
                    raise RBACError(f"Cannot delete role: {child_roles} child roles depend on this role")

                # If force deletion, deactivate user assignments
                if force and active_users > 0:
                    db.query(UserRole).filter(
                        UserRole.role_id == role_id
                    ).update({"is_active": False})

                # Soft delete the role
                role.is_deleted = True
                role.deleted_at = datetime.utcnow()
                role.deleted_by = deleted_by
                role.is_active = False

                db.commit()

                # Clear caches
                self._clear_role_cache(school_id)
                self._clear_user_permission_cache(school_id)

                # Log audit event
                log_audit_event(
                    school_id=school_id,
                    user_id=deleted_by,
                    action="role_deleted",
                    resource_type="role",
                    resource_id=role.id,
                    details={
                        "role_name": role.name,
                        "force_deletion": force,
                        "affected_users": active_users
                    }
                )

                self.logger.info(f"Role deleted successfully: {role.name} in school {school_id}")

                return {
                    "success": True,
                    "message": f"Role '{role.display_name}' deleted successfully"
                }

            except Exception as e:
                db.rollback()
                self.logger.error(f"Error deleting role: {str(e)}")
                raise RBACError(f"Failed to delete role: {str(e)}")

    # ==================== PERMISSION MANAGEMENT ====================

    def create_permission(
        self,
        school_id: uuid.UUID,
        code: str,
        name: str,
        category: str,
        resource: str,
        action: str,
        description: Optional[str] = None,
        scope: str = "school",
        created_by: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """
        Create a new permission with validation

        Args:
            school_id: School identifier
            code: Unique permission code
            name: Human-readable permission name
            category: Permission category (admin, user, student, etc.)
            resource: Resource type (user, student, fee, etc.)
            action: Action type (create, read, update, delete, etc.)
            description: Permission description
            scope: Permission scope (school, organization, system)
            created_by: User creating the permission

        Returns:
            Dict containing success status and permission data
        """
        with get_db_context() as db:
            try:
                # Validate permission code uniqueness
                existing_permission = db.query(Permission).filter(
                    Permission.code == code.lower().strip()
                ).first()

                if existing_permission:
                    raise RBACError(f"Permission with code '{code}' already exists")

                # Validate category and action
                if category not in self.PERMISSION_CATEGORIES:
                    raise RBACError(f"Invalid category '{category}'. Allowed: {list(self.PERMISSION_CATEGORIES.keys())}")

                if action not in self.PERMISSION_CATEGORIES[category]:
                    raise RBACError(f"Invalid action '{action}' for category '{category}'. Allowed: {self.PERMISSION_CATEGORIES[category]}")

                # Create permission
                permission = Permission(
                    school_id=school_id,
                    code=code.lower().strip(),
                    name=name.strip(),
                    description=description,
                    category=category,
                    resource=resource.lower().strip(),
                    action=action.lower().strip(),
                    scope=scope,
                    is_active=True,
                    is_system_permission=False,
                    created_by=created_by,
                    updated_by=created_by
                )

                db.add(permission)
                db.commit()
                db.refresh(permission)

                # Clear permission cache
                self._clear_permission_cache(school_id)

                # Log audit event
                log_audit_event(
                    school_id=school_id,
                    user_id=created_by,
                    action="permission_created",
                    resource_type="permission",
                    resource_id=permission.id,
                    details={
                        "permission_code": permission.code,
                        "category": permission.category,
                        "resource": permission.resource,
                        "action": permission.action
                    }
                )

                self.logger.info(f"Permission created successfully: {permission.code} in school {school_id}")

                return {
                    "success": True,
                    "message": "Permission created successfully",
                    "permission": self._permission_to_dict(permission)
                }

            except Exception as e:
                db.rollback()
                self.logger.error(f"Error creating permission: {str(e)}")
                raise RBACError(f"Failed to create permission: {str(e)}")

    def get_permission(
        self,
        school_id: uuid.UUID,
        permission_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """
        Get permission by ID

        Args:
            school_id: School identifier
            permission_id: Permission identifier

        Returns:
            Permission data dictionary or None if not found
        """
        with get_db_context() as db:
            permission = db.query(Permission).filter(
                and_(
                    Permission.id == permission_id,
                    Permission.school_id == school_id,
                    Permission.is_deleted == False
                )
            ).first()

            if not permission:
                return None

            return self._permission_to_dict(permission)

    def list_permissions(
        self,
        school_id: uuid.UUID,
        category: Optional[str] = None,
        resource: Optional[str] = None,
        action: Optional[str] = None,
        include_inactive: bool = False,
        include_system: bool = True,
        page: int = 1,
        page_size: int = 100
    ) -> Dict[str, Any]:
        """
        List permissions with filtering and pagination

        Args:
            school_id: School identifier
            category: Filter by category
            resource: Filter by resource
            action: Filter by action
            include_inactive: Whether to include inactive permissions
            include_system: Whether to include system permissions
            page: Page number (1-based)
            page_size: Number of permissions per page

        Returns:
            Paginated list of permissions
        """
        with get_db_context() as db:
            query = db.query(Permission).filter(
                and_(
                    Permission.school_id == school_id,
                    Permission.is_deleted == False
                )
            )

            if category:
                query = query.filter(Permission.category == category)
            if resource:
                query = query.filter(Permission.resource == resource)
            if action:
                query = query.filter(Permission.action == action)
            if not include_inactive:
                query = query.filter(Permission.is_active == True)
            if not include_system:
                query = query.filter(Permission.is_system_permission == False)

            # Get total count
            total_count = query.count()

            # Apply pagination
            offset = (page - 1) * page_size
            permissions = query.order_by(Permission.category, Permission.resource, Permission.action).offset(offset).limit(page_size).all()

            return {
                "permissions": [self._permission_to_dict(permission) for permission in permissions],
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": (total_count + page_size - 1) // page_size
                }
            }

    # ==================== ROLE-PERMISSION ASSIGNMENT ====================

    def assign_permission_to_role(
        self,
        school_id: uuid.UUID,
        role_id: uuid.UUID,
        permission_id: uuid.UUID,
        assigned_by: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """
        Assign permission to role

        Args:
            school_id: School identifier
            role_id: Role identifier
            permission_id: Permission identifier
            assigned_by: User making the assignment

        Returns:
            Dict containing success status and message
        """
        with get_db_context() as db:
            try:
                # Validate role exists and is active
                role = db.query(Role).filter(
                    and_(
                        Role.id == role_id,
                        Role.school_id == school_id,
                        Role.is_active == True,
                        Role.is_deleted == False
                    )
                ).first()

                if not role:
                    raise RBACError("Role not found or inactive")

                # Validate permission exists and is active
                permission = db.query(Permission).filter(
                    and_(
                        Permission.id == permission_id,
                        Permission.school_id == school_id,
                        Permission.is_active == True,
                        Permission.is_deleted == False
                    )
                ).first()

                if not permission:
                    raise RBACError("Permission not found or inactive")

                # Check if assignment already exists
                existing_assignment = db.query(RolePermission).filter(
                    and_(
                        RolePermission.role_id == role_id,
                        RolePermission.permission_id == permission_id
                    )
                ).first()

                if existing_assignment:
                    if existing_assignment.is_active:
                        return {
                            "success": True,
                            "message": "Permission already assigned to role"
                        }
                    else:
                        # Reactivate existing assignment
                        existing_assignment.is_active = True
                        existing_assignment.assigned_by = assigned_by
                        existing_assignment.assigned_at = datetime.utcnow()
                else:
                    # Create new assignment
                    role_permission = RolePermission(
                        role_id=role_id,
                        permission_id=permission_id,
                        assigned_by=assigned_by,
                        is_active=True
                    )
                    db.add(role_permission)

                db.commit()

                # Clear caches
                self._clear_role_cache(school_id)
                self._clear_user_permission_cache(school_id)

                # Log audit event
                log_audit_event(
                    school_id=school_id,
                    user_id=assigned_by,
                    action="permission_assigned_to_role",
                    resource_type="role_permission",
                    resource_id=role.id,
                    details={
                        "role_name": role.name,
                        "permission_code": permission.code,
                        "permission_name": permission.name
                    }
                )

                self.logger.info(f"Permission {permission.code} assigned to role {role.name} in school {school_id}")

                return {
                    "success": True,
                    "message": f"Permission '{permission.name}' assigned to role '{role.display_name}'"
                }

            except Exception as e:
                db.rollback()
                self.logger.error(f"Error assigning permission to role: {str(e)}")
                raise RBACError(f"Failed to assign permission to role: {str(e)}")

    def remove_permission_from_role(
        self,
        school_id: uuid.UUID,
        role_id: uuid.UUID,
        permission_id: uuid.UUID,
        removed_by: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """
        Remove permission from role

        Args:
            school_id: School identifier
            role_id: Role identifier
            permission_id: Permission identifier
            removed_by: User removing the assignment

        Returns:
            Dict containing success status and message
        """
        with get_db_context() as db:
            try:
                # Get role and permission for validation and logging
                role = db.query(Role).filter(
                    and_(
                        Role.id == role_id,
                        Role.school_id == school_id,
                        Role.is_deleted == False
                    )
                ).first()

                permission = db.query(Permission).filter(
                    and_(
                        Permission.id == permission_id,
                        Permission.school_id == school_id,
                        Permission.is_deleted == False
                    )
                ).first()

                if not role or not permission:
                    raise RBACError("Role or permission not found")

                # Find and deactivate assignment
                assignment = db.query(RolePermission).filter(
                    and_(
                        RolePermission.role_id == role_id,
                        RolePermission.permission_id == permission_id,
                        RolePermission.is_active == True
                    )
                ).first()

                if not assignment:
                    return {
                        "success": True,
                        "message": "Permission not assigned to role"
                    }

                assignment.is_active = False
                db.commit()

                # Clear caches
                self._clear_role_cache(school_id)
                self._clear_user_permission_cache(school_id)

                # Log audit event
                log_audit_event(
                    school_id=school_id,
                    user_id=removed_by,
                    action="permission_removed_from_role",
                    resource_type="role_permission",
                    resource_id=role.id,
                    details={
                        "role_name": role.name,
                        "permission_code": permission.code,
                        "permission_name": permission.name
                    }
                )

                self.logger.info(f"Permission {permission.code} removed from role {role.name} in school {school_id}")

                return {
                    "success": True,
                    "message": f"Permission '{permission.name}' removed from role '{role.display_name}'"
                }

            except Exception as e:
                db.rollback()
                self.logger.error(f"Error removing permission from role: {str(e)}")
                raise RBACError(f"Failed to remove permission from role: {str(e)}")

    # ==================== USER-ROLE ASSIGNMENT ====================

    def assign_role_to_user(
        self,
        school_id: uuid.UUID,
        user_id: uuid.UUID,
        role_id: uuid.UUID,
        assigned_by: Optional[uuid.UUID] = None,
        expires_at: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Assign role to user

        Args:
            school_id: School identifier
            user_id: User identifier
            role_id: Role identifier
            assigned_by: User making the assignment
            expires_at: Optional expiration date for the assignment

        Returns:
            Dict containing success status and message
        """
        with get_db_context() as db:
            try:
                # Validate user exists and is active
                user = db.query(User).filter(
                    and_(
                        User.id == user_id,
                        User.school_id == school_id,
                        User.is_active == True,
                        User.is_deleted == False
                    )
                ).first()

                if not user:
                    raise RBACError("User not found or inactive")

                # Validate role exists and is active
                role = db.query(Role).filter(
                    and_(
                        Role.id == role_id,
                        Role.school_id == school_id,
                        Role.is_active == True,
                        Role.is_deleted == False
                    )
                ).first()

                if not role:
                    raise RBACError("Role not found or inactive")

                # Check if assignment already exists
                existing_assignment = db.query(UserRole).filter(
                    and_(
                        UserRole.user_id == user_id,
                        UserRole.role_id == role_id
                    )
                ).first()

                if existing_assignment:
                    if existing_assignment.is_active and not existing_assignment.is_expired():
                        return {
                            "success": True,
                            "message": "Role already assigned to user"
                        }
                    else:
                        # Reactivate existing assignment
                        existing_assignment.is_active = True
                        existing_assignment.assigned_by = assigned_by
                        existing_assignment.assigned_at = datetime.utcnow()
                        existing_assignment.expires_at = expires_at
                else:
                    # Create new assignment
                    user_role = UserRole(
                        user_id=user_id,
                        role_id=role_id,
                        assigned_by=assigned_by,
                        expires_at=expires_at,
                        is_active=True
                    )
                    db.add(user_role)

                db.commit()

                # Clear user permission cache
                self._clear_user_permission_cache(school_id, user_id)

                # Log audit event
                log_audit_event(
                    school_id=school_id,
                    user_id=assigned_by,
                    action="role_assigned_to_user",
                    resource_type="user_role",
                    resource_id=user.id,
                    details={
                        "user_email": user.email,
                        "role_name": role.name,
                        "expires_at": expires_at.isoformat() if expires_at else None
                    }
                )

                self.logger.info(f"Role {role.name} assigned to user {user.email} in school {school_id}")

                return {
                    "success": True,
                    "message": f"Role '{role.display_name}' assigned to user '{user.get_full_name()}'"
                }

            except Exception as e:
                db.rollback()
                self.logger.error(f"Error assigning role to user: {str(e)}")
                raise RBACError(f"Failed to assign role to user: {str(e)}")

    def remove_role_from_user(
        self,
        school_id: uuid.UUID,
        user_id: uuid.UUID,
        role_id: uuid.UUID,
        removed_by: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """
        Remove role from user

        Args:
            school_id: School identifier
            user_id: User identifier
            role_id: Role identifier
            removed_by: User removing the assignment

        Returns:
            Dict containing success status and message
        """
        with get_db_context() as db:
            try:
                # Get user and role for validation and logging
                user = db.query(User).filter(
                    and_(
                        User.id == user_id,
                        User.school_id == school_id,
                        User.is_deleted == False
                    )
                ).first()

                role = db.query(Role).filter(
                    and_(
                        Role.id == role_id,
                        Role.school_id == school_id,
                        Role.is_deleted == False
                    )
                ).first()

                if not user or not role:
                    raise RBACError("User or role not found")

                # Find and deactivate assignment
                assignment = db.query(UserRole).filter(
                    and_(
                        UserRole.user_id == user_id,
                        UserRole.role_id == role_id,
                        UserRole.is_active == True
                    )
                ).first()

                if not assignment:
                    return {
                        "success": True,
                        "message": "Role not assigned to user"
                    }

                assignment.is_active = False
                db.commit()

                # Clear user permission cache
                self._clear_user_permission_cache(school_id, user_id)

                # Log audit event
                log_audit_event(
                    school_id=school_id,
                    user_id=removed_by,
                    action="role_removed_from_user",
                    resource_type="user_role",
                    resource_id=user.id,
                    details={
                        "user_email": user.email,
                        "role_name": role.name
                    }
                )

                self.logger.info(f"Role {role.name} removed from user {user.email} in school {school_id}")

                return {
                    "success": True,
                    "message": f"Role '{role.display_name}' removed from user '{user.get_full_name()}'"
                }

            except Exception as e:
                db.rollback()
                self.logger.error(f"Error removing role from user: {str(e)}")
                raise RBACError(f"Failed to remove role from user: {str(e)}")

    # ==================== PERMISSION CHECKING ====================

    def check_user_permission(
        self,
        school_id: uuid.UUID,
        user_id: uuid.UUID,
        permission_code: str,
        use_cache: bool = True
    ) -> bool:
        """
        Check if user has specific permission

        Args:
            school_id: School identifier
            user_id: User identifier
            permission_code: Permission code to check
            use_cache: Whether to use cached permissions

        Returns:
            True if user has permission, False otherwise
        """
        try:
            user_permissions = self.get_user_permissions(school_id, user_id, use_cache)
            return permission_code in user_permissions
        except Exception as e:
            self.logger.error(f"Error checking user permission: {str(e)}")
            return False

    def get_user_permissions(
        self,
        school_id: uuid.UUID,
        user_id: uuid.UUID,
        use_cache: bool = True
    ) -> List[str]:
        """
        Get all permissions for a user with caching

        Args:
            school_id: School identifier
            user_id: User identifier
            use_cache: Whether to use cached permissions

        Returns:
            List of permission codes
        """
        cache_key = f"erp:{school_id}:user:{user_id}:permissions"

        if use_cache:
            try:
                cached_permissions = redis_client.get(cache_key)
                if cached_permissions:
                    return json.loads(cached_permissions)
            except Exception as e:
                self.logger.warning(f"Error reading from cache: {str(e)}")

        # Get permissions from database
        with get_db_context() as db:
            user = db.query(User).filter(
                and_(
                    User.id == user_id,
                    User.school_id == school_id,
                    User.is_active == True,
                    User.is_deleted == False
                )
            ).options(
                joinedload(User.user_roles).joinedload(UserRole.role).joinedload(Role.role_permissions).joinedload(RolePermission.permission)
            ).first()

            if not user:
                return []

            permissions = user.get_permissions()

            # Cache permissions
            if use_cache:
                try:
                    redis_client.setex(
                        cache_key,
                        self.USER_PERMISSION_CACHE_TTL,
                        json.dumps(permissions)
                    )
                except Exception as e:
                    self.logger.warning(f"Error writing to cache: {str(e)}")

            return permissions

    def get_role_permissions(
        self,
        school_id: uuid.UUID,
        role_id: uuid.UUID,
        include_inherited: bool = True
    ) -> List[str]:
        """
        Get all permissions for a role

        Args:
            school_id: School identifier
            role_id: Role identifier
            include_inherited: Whether to include inherited permissions

        Returns:
            List of permission codes
        """
        with get_db_context() as db:
            role = db.query(Role).filter(
                and_(
                    Role.id == role_id,
                    Role.school_id == school_id,
                    Role.is_active == True,
                    Role.is_deleted == False
                )
            ).options(
                joinedload(Role.role_permissions).joinedload(RolePermission.permission)
            ).first()

            if not role:
                return []

            if include_inherited:
                return role.get_all_permissions()
            else:
                return [
                    rp.permission.code
                    for rp in role.role_permissions
                    if rp.is_active and rp.permission.is_active
                ]

    def get_users_with_permission(
        self,
        school_id: uuid.UUID,
        permission_code: str,
        include_inactive: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Get all users who have a specific permission

        Args:
            school_id: School identifier
            permission_code: Permission code to search for
            include_inactive: Whether to include inactive users

        Returns:
            List of user data dictionaries
        """
        with get_db_context() as db:
            query = db.query(User).join(UserRole).join(Role).join(RolePermission).join(Permission).filter(
                and_(
                    User.school_id == school_id,
                    User.is_deleted == False,
                    UserRole.is_active == True,
                    Role.is_active == True,
                    Role.is_deleted == False,
                    RolePermission.is_active == True,
                    Permission.code == permission_code,
                    Permission.is_active == True,
                    Permission.is_deleted == False
                )
            )

            if not include_inactive:
                query = query.filter(User.is_active == True)

            users = query.distinct().all()

            return [
                {
                    "id": str(user.id),
                    "email": user.email,
                    "full_name": user.get_full_name(),
                    "user_type": user.user_type,
                    "is_active": user.is_active
                }
                for user in users
            ]

    # ==================== HELPER METHODS ====================

    def _role_to_dict(self, role: Role) -> Dict[str, Any]:
        """Convert Role model to dictionary"""
        return {
            "id": str(role.id),
            "name": role.name,
            "display_name": role.display_name,
            "description": role.description,
            "level": role.level,
            "scope": role.scope,
            "is_active": role.is_active,
            "is_system_role": role.is_system_role,
            "is_default": role.is_default,
            "parent_role_id": str(role.parent_role_id) if role.parent_role_id else None,
            "created_at": role.created_at.isoformat() if role.created_at else None,
            "updated_at": role.updated_at.isoformat() if role.updated_at else None
        }

    def _permission_to_dict(self, permission: Permission) -> Dict[str, Any]:
        """Convert Permission model to dictionary"""
        return {
            "id": str(permission.id),
            "code": permission.code,
            "name": permission.name,
            "description": permission.description,
            "category": permission.category,
            "resource": permission.resource,
            "action": permission.action,
            "scope": permission.scope,
            "is_active": permission.is_active,
            "is_system_permission": permission.is_system_permission,
            "created_at": permission.created_at.isoformat() if permission.created_at else None,
            "updated_at": permission.updated_at.isoformat() if permission.updated_at else None
        }

    def _would_create_circular_hierarchy(
        self,
        parent_role: Role,
        school_id: uuid.UUID,
        db: Session,
        exclude_role_id: Optional[uuid.UUID] = None
    ) -> bool:
        """Check if adding parent role would create circular hierarchy"""
        visited = set()
        current_role = parent_role

        while current_role:
            if current_role.id in visited:
                return True

            if exclude_role_id and current_role.id == exclude_role_id:
                return True

            visited.add(current_role.id)

            if current_role.parent_role_id:
                current_role = db.query(Role).filter(
                    and_(
                        Role.id == current_role.parent_role_id,
                        Role.school_id == school_id,
                        Role.is_deleted == False
                    )
                ).first()
            else:
                current_role = None

        return False

    # ==================== CACHE MANAGEMENT ====================

    def _clear_role_cache(self, school_id: uuid.UUID) -> None:
        """Clear role-related cache entries"""
        try:
            pattern = f"erp:{school_id}:role:*"
            keys = redis_client.keys(pattern)
            if keys:
                redis_client.delete(*keys)
        except Exception as e:
            self.logger.warning(f"Error clearing role cache: {str(e)}")

    def _clear_permission_cache(self, school_id: uuid.UUID) -> None:
        """Clear permission-related cache entries"""
        try:
            pattern = f"erp:{school_id}:permission:*"
            keys = redis_client.keys(pattern)
            if keys:
                redis_client.delete(*keys)
        except Exception as e:
            self.logger.warning(f"Error clearing permission cache: {str(e)}")

    def _clear_user_permission_cache(
        self,
        school_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None
    ) -> None:
        """Clear user permission cache entries"""
        try:
            if user_id:
                # Clear specific user's cache
                cache_key = f"erp:{school_id}:user:{user_id}:permissions"
                redis_client.delete(cache_key)
            else:
                # Clear all user permission caches for school
                pattern = f"erp:{school_id}:user:*:permissions"
                keys = redis_client.keys(pattern)
                if keys:
                    redis_client.delete(*keys)
        except Exception as e:
            self.logger.warning(f"Error clearing user permission cache: {str(e)}")

    def clear_all_rbac_cache(self, school_id: uuid.UUID) -> None:
        """Clear all RBAC-related cache entries for a school"""
        self._clear_role_cache(school_id)
        self._clear_permission_cache(school_id)
        self._clear_user_permission_cache(school_id)
        self.logger.info(f"Cleared all RBAC cache for school {school_id}")


# Create service instance
rbac_service = RBACService()
