"""
Audit service for School ERP
Centralized audit logging and compliance management
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
import uuid

from app.models.audit import AuditLog, DataRetentionPolicy, ArchivedData
from app.core.database import get_db_context
from app.core.logging import get_logger

logger = get_logger(__name__)


class AuditService:
    """Centralized audit logging and compliance service"""
    
    # Critical modules that require global audit logging
    CRITICAL_MODULES = {
        "auth", "user", "role", "permission", "fee", "student", 
        "organization", "school", "license", "payment"
    }
    
    # Critical actions that always require audit logging
    CRITICAL_ACTIONS = {
        "login", "logout", "password_change", "role_change", "permission_change",
        "delete", "create_user", "create_student", "payment", "refund",
        "license_change", "data_export", "backup_restore"
    }
    
    def __init__(self):
        self.logger = logger
    
    def log_audit_event(
        self,
        school_id: uuid.UUID,
        module: str,
        action: str,
        user_id: Optional[uuid.UUID] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[uuid.UUID] = None,
        event_data: Optional[Dict[str, Any]] = None,
        changes: Optional[Dict[str, Any]] = None,
        event_metadata: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        correlation_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        severity: str = "info",
        category: str = "data",
        retention_policy: str = "standard"
    ) -> bool:
        """
        Log audit event to global audit table
        Returns True if logged successfully
        """
        try:
            with get_db_context() as db:
                audit_entry = AuditLog.log_event(
                    school_id=school_id,
                    module=module,
                    action=action,
                    user_id=user_id,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    event_data=event_data,
                    changes=changes,
                    event_metadata=event_metadata,
                    session_id=session_id,
                    correlation_id=correlation_id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    severity=severity,
                    category=category,
                    retention_policy=retention_policy
                )
                
                db.add(audit_entry)
                db.commit()
                
                self.logger.info(
                    f"Audit event logged: {module}.{action}",
                    extra={
                        "school_id": str(school_id),
                        "user_id": str(user_id) if user_id else None,
                        "module": module,
                        "action": action,
                        "severity": severity
                    }
                )
                
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to log audit event: {str(e)}")
            return False
    
    def should_log_globally(self, module: str, action: str) -> bool:
        """
        Determine if event should be logged to global audit table
        Based on module criticality and action importance
        """
        return (
            module.lower() in self.CRITICAL_MODULES or 
            action.lower() in self.CRITICAL_ACTIONS
        )
    
    def log_security_event(
        self,
        school_id: uuid.UUID,
        action: str,
        user_id: Optional[uuid.UUID] = None,
        event_data: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        severity: str = "warning"
    ) -> bool:
        """Log security-related events with high priority"""
        return self.log_audit_event(
            school_id=school_id,
            module="security",
            action=action,
            user_id=user_id,
            event_data=event_data,
            ip_address=ip_address,
            user_agent=user_agent,
            severity=severity,
            category="security",
            retention_policy="extended"
        )
    
    def log_compliance_event(
        self,
        school_id: uuid.UUID,
        action: str,
        resource_type: str,
        resource_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        changes: Optional[Dict[str, Any]] = None,
        compliance_flags: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Log compliance-related events (GDPR, DPDPA)"""
        event_metadata = {"compliance_flags": compliance_flags} if compliance_flags else None
        
        return self.log_audit_event(
            school_id=school_id,
            module="compliance",
            action=action,
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            changes=changes,
            event_metadata=event_metadata,
            severity="info",
            category="compliance",
            retention_policy="permanent"
        )
    
    def get_audit_trail(
        self,
        school_id: uuid.UUID,
        resource_type: Optional[str] = None,
        resource_id: Optional[uuid.UUID] = None,
        user_id: Optional[uuid.UUID] = None,
        module: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Retrieve audit trail with filtering options
        """
        try:
            with get_db_context() as db:
                query = db.query(AuditLog).filter(AuditLog.school_id == school_id)
                
                if resource_type:
                    query = query.filter(AuditLog.resource_type == resource_type)
                
                if resource_id:
                    query = query.filter(AuditLog.resource_id == resource_id)
                
                if user_id:
                    query = query.filter(AuditLog.user_id == user_id)
                
                if module:
                    query = query.filter(AuditLog.module == module)
                
                if start_date:
                    query = query.filter(AuditLog.created_at >= start_date)
                
                if end_date:
                    query = query.filter(AuditLog.created_at <= end_date)
                
                audit_entries = query.order_by(
                    AuditLog.created_at.desc()
                ).limit(limit).all()
                
                return [
                    {
                        "id": str(entry.id),
                        "timestamp": entry.created_at.isoformat(),
                        "module": entry.module,
                        "action": entry.action,
                        "user_id": str(entry.user_id) if entry.user_id else None,
                        "resource_type": entry.resource_type,
                        "resource_id": str(entry.resource_id) if entry.resource_id else None,
                        "event_data": entry.event_data,
                        "changes": entry.changes,
                        "severity": entry.severity,
                        "category": entry.category,
                        "ip_address": entry.ip_address
                    }
                    for entry in audit_entries
                ]
                
        except Exception as e:
            self.logger.error(f"Failed to retrieve audit trail: {str(e)}")
            return []
    
    def cleanup_old_audit_logs(self, school_id: uuid.UUID) -> Dict[str, Any]:
        """
        Clean up old audit logs based on retention policies
        """
        try:
            with get_db_context() as db:
                # Get retention policy for audit logs
                retention_days = DataRetentionPolicy.get_retention_days(school_id, "audit_logs")
                cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
                
                # Find old audit logs
                old_logs = db.query(AuditLog).filter(
                    and_(
                        AuditLog.school_id == school_id,
                        AuditLog.created_at < cutoff_date,
                        AuditLog.retention_policy != "permanent"  # Never delete permanent logs
                    )
                ).all()
                
                archived_count = 0
                deleted_count = 0
                
                for log in old_logs:
                    # Archive critical logs, delete others
                    if log.category in ["security", "compliance"] or log.severity in ["error", "critical"]:
                        # Archive critical logs
                        archive_entry = ArchivedData.archive_record(
                            school_id=school_id,
                            original_table="audit_logs",
                            original_id=log.id,
                            record_data=log.to_dict(),
                            archive_reason="retention",
                            expires_at=datetime.utcnow() + timedelta(days=365*7)  # 7 years
                        )
                        db.add(archive_entry)
                        archived_count += 1
                    
                    # Delete from audit_logs table
                    db.delete(log)
                    deleted_count += 1
                
                db.commit()
                
                self.logger.info(
                    f"Audit log cleanup completed",
                    extra={
                        "school_id": str(school_id),
                        "archived_count": archived_count,
                        "deleted_count": deleted_count,
                        "retention_days": retention_days
                    }
                )
                
                return {
                    "success": True,
                    "archived_count": archived_count,
                    "deleted_count": deleted_count,
                    "retention_days": retention_days
                }
                
        except Exception as e:
            self.logger.error(f"Audit log cleanup failed: {str(e)}")
            return {"success": False, "error": str(e)}


# Global audit service instance
audit_service = AuditService()


# Helper functions for easy use throughout the application
def log_audit_event(
    school_id: uuid.UUID,
    module: str,
    action: str,
    user_id: Optional[uuid.UUID] = None,
    **kwargs
) -> bool:
    """Convenience function for logging audit events"""
    return audit_service.log_audit_event(
        school_id=school_id,
        module=module,
        action=action,
        user_id=user_id,
        **kwargs
    )


def log_security_event(
    school_id: uuid.UUID,
    action: str,
    user_id: Optional[uuid.UUID] = None,
    **kwargs
) -> bool:
    """Convenience function for logging security events"""
    return audit_service.log_security_event(
        school_id=school_id,
        action=action,
        user_id=user_id,
        **kwargs
    )


def log_compliance_event(
    school_id: uuid.UUID,
    action: str,
    resource_type: str,
    resource_id: uuid.UUID,
    **kwargs
) -> bool:
    """Convenience function for logging compliance events"""
    return audit_service.log_compliance_event(
        school_id=school_id,
        action=action,
        resource_type=resource_type,
        resource_id=resource_id,
        **kwargs
    )
