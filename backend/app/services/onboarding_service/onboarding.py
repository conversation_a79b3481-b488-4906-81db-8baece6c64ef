"""
Onboarding Service for School ERP
Handles new school registration, organization setup, and default data creation
World-class implementation with comprehensive validation and error handling
"""

import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.core.database import get_db_context
from app.core.config import settings
from app.core.logging import get_logger
from app.core.security import password_manager
from app.models.base import Organization, School
from app.models.auth import User, Role, Permission, UserRole, RolePermission
from app.services.subdomain_service.subdomain import subdomain_service
from app.services.audit_service.audit import log_audit_event, log_security_event

logger = get_logger(__name__)


class OnboardingError(Exception):
    """Custom exception for onboarding errors"""
    pass


class OnboardingService:
    """
    Comprehensive onboarding service for new school registration
    Handles organization creation, school setup, default data seeding, and trial license assignment
    """
    
    def __init__(self):
        self.logger = logger
    
    def register_school(
        self,
        school_name: str,
        admin_email: str,
        admin_password: str,
        preferred_subdomain: str,
        country: str = "India",
        phone: Optional[str] = None,
        is_multi_branch: bool = False,
        admin_first_name: str = "Admin",
        admin_last_name: str = "User",
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Complete school registration process
        
        Args:
            school_name: Name of the school/organization
            admin_email: Email for the admin user
            admin_password: Password for the admin user
            preferred_subdomain: Desired subdomain
            country: Country (default: India)
            phone: Optional phone number
            is_multi_branch: Whether this is a multi-branch organization
            admin_first_name: Admin user's first name
            admin_last_name: Admin user's last name
            ip_address: Client IP for audit logging
            user_agent: Client user agent for audit logging
            
        Returns:
            Dict containing registration result with organization, school, and user details
            
        Raises:
            OnboardingError: If registration fails
        """
        with get_db_context() as db:
            try:
                # Step 1: Validate inputs
                self._validate_registration_inputs(
                    school_name=school_name,
                    admin_email=admin_email,
                    admin_password=admin_password,
                    preferred_subdomain=preferred_subdomain,
                    db=db
                )
                
                # Step 2: Check subdomain availability
                subdomain_result = subdomain_service.check_availability(preferred_subdomain)
                if not subdomain_result["is_available"]:
                    raise OnboardingError(
                        f"Subdomain '{preferred_subdomain}' is not available. "
                        f"Suggestions: {', '.join(subdomain_result.get('suggestions', [])[:3])}"
                    )
                
                final_subdomain = subdomain_result["subdomain"]
                
                # Step 3: Create organization
                organization = self._create_organization(
                    name=school_name if is_multi_branch else f"{school_name} Organization",
                    email=admin_email,
                    phone=phone,
                    country=country,
                    subdomain=final_subdomain if is_multi_branch else None,
                    is_multi_branch=is_multi_branch,
                    db=db
                )
                
                # Step 4: Create school
                school = self._create_school(
                    organization=organization,
                    name=school_name,
                    email=admin_email,
                    phone=phone,
                    country=country,
                    subdomain=final_subdomain,
                    db=db
                )
                
                # Step 5: Create default roles and permissions
                roles_permissions = self._create_default_roles_and_permissions(
                    school=school,
                    db=db
                )
                
                # Step 6: Create admin user
                admin_user = self._create_admin_user(
                    school=school,
                    email=admin_email,
                    password=admin_password,
                    first_name=admin_first_name,
                    last_name=admin_last_name,
                    phone=phone,
                    roles=roles_permissions["roles"],
                    db=db
                )
                
                # Step 7: Create default academic structure
                academic_year = self._create_default_academic_year(
                    school=school,
                    db=db
                )
                
                # Step 8: Assign trial license
                trial_license = self._assign_trial_license(
                    school=school,
                    db=db
                )
                
                # Step 9: Log successful registration
                log_audit_event(
                    action="SCHOOL_REGISTERED",
                    resource_type="School",
                    resource_id=str(school.id),
                    user_id=admin_user.id,
                    school_id=school.id,
                    details={
                        "school_name": school_name,
                        "subdomain": final_subdomain,
                        "is_multi_branch": is_multi_branch,
                        "country": country,
                        "admin_email": admin_email
                    }
                )
                
                log_security_event(
                    event_type="ACCOUNT_CREATED",
                    user_id=admin_user.id,
                    school_id=school.id,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    details={
                        "registration_type": "school_onboarding",
                        "subdomain": final_subdomain
                    }
                )
                
                db.commit()
                
                self.logger.info(
                    f"School registration completed successfully",
                    extra={
                        "school_id": str(school.id),
                        "organization_id": str(organization.id),
                        "admin_user_id": str(admin_user.id),
                        "subdomain": final_subdomain,
                        "is_multi_branch": is_multi_branch
                    }
                )
                
                return {
                    "success": True,
                    "message": "School registration completed successfully",
                    "organization": {
                        "id": str(organization.id),
                        "name": organization.name,
                        "subdomain": organization.subdomain
                    },
                    "school": {
                        "id": str(school.id),
                        "name": school.name,
                        "subdomain": school.subdomain,
                        "school_code": school.school_code
                    },
                    "admin_user": {
                        "id": str(admin_user.id),
                        "email": admin_user.email,
                        "first_name": admin_user.first_name,
                        "last_name": admin_user.last_name
                    },
                    "trial_license": trial_license,
                    "academic_year": academic_year,
                    "next_steps": [
                        "Complete email verification",
                        "Set up school profile",
                        "Configure academic structure",
                        "Invite staff members"
                    ]
                }
                
            except OnboardingError:
                db.rollback()
                raise
            except IntegrityError as e:
                db.rollback()
                self.logger.error(f"Database integrity error during registration: {str(e)}")
                raise OnboardingError("Registration failed due to data conflict. Please try again.")
            except Exception as e:
                db.rollback()
                self.logger.error(f"Unexpected error during registration: {str(e)}")
                raise OnboardingError("Registration failed due to system error. Please try again.")
    
    def _validate_registration_inputs(
        self,
        school_name: str,
        admin_email: str,
        admin_password: str,
        preferred_subdomain: str,
        db: Session
    ) -> None:
        """Validate all registration inputs"""
        
        # Validate school name
        if not school_name or len(school_name.strip()) < 2:
            raise OnboardingError("School name must be at least 2 characters long")
        
        if len(school_name) > 255:
            raise OnboardingError("School name must be less than 255 characters")
        
        # Validate email format
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, admin_email):
            raise OnboardingError("Invalid email format")
        
        # Check if email is already registered
        existing_user = db.query(User).filter(
            User.email == admin_email.lower().strip(),
            User.is_deleted == False
        ).first()
        
        if existing_user:
            raise OnboardingError("Email address is already registered")
        
        # Validate password strength
        if not password_manager.validate_password_strength(admin_password):
            raise OnboardingError(
                "Password must be at least 8 characters long and contain uppercase, "
                "lowercase, number, and special character"
            )
        
        # Validate subdomain format (basic validation - detailed validation in subdomain service)
        if not preferred_subdomain or len(preferred_subdomain) < 3:
            raise OnboardingError("Subdomain must be at least 3 characters long")
        
        if len(preferred_subdomain) > 63:
            raise OnboardingError("Subdomain must be less than 63 characters")
    
    def _create_organization(
        self,
        name: str,
        email: str,
        country: str,
        phone: Optional[str] = None,
        subdomain: Optional[str] = None,
        is_multi_branch: bool = False,
        db: Session = None
    ) -> Organization:
        """Create organization with proper defaults"""
        
        organization = Organization(
            name=name.strip(),
            display_name=name.strip(),
            email=email.lower().strip(),
            phone=phone,
            country=country,
            subdomain=subdomain,
            org_type="trust" if is_multi_branch else "school",
            is_active=True,
            # Localization defaults for India
            language="en",
            timezone="Asia/Kolkata",
            currency="INR",
            date_format="DD/MM/YYYY",
            time_format="HH:mm"
        )
        
        db.add(organization)
        db.flush()  # Get the ID without committing
        
        return organization

    def _create_school(
        self,
        organization: Organization,
        name: str,
        email: str,
        country: str,
        subdomain: str,
        phone: Optional[str] = None,
        db: Session = None
    ) -> School:
        """Create school with proper defaults and multi-tenant setup"""

        # Generate unique school code
        school_code = self._generate_school_code(name, db)

        # Pre-generate school ID for multi-tenant setup
        school_id = uuid.uuid4()

        school = School(
            id=school_id,
            school_id=school_id,  # Self-reference for multi-tenant
            organization_id=organization.id,
            name=name.strip(),
            display_name=name.strip(),
            email=email.lower().strip(),
            phone=phone,
            country=country,
            subdomain=subdomain.lower().strip(),
            school_code=school_code,
            board_type="CBSE",  # Default for Indian schools
            school_type="school",
            is_active=True,
            is_verified=False,  # Will be verified after email confirmation
            academic_year_start_month=4,  # April for Indian schools
            # Localization defaults
            language="en",
            timezone="Asia/Kolkata",
            currency="INR",
            date_format="DD/MM/YYYY",
            time_format="HH:mm"
        )

        db.add(school)
        db.flush()

        return school

    def _generate_school_code(self, school_name: str, db: Session) -> str:
        """Generate unique school code"""
        # Create base code from school name
        words = school_name.upper().split()
        if len(words) >= 2:
            base_code = words[0][:3] + words[1][:3]
        else:
            base_code = words[0][:6] if words else "SCH"

        # Ensure it's at least 3 characters
        base_code = base_code.ljust(3, 'X')[:6]

        # Find unique code
        counter = 1
        while True:
            school_code = f"{base_code}{counter:03d}"

            existing = db.query(School).filter(
                School.school_code == school_code,
                School.is_deleted == False
            ).first()

            if not existing:
                return school_code

            counter += 1
            if counter > 999:  # Safety check
                # Fallback to UUID-based code
                return f"SCH{uuid.uuid4().hex[:6].upper()}"

    def _create_default_roles_and_permissions(
        self,
        school: School,
        db: Session
    ) -> Dict[str, Any]:
        """Create default roles and permissions for the school"""

        # Default permissions for school management
        default_permissions = [
            # Admin permissions
            {
                "code": "admin.all",
                "name": "Full Administrative Access",
                "category": "admin",
                "resource": "all",
                "action": "all",
                "description": "Complete system access for administrators"
            },
            # User management
            {
                "code": "user.manage",
                "name": "User Management",
                "category": "admin",
                "resource": "user",
                "action": "all",
                "description": "Create, update, delete users"
            },
            # School management
            {
                "code": "school.manage",
                "name": "School Management",
                "category": "admin",
                "resource": "school",
                "action": "all",
                "description": "Manage school settings and configuration"
            },
            # Academic management
            {
                "code": "academic.manage",
                "name": "Academic Management",
                "category": "academic",
                "resource": "academic",
                "action": "all",
                "description": "Manage classes, sections, academic years"
            },
            # Teacher permissions
            {
                "code": "teacher.academic",
                "name": "Teacher Academic Access",
                "category": "academic",
                "resource": "class",
                "action": "read",
                "description": "View academic information and manage assigned classes"
            },
            # Accountant permissions
            {
                "code": "finance.manage",
                "name": "Financial Management",
                "category": "finance",
                "resource": "finance",
                "action": "all",
                "description": "Manage fees, payments, and financial records"
            }
        ]

        # Create permissions
        permissions = []
        for perm_data in default_permissions:
            permission = Permission(
                school_id=school.id,
                code=perm_data["code"],
                name=perm_data["name"],
                description=perm_data["description"],
                category=perm_data["category"],
                resource=perm_data["resource"],
                action=perm_data["action"],
                is_active=True,
                is_system_permission=True
            )
            db.add(permission)
            permissions.append(permission)

        db.flush()  # Get permission IDs

        # Default roles
        default_roles = [
            {
                "name": "admin",
                "display_name": "Administrator",
                "description": "Full system administrator with all permissions",
                "level": 1,
                "permissions": ["admin.all", "user.manage", "school.manage", "academic.manage"]
            },
            {
                "name": "teacher",
                "display_name": "Teacher",
                "description": "Teaching staff with academic access",
                "level": 3,
                "permissions": ["teacher.academic"]
            },
            {
                "name": "accountant",
                "display_name": "Accountant",
                "description": "Financial management staff",
                "level": 2,
                "permissions": ["finance.manage"]
            },
            {
                "name": "parent",
                "display_name": "Parent",
                "description": "Parent/guardian with limited access",
                "level": 5,
                "permissions": []
            },
            {
                "name": "student",
                "display_name": "Student",
                "description": "Student with basic access",
                "level": 6,
                "permissions": []
            }
        ]

        # Create roles and assign permissions
        roles = []
        permission_map = {p.code: p for p in permissions}

        for role_data in default_roles:
            role = Role(
                school_id=school.id,
                name=role_data["name"],
                display_name=role_data["display_name"],
                description=role_data["description"],
                level=role_data["level"],
                is_active=True,
                is_system_role=True
            )
            db.add(role)
            db.flush()  # Get role ID

            # Assign permissions to role
            for perm_code in role_data["permissions"]:
                if perm_code in permission_map:
                    role_permission = RolePermission(
                        role_id=role.id,
                        permission_id=permission_map[perm_code].id
                    )
                    db.add(role_permission)

            roles.append(role)

        return {
            "roles": roles,
            "permissions": permissions,
            "admin_role": next(r for r in roles if r.name == "admin")
        }

    def _create_admin_user(
        self,
        school: School,
        email: str,
        password: str,
        first_name: str,
        last_name: str,
        phone: Optional[str],
        roles: List[Role],
        db: Session
    ) -> User:
        """Create admin user with proper role assignment"""

        admin_user = User(
            school_id=school.id,
            email=email.lower().strip(),
            first_name=first_name.strip(),
            last_name=last_name.strip(),
            phone=phone,
            user_type="admin",
            is_active=True,
            is_verified=False,  # Will be verified after email confirmation
            is_superuser=True,  # Admin has superuser privileges
            # Localization defaults
            language="en",
            timezone="Asia/Kolkata",
            currency="INR",
            date_format="DD/MM/YYYY",
            time_format="HH:mm"
        )

        # Set password
        admin_user.set_password(password)

        db.add(admin_user)
        db.flush()  # Get user ID

        # Assign admin role
        admin_role = next(r for r in roles if r.name == "admin")
        user_role = UserRole(
            user_id=admin_user.id,
            role_id=admin_role.id,
            assigned_by=admin_user.id,  # Self-assigned during registration
            is_active=True
        )
        db.add(user_role)

        return admin_user

    def _create_default_academic_year(
        self,
        school: School,
        db: Session
    ) -> Dict[str, Any]:
        """Create default academic year for the school"""

        # Calculate current academic year based on Indian academic calendar
        current_date = datetime.now()
        if current_date.month >= 4:  # April onwards is new academic year
            start_year = current_date.year
            end_year = current_date.year + 1
        else:  # January-March is previous academic year
            start_year = current_date.year - 1
            end_year = current_date.year

        academic_year_label = f"{start_year}-{str(end_year)[2:]}"
        start_date = datetime(start_year, 4, 1)  # April 1st
        end_date = datetime(end_year, 3, 31)  # March 31st

        # For now, we'll store this in school settings
        # Later this will be moved to a dedicated AcademicYear model
        academic_year_data = {
            "label": academic_year_label,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "is_active": True,
            "created_during_onboarding": True
        }

        # Update school settings
        current_settings = school.settings or {}
        current_settings["current_academic_year"] = academic_year_data
        school.settings = current_settings

        return academic_year_data

    def _assign_trial_license(
        self,
        school: School,
        db: Session
    ) -> Dict[str, Any]:
        """Assign trial license to the school"""

        # Calculate trial period (configurable)
        trial_days = getattr(settings, 'TRIAL_PERIOD_DAYS', 14)
        trial_start = datetime.utcnow()
        trial_end = trial_start + timedelta(days=trial_days)

        # For now, we'll store this in school settings
        # Later this will be moved to a dedicated SchoolLicense model
        trial_license_data = {
            "plan_type": "trial",
            "plan_name": "Trial Plan",
            "start_date": trial_start.isoformat(),
            "end_date": trial_end.isoformat(),
            "is_active": True,
            "max_students": 50,  # Trial limits
            "max_staff": 10,
            "enabled_features": [
                "student_management",
                "staff_management",
                "basic_academics",
                "basic_communication"
            ],
            "created_during_onboarding": True
        }

        # Update school settings
        current_settings = school.settings or {}
        current_settings["license"] = trial_license_data
        school.settings = current_settings

        return trial_license_data

    def check_subdomain_availability(self, subdomain: str) -> Dict[str, Any]:
        """Check if subdomain is available for registration"""
        return subdomain_service.check_availability(subdomain)

    def get_subdomain_suggestions(self, school_name: str, limit: int = 5) -> List[str]:
        """Generate subdomain suggestions based on school name"""
        return subdomain_service.generate_suggestions(school_name, limit)

    def send_verification_email(self, user_id: uuid.UUID) -> bool:
        """
        Send email verification (placeholder implementation)
        TODO: Implement actual email sending in future phase
        """
        self.logger.info(f"Email verification placeholder for user {user_id}")
        return True

    def verify_email(self, verification_token: str) -> Dict[str, Any]:
        """
        Verify email with token (placeholder implementation)
        TODO: Implement actual email verification in future phase
        """
        self.logger.info(f"Email verification placeholder for token {verification_token}")
        return {"success": True, "message": "Email verification placeholder"}


# Create singleton instance
onboarding_service = OnboardingService()
