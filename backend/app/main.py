"""
School ERP Backend Application
Main FastAPI application entry point
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import settings
from app.core.logging import setup_logging
from app.core.middleware import (
    SecurityMiddleware,
    RateLimitMiddleware,
    SubdomainRoutingMiddleware,
    AuthenticationMiddleware
)
from app.api.v1 import auth, subdomain, onboarding, academic, rbac

# Setup logging
setup_logging()

# Create FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="AI-Driven Modular School ERP Backend API",
    version="0.1.0",
    docs_url="/docs" if settings.ENVIRONMENT != "production" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT != "production" else None,
)

# Add security and routing middleware (order matters!)
app.add_middleware(SecurityMiddleware)
app.add_middleware(RateLimitMiddleware)
app.add_middleware(SubdomainRoutingMiddleware)
app.add_middleware(AuthenticationMiddleware)

# CORS middleware for frontend communication
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(auth.router, prefix="/api/v1")
app.include_router(subdomain.router, prefix="/api/v1")
app.include_router(onboarding.router, prefix="/api/v1")
app.include_router(academic.router, prefix="/api/v1")
app.include_router(rbac.router, prefix="/api/v1")


@app.get("/")
async def root():
    """Root endpoint for health check"""
    return {
        "message": "School ERP Backend API",
        "version": "0.1.0",
        "status": "running",
        "environment": settings.ENVIRONMENT
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True if settings.ENVIRONMENT == "development" else False
    )
