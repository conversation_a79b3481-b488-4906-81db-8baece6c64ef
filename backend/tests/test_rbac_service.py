"""
Tests for RBAC Service
Comprehensive testing of role and permission management functionality
"""

import pytest
import uuid
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

from app.services.auth_service.rbac import rbac_service, RBACError, PermissionDeniedError
from app.models.auth import User, Role, Permission, UserRole, RolePermission
from app.models.base import School, Organization
from tests.factories import (
    UserFactory, RoleFactory, PermissionFactory, 
    SchoolFactory, OrganizationFactory
)


class TestRBACService:
    """Test suite for RBAC Service"""
    
    @pytest.fixture
    def setup_test_data(self, db_session):
        """Setup test data for RBAC tests"""
        # Create organization and school
        organization = OrganizationFactory()
        school = SchoolFactory(organization=organization)
        
        # Create test users
        admin_user = UserFactory(
            school=school,
            user_type="admin",
            is_active=True
        )
        
        teacher_user = UserFactory(
            school=school,
            user_type="teacher",
            is_active=True
        )
        
        # Create test roles
        admin_role = RoleFactory(
            school=school,
            name="admin",
            display_name="Administrator",
            level=1,
            is_system_role=True
        )
        
        teacher_role = RoleFactory(
            school=school,
            name="teacher",
            display_name="Teacher",
            level=3,
            parent_role=admin_role
        )
        
        # Create test permissions
        admin_permission = PermissionFactory(
            school=school,
            code="admin.all",
            name="Full Administrative Access",
            category="admin",
            resource="all",
            action="all"
        )
        
        user_manage_permission = PermissionFactory(
            school=school,
            code="user.manage",
            name="User Management",
            category="user",
            resource="user",
            action="manage"
        )
        
        db_session.commit()
        
        return {
            "organization": organization,
            "school": school,
            "admin_user": admin_user,
            "teacher_user": teacher_user,
            "admin_role": admin_role,
            "teacher_role": teacher_role,
            "admin_permission": admin_permission,
            "user_manage_permission": user_manage_permission
        }
    
    # ==================== ROLE MANAGEMENT TESTS ====================
    
    def test_create_role_success(self, db_session, setup_test_data):
        """Test successful role creation"""
        data = setup_test_data
        
        result = rbac_service.create_role(
            school_id=data["school"].id,
            name="student",
            display_name="Student",
            description="Student role with limited access",
            level=5,
            created_by=data["admin_user"].id
        )
        
        assert result["success"] is True
        assert result["message"] == "Role created successfully"
        assert result["role"]["name"] == "student"
        assert result["role"]["display_name"] == "Student"
        assert result["role"]["level"] == 5
    
    def test_create_role_duplicate_name(self, db_session, setup_test_data):
        """Test role creation with duplicate name"""
        data = setup_test_data
        
        with pytest.raises(RBACError, match="Role with name 'admin' already exists"):
            rbac_service.create_role(
                school_id=data["school"].id,
                name="admin",  # Duplicate name
                display_name="Another Admin",
                created_by=data["admin_user"].id
            )
    
    def test_create_role_invalid_parent(self, db_session, setup_test_data):
        """Test role creation with invalid parent role"""
        data = setup_test_data
        invalid_parent_id = uuid.uuid4()
        
        with pytest.raises(RBACError, match="Parent role not found or inactive"):
            rbac_service.create_role(
                school_id=data["school"].id,
                name="test_role",
                display_name="Test Role",
                parent_role_id=invalid_parent_id,
                created_by=data["admin_user"].id
            )
    
    def test_get_role_success(self, db_session, setup_test_data):
        """Test successful role retrieval"""
        data = setup_test_data
        
        role = rbac_service.get_role(
            school_id=data["school"].id,
            role_id=data["admin_role"].id
        )
        
        assert role is not None
        assert role["name"] == "admin"
        assert role["display_name"] == "Administrator"
        assert role["level"] == 1
    
    def test_get_role_not_found(self, db_session, setup_test_data):
        """Test role retrieval with invalid ID"""
        data = setup_test_data
        invalid_role_id = uuid.uuid4()
        
        role = rbac_service.get_role(
            school_id=data["school"].id,
            role_id=invalid_role_id
        )
        
        assert role is None
    
    def test_list_roles_success(self, db_session, setup_test_data):
        """Test successful role listing"""
        data = setup_test_data
        
        result = rbac_service.list_roles(
            school_id=data["school"].id,
            page=1,
            page_size=10
        )
        
        assert "roles" in result
        assert "pagination" in result
        assert len(result["roles"]) >= 2  # admin and teacher roles
        assert result["pagination"]["page"] == 1
        assert result["pagination"]["page_size"] == 10
    
    def test_update_role_success(self, db_session, setup_test_data):
        """Test successful role update"""
        data = setup_test_data
        
        result = rbac_service.update_role(
            school_id=data["school"].id,
            role_id=data["teacher_role"].id,
            display_name="Senior Teacher",
            description="Updated description",
            updated_by=data["admin_user"].id
        )
        
        assert result["success"] is True
        assert result["role"]["display_name"] == "Senior Teacher"
        assert result["role"]["description"] == "Updated description"
    
    def test_update_system_role_restricted(self, db_session, setup_test_data):
        """Test that system role properties cannot be modified"""
        data = setup_test_data
        
        with pytest.raises(RBACError, match="Cannot modify system role properties"):
            rbac_service.update_role(
                school_id=data["school"].id,
                role_id=data["admin_role"].id,  # System role
                display_name="Modified Admin",
                updated_by=data["admin_user"].id
            )
    
    def test_delete_role_success(self, db_session, setup_test_data):
        """Test successful role deletion"""
        data = setup_test_data
        
        # Create a role without dependencies
        test_role = RoleFactory(
            school=data["school"],
            name="test_delete",
            display_name="Test Delete Role"
        )
        db_session.commit()
        
        result = rbac_service.delete_role(
            school_id=data["school"].id,
            role_id=test_role.id,
            deleted_by=data["admin_user"].id
        )
        
        assert result["success"] is True
        assert "deleted successfully" in result["message"]
    
    def test_delete_system_role_restricted(self, db_session, setup_test_data):
        """Test that system roles cannot be deleted"""
        data = setup_test_data
        
        with pytest.raises(RBACError, match="Cannot delete system role"):
            rbac_service.delete_role(
                school_id=data["school"].id,
                role_id=data["admin_role"].id,  # System role
                deleted_by=data["admin_user"].id
            )
    
    # ==================== PERMISSION MANAGEMENT TESTS ====================
    
    def test_create_permission_success(self, db_session, setup_test_data):
        """Test successful permission creation"""
        data = setup_test_data
        
        result = rbac_service.create_permission(
            school_id=data["school"].id,
            code="student.read",
            name="Read Student Data",
            category="student",
            resource="student",
            action="read",
            description="Permission to read student information",
            created_by=data["admin_user"].id
        )
        
        assert result["success"] is True
        assert result["permission"]["code"] == "student.read"
        assert result["permission"]["category"] == "student"
        assert result["permission"]["resource"] == "student"
        assert result["permission"]["action"] == "read"
    
    def test_create_permission_duplicate_code(self, db_session, setup_test_data):
        """Test permission creation with duplicate code"""
        data = setup_test_data
        
        with pytest.raises(RBACError, match="Permission with code 'admin.all' already exists"):
            rbac_service.create_permission(
                school_id=data["school"].id,
                code="admin.all",  # Duplicate code
                name="Another Admin Permission",
                category="admin",
                resource="all",
                action="all",
                created_by=data["admin_user"].id
            )
    
    def test_create_permission_invalid_category(self, db_session, setup_test_data):
        """Test permission creation with invalid category"""
        data = setup_test_data
        
        with pytest.raises(RBACError, match="Invalid category 'invalid'"):
            rbac_service.create_permission(
                school_id=data["school"].id,
                code="test.permission",
                name="Test Permission",
                category="invalid",  # Invalid category
                resource="test",
                action="read",
                created_by=data["admin_user"].id
            )
    
    def test_list_permissions_success(self, db_session, setup_test_data):
        """Test successful permission listing"""
        data = setup_test_data
        
        result = rbac_service.list_permissions(
            school_id=data["school"].id,
            page=1,
            page_size=10
        )
        
        assert "permissions" in result
        assert "pagination" in result
        assert len(result["permissions"]) >= 2  # admin.all and user.manage
        assert result["pagination"]["page"] == 1
        assert result["pagination"]["page_size"] == 10
    
    def test_list_permissions_with_filters(self, db_session, setup_test_data):
        """Test permission listing with category filter"""
        data = setup_test_data
        
        result = rbac_service.list_permissions(
            school_id=data["school"].id,
            category="admin",
            page=1,
            page_size=10
        )
        
        assert "permissions" in result
        # Should only return admin category permissions
        for permission in result["permissions"]:
            assert permission["category"] == "admin"

    # ==================== ROLE-PERMISSION ASSIGNMENT TESTS ====================

    def test_assign_permission_to_role_success(self, db_session, setup_test_data):
        """Test successful permission assignment to role"""
        data = setup_test_data

        result = rbac_service.assign_permission_to_role(
            school_id=data["school"].id,
            role_id=data["teacher_role"].id,
            permission_id=data["user_manage_permission"].id,
            assigned_by=data["admin_user"].id
        )

        assert result["success"] is True
        assert "assigned to role" in result["message"]

    def test_assign_permission_to_role_already_assigned(self, db_session, setup_test_data):
        """Test assigning already assigned permission"""
        data = setup_test_data

        # First assignment
        rbac_service.assign_permission_to_role(
            school_id=data["school"].id,
            role_id=data["teacher_role"].id,
            permission_id=data["user_manage_permission"].id,
            assigned_by=data["admin_user"].id
        )

        # Second assignment (should succeed with message)
        result = rbac_service.assign_permission_to_role(
            school_id=data["school"].id,
            role_id=data["teacher_role"].id,
            permission_id=data["user_manage_permission"].id,
            assigned_by=data["admin_user"].id
        )

        assert result["success"] is True
        assert "already assigned" in result["message"]

    def test_assign_permission_invalid_role(self, db_session, setup_test_data):
        """Test assigning permission to invalid role"""
        data = setup_test_data
        invalid_role_id = uuid.uuid4()

        with pytest.raises(RBACError, match="Role not found or inactive"):
            rbac_service.assign_permission_to_role(
                school_id=data["school"].id,
                role_id=invalid_role_id,
                permission_id=data["user_manage_permission"].id,
                assigned_by=data["admin_user"].id
            )

    def test_remove_permission_from_role_success(self, db_session, setup_test_data):
        """Test successful permission removal from role"""
        data = setup_test_data

        # First assign the permission
        rbac_service.assign_permission_to_role(
            school_id=data["school"].id,
            role_id=data["teacher_role"].id,
            permission_id=data["user_manage_permission"].id,
            assigned_by=data["admin_user"].id
        )

        # Then remove it
        result = rbac_service.remove_permission_from_role(
            school_id=data["school"].id,
            role_id=data["teacher_role"].id,
            permission_id=data["user_manage_permission"].id,
            removed_by=data["admin_user"].id
        )

        assert result["success"] is True
        assert "removed from role" in result["message"]

    def test_remove_permission_not_assigned(self, db_session, setup_test_data):
        """Test removing permission that's not assigned"""
        data = setup_test_data

        result = rbac_service.remove_permission_from_role(
            school_id=data["school"].id,
            role_id=data["teacher_role"].id,
            permission_id=data["user_manage_permission"].id,
            removed_by=data["admin_user"].id
        )

        assert result["success"] is True
        assert "not assigned" in result["message"]

    # ==================== USER-ROLE ASSIGNMENT TESTS ====================

    def test_assign_role_to_user_success(self, db_session, setup_test_data):
        """Test successful role assignment to user"""
        data = setup_test_data

        result = rbac_service.assign_role_to_user(
            school_id=data["school"].id,
            user_id=data["teacher_user"].id,
            role_id=data["teacher_role"].id,
            assigned_by=data["admin_user"].id
        )

        assert result["success"] is True
        assert "assigned to user" in result["message"]

    def test_assign_role_to_user_with_expiration(self, db_session, setup_test_data):
        """Test role assignment with expiration date"""
        data = setup_test_data
        expires_at = datetime.utcnow() + timedelta(days=30)

        result = rbac_service.assign_role_to_user(
            school_id=data["school"].id,
            user_id=data["teacher_user"].id,
            role_id=data["teacher_role"].id,
            assigned_by=data["admin_user"].id,
            expires_at=expires_at
        )

        assert result["success"] is True
        assert "assigned to user" in result["message"]

    def test_assign_role_invalid_user(self, db_session, setup_test_data):
        """Test assigning role to invalid user"""
        data = setup_test_data
        invalid_user_id = uuid.uuid4()

        with pytest.raises(RBACError, match="User not found or inactive"):
            rbac_service.assign_role_to_user(
                school_id=data["school"].id,
                user_id=invalid_user_id,
                role_id=data["teacher_role"].id,
                assigned_by=data["admin_user"].id
            )

    def test_remove_role_from_user_success(self, db_session, setup_test_data):
        """Test successful role removal from user"""
        data = setup_test_data

        # First assign the role
        rbac_service.assign_role_to_user(
            school_id=data["school"].id,
            user_id=data["teacher_user"].id,
            role_id=data["teacher_role"].id,
            assigned_by=data["admin_user"].id
        )

        # Then remove it
        result = rbac_service.remove_role_from_user(
            school_id=data["school"].id,
            user_id=data["teacher_user"].id,
            role_id=data["teacher_role"].id,
            removed_by=data["admin_user"].id
        )

        assert result["success"] is True
        assert "removed from user" in result["message"]

    # ==================== PERMISSION CHECKING TESTS ====================

    @patch('app.services.auth_service.rbac.redis_client')
    def test_check_user_permission_success(self, mock_redis, db_session, setup_test_data):
        """Test successful permission checking"""
        data = setup_test_data
        mock_redis.get.return_value = None  # No cache

        # Assign role to user and permission to role
        rbac_service.assign_role_to_user(
            school_id=data["school"].id,
            user_id=data["admin_user"].id,
            role_id=data["admin_role"].id,
            assigned_by=data["admin_user"].id
        )

        rbac_service.assign_permission_to_role(
            school_id=data["school"].id,
            role_id=data["admin_role"].id,
            permission_id=data["admin_permission"].id,
            assigned_by=data["admin_user"].id
        )

        has_permission = rbac_service.check_user_permission(
            school_id=data["school"].id,
            user_id=data["admin_user"].id,
            permission_code="admin.all",
            use_cache=False
        )

        assert has_permission is True

    @patch('app.services.auth_service.rbac.redis_client')
    def test_check_user_permission_denied(self, mock_redis, db_session, setup_test_data):
        """Test permission checking when user doesn't have permission"""
        data = setup_test_data
        mock_redis.get.return_value = None  # No cache

        has_permission = rbac_service.check_user_permission(
            school_id=data["school"].id,
            user_id=data["teacher_user"].id,
            permission_code="admin.all",
            use_cache=False
        )

        assert has_permission is False

    @patch('app.services.auth_service.rbac.redis_client')
    def test_get_user_permissions_success(self, mock_redis, db_session, setup_test_data):
        """Test getting user permissions"""
        data = setup_test_data
        mock_redis.get.return_value = None  # No cache
        mock_redis.setex.return_value = True  # Cache write success

        # Assign role to user and permission to role
        rbac_service.assign_role_to_user(
            school_id=data["school"].id,
            user_id=data["admin_user"].id,
            role_id=data["admin_role"].id,
            assigned_by=data["admin_user"].id
        )

        rbac_service.assign_permission_to_role(
            school_id=data["school"].id,
            role_id=data["admin_role"].id,
            permission_id=data["admin_permission"].id,
            assigned_by=data["admin_user"].id
        )

        permissions = rbac_service.get_user_permissions(
            school_id=data["school"].id,
            user_id=data["admin_user"].id,
            use_cache=False
        )

        assert isinstance(permissions, list)
        assert "admin.all" in permissions

    def test_get_role_permissions_success(self, db_session, setup_test_data):
        """Test getting role permissions"""
        data = setup_test_data

        # Assign permission to role
        rbac_service.assign_permission_to_role(
            school_id=data["school"].id,
            role_id=data["admin_role"].id,
            permission_id=data["admin_permission"].id,
            assigned_by=data["admin_user"].id
        )

        permissions = rbac_service.get_role_permissions(
            school_id=data["school"].id,
            role_id=data["admin_role"].id,
            include_inherited=False
        )

        assert isinstance(permissions, list)
        assert "admin.all" in permissions
