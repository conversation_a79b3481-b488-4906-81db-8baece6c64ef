# Redis configuration for School ERP Testing
# Optimized for test environment

# Network
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300

# General
daemonize no
supervised no
pidfile /var/run/redis_test_6379.pid
loglevel notice
logfile ""

# Memory management for tests
maxmemory 128mb
maxmemory-policy allkeys-lru

# Disable persistence for faster tests
save ""
appendonly no

# Faster for testing
stop-writes-on-bgsave-error no
rdbcompression no
rdbchecksum no

# Test database
databases 16

# Slow log (more verbose for testing)
slowlog-log-slower-than 1000
slowlog-max-len 256
