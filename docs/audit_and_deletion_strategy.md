**Reusable Technical Strategy Document**
**Topic:** Audit Trails & Soft Deletion Strategy
**Context:** Modular School ERP SaaS Platform (Multi-Tenant)
**Purpose:** Define clear architectural guidelines for tracking system activity and data lifecycle using audit logs and soft deletion.

---

### ✅ 1. Audit Trail Strategy: Hybrid Model (Recommended)

#### 🔹 Global Audit Table (Centralized Logging)
- **Use For:** Critical system-level actions and sensitive operations
- **Table:** `audit_logs`
- **Fields:**
  ```sql
  id (uuid), school_id, user_id, module, action, data (jsonb), created_at (timestamp)
  ```
- **Examples:**
  - User login/logout, password change
  - Plan/license upgrade, student deletion
  - Fee collection, refund, permission change

#### 🔹 Per-Model Audit Column (Light JSONB Tracking)
- **Use For:** Minor record-level changes (optional)
- **Column:** `audit_log jsonb[]`
- **Example Structure:**
  ```json
  [
    {
      "timestamp": "2024-07-28T10:30:00Z",
      "action": "profile_updated",
      "user_id": "user_123",
      "changes": { "phone": ["old", "new"] },
      "version": 3
    }
  ]
  ```
- **Retention Limit:** Keep only the last 20 entries (optional by config)

#### 🔹 Implementation Notes
- Provide a helper function: `log_audit_event(user_id, module, action, data)`
- Use middleware to auto-log CRUD for selected models

---

### ✅ 2. Soft Deletion Strategy

#### 🔹 Policy: Soft Delete for Critical Data Only
| Data Type            | Strategy     |
|----------------------|--------------|
| Students, Staff      | ✅ Soft Delete (`is_deleted = true`, `deleted_at`)
| Financial Records    | ✅ Soft Delete + Immutable Transactions
| Lookup Tables        | ❌ Hard Delete (safe to recreate)
| Tokens/Temp Records  | ❌ Hard Delete (short-lived)

- Add global ORM filters: `is_deleted = false` to all queries (abstracted)
- Maintain referential integrity — avoid cascading hard delete

---

### ✅ 3. Data Retention & Cleanup Policy
- **Retention Period:** 6 months for soft-deleted records (configurable)
- **Archival:** Move to archive table or cold storage after retention period
- **Job:** Scheduled cleanup via background job/cron (per model or per tenant)
- **Flag:** Add `archived_at` if archived

---

### ✅ 4. Compliance vs Performance
- **Current Priority:** Performance-first, extendable for compliance
- Prepare schema and design for:
  - DPDPA (India), GDPR (future international clients)
  - Per-school flags like `strict_audit_mode`
  - Optional full audit export API

---

### ✅ 5. AI Agent Coding Rules
1. Always scope audit and delete actions by `school_id`
2. Never hard-delete records marked for soft deletion unless allowed
3. Use helper utilities for logging and deletion (`soft_delete()`, `log_audit()`)
4. Auto-append audit logs on update/delete where applicable
5. Avoid storing large logs in JSONB for high-activity models
6. Protect financial and permission-modifying actions with global logging
7. Add `deleted_by`, `deleted_at`, `is_deleted` fields where soft delete is enabled
8. Create indexes on `is_deleted`, `deleted_at` for performance
9. Maintain API behavior: Exclude soft-deleted unless `include_deleted=true` query param is passed
10. Always log audit entries to `audit_logs` for core modules (auth, user, fee, role)

---

**End of Document: Audit & Deletion Strategy for ERP Platform**

