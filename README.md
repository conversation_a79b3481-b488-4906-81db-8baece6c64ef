# 🏫 School ERP Backend

AI-Driven Modular School ERP Backend for Indian Schools - A comprehensive SaaS solution built with FastAPI, PostgreSQL, and Redis.

## 🚀 Features

- **Multi-Tenant Architecture**: Single database, multiple schools with complete isolation
- **Modular Design**: Plug-and-play services for easy extensibility
- **Subdomain Routing**: Each school gets its own subdomain
- **Role-Based Access Control**: Configurable roles and permissions
- **Feature Flags**: Plan-based feature access control
- **Comprehensive API**: RESTful APIs with OpenAPI documentation
- **Production Ready**: Docker containerization, logging, monitoring

## 🏗️ Architecture

```
backend/
├── app/
│   ├── core/              # Configuration, security, database
│   ├── services/          # Business logic modules (plug-and-play)
│   │   ├── auth_service/
│   │   ├── school_service/
│   │   ├── student_service/
│   │   ├── fee_service/
│   │   ├── attendance_service/
│   │   ├── academic_service/
│   │   └── licensing_service/
│   ├── models/            # SQLAlchemy models
│   ├── schemas/           # Pydantic schemas
│   ├── api/               # API routes
│   ├── utils/             # Utilities and helpers
│   └── tests/             # Test modules
├── alembic/               # Database migrations
├── scripts/               # Utility scripts
└── requirements/          # Dependencies
```

## 🛠️ Tech Stack

- **Backend**: FastAPI + Python 3.11
- **Database**: PostgreSQL 15
- **Cache**: Redis 7
- **ORM**: SQLAlchemy 2.0
- **Migrations**: Alembic
- **Containerization**: Docker & Docker Compose
- **Testing**: Pytest
- **Documentation**: OpenAPI/Swagger

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Python 3.11+ (for local development)
- Git

### 1. Clone and Setup

```bash
git clone <repository-url>
cd school_saas
```

### 2. Environment Configuration

```bash
cp backend/.env.example backend/.env
# Edit backend/.env with your configuration
```

### 3. Start Services

```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f backend
```

### 4. Access Application

- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Database**: localhost:5432
- **Redis**: localhost:6379

## 📋 Development Workflow

### Local Development

```bash
# Install dependencies
cd backend
pip install -r requirements/dev.txt

# Run database migrations
alembic upgrade head

# Start development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest app/tests/test_auth.py -v
```

### Database Operations

```bash
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# Environment
ENVIRONMENT=development
DEBUG=true

# Database
POSTGRES_SERVER=localhost
POSTGRES_USER=school_erp_user
POSTGRES_PASSWORD=school_erp_password
POSTGRES_DB=school_erp_dev

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Domain (Development)
PRIMARY_DOMAIN=myschoolerp.local
```

## 📚 API Documentation

Once the application is running, visit:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🧪 Testing Strategy

- **Unit Tests**: Individual component testing
- **Integration Tests**: API endpoint testing
- **Database Tests**: Model and migration testing
- **Performance Tests**: Load and stress testing

## 🚀 Deployment

### Production Deployment

1. **Oracle Cloud Setup** (Coming Soon)
2. **Environment Configuration**
3. **SSL/TLS Setup with Cloudflare**
4. **Monitoring and Logging**

## 📖 Documentation

- [Implementation Checklist](docs/backend_implementation_checklist.md)
- [BRD Documents](BRD_DOCS/)
- [API Documentation](http://localhost:8000/docs)

## 🤝 Contributing

1. Follow the implementation checklist
2. Write tests for new features
3. Update documentation
4. Follow code style guidelines (Black, isort, flake8)

## 📄 License

This project is proprietary software for School ERP SaaS.

## 🆘 Support

For development support and questions, refer to the implementation checklist and BRD documents.

---

**Version**: 0.1.0  
**Status**: In Development  
**Last Updated**: 2024-07-28
