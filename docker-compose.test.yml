version: '3.8'

services:
  # PostgreSQL Test Database
  postgres-test:
    image: postgres:15-alpine
    container_name: school_erp_postgres_test
    environment:
      POSTGRES_USER: school_erp_user
      POSTGRES_PASSWORD: school_erp_password
      POSTGRES_DB: school_erp_test
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./scripts/init-test-db.sql:/docker-entrypoint-initdb.d/init-test-db.sql
    networks:
      - school_erp_test_network
    restart: "no"  # Don't restart automatically for tests
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U school_erp_user -d school_erp_test"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Redis Test Cache
  redis-test:
    image: redis:7-alpine
    container_name: school_erp_redis_test
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis_test_data:/data
      - ./config/redis-test.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - school_erp_test_network
    restart: "no"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # FastAPI Test Backend
  backend-test:
    build:
      context: ./backend
      dockerfile: Dockerfile.test
    container_name: school_erp_backend_test
    environment:
      - ENVIRONMENT=testing
      - DEBUG=true
      - POSTGRES_SERVER=postgres-test
      - POSTGRES_USER=school_erp_user
      - POSTGRES_PASSWORD=school_erp_password
      - POSTGRES_DB=school_erp_test
      - POSTGRES_PORT=5432
      - REDIS_HOST=redis-test
      - REDIS_PORT=6379
      - REDIS_DB=1  # Different DB for tests
    volumes:
      - ./backend:/app
      - backend_test_uploads:/app/uploads
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - school_erp_test_network
    restart: "no"
    command: pytest -v --cov=app --cov-report=html --cov-report=term

volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local
  backend_test_uploads:
    driver: local

networks:
  school_erp_test_network:
    driver: bridge
